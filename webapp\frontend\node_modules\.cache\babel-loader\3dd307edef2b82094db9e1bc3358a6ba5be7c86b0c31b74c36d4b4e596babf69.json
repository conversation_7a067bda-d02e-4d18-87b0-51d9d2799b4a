{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\CertificazioneCaviImproved.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, forwardRef, useImperativeHandle } from 'react';\nimport { Box, Typography, Button, Paper, Grid, Card, CardContent, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, Autocomplete, CircularProgress, Alert, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, IconButton, Tabs, Tab, Pagination, InputAdornment, Divider, Stack, Chip, Tooltip, LinearProgress, Collapse, Snackbar, Container } from '@mui/material';\nimport { Add as AddIcon, Search as SearchIcon, Clear as ClearIcon, CheckCircle as CheckIcon, Warning as WarningIcon, ExpandMore as ExpandMoreIcon, ExpandLess as ExpandLessIcon, Block as BlockIcon, Visibility as ViewIcon, PictureAsPdf as PdfIcon, Delete as DeleteIcon, Error as ErrorIcon, Cable as CableIcon, Build as BuildIcon, Link as LinkIcon, Download as DownloadIcon } from '@mui/icons-material';\nimport certificazioneService from '../../services/certificazioneService';\nimport caviService from '../../services/caviService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CertificazioneCaviImproved = /*#__PURE__*/_s(/*#__PURE__*/forwardRef(_c = _s(({\n  cantiereId,\n  onSuccess,\n  onError\n}, ref) => {\n  _s();\n  // Stati principali\n  const [loading, setLoading] = useState(false);\n  const [activeTab, setActiveTab] = useState(0);\n  const [certificazioni, setCertificazioni] = useState([]);\n  const [cavi, setCavi] = useState([]);\n  const [strumenti, setStrumenti] = useState([]);\n\n  // Stati per ricerca e filtri avanzati\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filteredCavi, setFilteredCavi] = useState([]);\n  const [filteredCertificazioni, setFilteredCertificazioni] = useState([]);\n  const [advancedFiltersOpen, setAdvancedFiltersOpen] = useState(false);\n  const [filters, setFilters] = useState({\n    stato: '',\n    tipologia: '',\n    operatore: '',\n    dataInizio: '',\n    dataFine: '',\n    valoreIsolamento: '',\n    risultatoTest: '',\n    strumento: '',\n    certificazione: ''\n  });\n\n  // Stati per paginazione e ordinamento\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage] = useState(10);\n  const [sortBy, setSortBy] = useState('id_cavo');\n  const [sortOrder, setSortOrder] = useState('asc'); // Ordine crescente di default per ID cavo\n\n  // Stati per dialogs e modali\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedItem, setSelectedItem] = useState(null);\n  const [bulkSelection, setBulkSelection] = useState([]);\n  const [bulkMode, setBulkMode] = useState(false);\n\n  // Stati per notifiche e feedback\n  const [snackbar, setSnackbar] = useState({\n    open: false,\n    message: '',\n    severity: 'info'\n  });\n  const [progress, setProgress] = useState(0);\n  const [operationInProgress, setOperationInProgress] = useState(false);\n\n  // Stati per form certificazione avanzato\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    id_operatore: '',\n    id_strumento: '',\n    lunghezza_misurata: '',\n    valore_continuita: 'OK',\n    valore_isolamento: '',\n    valore_resistenza: 'OK',\n    note: '',\n    temperatura_ambiente: '',\n    umidita: '',\n    tensione_prova: '',\n    durata_prova: '',\n    risultato_finale: 'CONFORME'\n  });\n\n  // Stati per statistiche e dashboard\n  const [statistics, setStatistics] = useState({\n    totaleCavi: 0,\n    caviCertificati: 0,\n    caviNonCertificati: 0,\n    percentualeCompletamento: 0,\n    certificazioniOggi: 0,\n    certificazioniSettimana: 0\n  });\n\n  // Carica dati iniziali\n  useEffect(() => {\n    loadInitialData();\n  }, [cantiereId]);\n\n  // Filtra cavi in base alla ricerca\n  useEffect(() => {\n    filterCavi();\n  }, [cavi, searchTerm, filters, sortBy, sortOrder]);\n\n  // Filtra certificazioni\n  useEffect(() => {\n    filterCertificazioni();\n  }, [certificazioni, searchTerm, filters, sortBy, sortOrder]);\n\n  // Ricalcola statistiche quando cambiano i dati\n  useEffect(() => {\n    calculateStatistics();\n  }, [cavi, certificazioni]);\n\n  // Riapplica filtri quando cambia il tab attivo E applica filtri iniziali\n  useEffect(() => {\n    if (activeTab === 0) {\n      filterCavi();\n    } else if (activeTab === 1) {\n      filterCertificazioni();\n    }\n  }, [activeTab, cavi, certificazioni]); // Aggiunto cavi e certificazioni come dipendenze\n\n  const loadInitialData = async () => {\n    try {\n      setLoading(true);\n      setProgress(0);\n\n      // Carica dati in sequenza con progress\n      setProgress(25);\n      const caviData = await loadCavi();\n      setProgress(50);\n      const certificazioniData = await loadCertificazioni();\n      setProgress(75);\n      await loadStrumenti();\n      setProgress(100);\n\n      // Calcola le statistiche usando i dati appena caricati\n      // invece di aspettare che gli stati si aggiornino\n      calculateStatisticsWithData(caviData, certificazioniData);\n    } catch (error) {\n      showSnackbar('Errore nel caricamento dei dati iniziali', 'error');\n      onError('Errore nel caricamento dei dati iniziali');\n    } finally {\n      setLoading(false);\n      setProgress(0);\n    }\n  };\n  const loadCertificazioni = async () => {\n    try {\n      const data = await certificazioneService.getCertificazioni(cantiereId);\n      setCertificazioni(data);\n      return data;\n    } catch (error) {\n      console.error('Errore nel caricamento delle certificazioni:', error);\n      throw error;\n    }\n  };\n  const loadCavi = async () => {\n    try {\n      const data = await caviService.getCavi(cantiereId);\n      // Ordina sempre i cavi per ID in ordine crescente (C001, C002, etc.)\n      const sortedData = data.sort((a, b) => {\n        // Estrai il numero dall'ID del cavo (es. C001 -> 1)\n        const getNumFromId = id => {\n          const match = id.match(/(\\d+)/);\n          return match ? parseInt(match[1], 10) : 0;\n        };\n        return getNumFromId(a.id_cavo) - getNumFromId(b.id_cavo);\n      });\n      setCavi(sortedData);\n      return sortedData;\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      throw error;\n    }\n  };\n  const loadStrumenti = async () => {\n    try {\n      const data = await certificazioneService.getStrumenti(cantiereId);\n      setStrumenti(data);\n      return data;\n    } catch (error) {\n      console.error('Errore nel caricamento degli strumenti:', error);\n      throw error;\n    }\n  };\n\n  // Funzione per verificare se un cavo può essere certificato\n  const puoEssereCertificato = useCallback(cavo => {\n    // Verifica che il cavo sia installato/posato\n    const isInstallato = cavo.stato_installazione === 'Installato' || cavo.stato_installazione === 'INSTALLATO' || cavo.stato_installazione === 'POSATO';\n\n    // Per la certificazione basta che sia posato\n    // Il collegamento può essere gestito durante la certificazione\n    return isInstallato;\n  }, []);\n\n  // Funzione per verificare se un cavo è completamente collegato\n  const isCavoCollegato = useCallback(cavo => {\n    const isCollegato = cavo.collegamenti === 3;\n    const hasResponsabili = cavo.responsabile_partenza && cavo.responsabile_arrivo;\n    return isCollegato && hasResponsabili;\n  }, []);\n\n  // Calcola statistiche avanzate usando i dati passati come parametri\n  const calculateStatisticsWithData = useCallback((caviData, certificazioniData) => {\n    if (!caviData || !certificazioniData) {\n      console.log('Dati mancanti per il calcolo delle statistiche:', {\n        caviData: !!caviData,\n        certificazioniData: !!certificazioniData\n      });\n      return;\n    }\n    console.log('Calcolo statistiche con dati:', {\n      cavi: caviData.length,\n      certificazioni: certificazioniData.length\n    });\n    const totaleCavi = caviData.length;\n    const caviCertificati = certificazioniData.length;\n    const caviNonCertificati = totaleCavi - caviCertificati;\n    const percentualeCompletamento = totaleCavi > 0 ? Math.round(caviCertificati / totaleCavi * 100) : 0;\n\n    // Calcola certificazioni di oggi\n    const oggi = new Date().toDateString();\n    const certificazioniOggi = certificazioniData.filter(cert => new Date(cert.data_certificazione).toDateString() === oggi).length;\n\n    // Calcola certificazioni della settimana\n    const unaSettimanaFa = new Date();\n    unaSettimanaFa.setDate(unaSettimanaFa.getDate() - 7);\n    const certificazioniSettimana = certificazioniData.filter(cert => new Date(cert.data_certificazione) >= unaSettimanaFa).length;\n\n    // Calcola cavi certificabili (solo posati/installati)\n    const caviCertificabili = caviData.filter(cavo => puoEssereCertificato(cavo)).length;\n    const caviNonCertificabili = totaleCavi - caviCertificabili;\n\n    // Calcola cavi collegati completamente\n    const caviCollegati = caviData.filter(cavo => isCavoCollegato(cavo)).length;\n    const newStatistics = {\n      totaleCavi,\n      caviCertificati,\n      caviNonCertificati,\n      caviCertificabili,\n      caviNonCertificabili,\n      caviCollegati,\n      percentualeCompletamento,\n      certificazioniOggi,\n      certificazioniSettimana\n    };\n    console.log('Nuove statistiche calcolate:', newStatistics);\n    setStatistics(newStatistics);\n  }, [puoEssereCertificato, isCavoCollegato]);\n\n  // Calcola statistiche avanzate usando gli stati correnti\n  const calculateStatistics = useCallback(() => {\n    if (!cavi || !certificazioni) {\n      console.log('Stati non pronti per il calcolo delle statistiche');\n      return;\n    }\n    calculateStatisticsWithData(cavi, certificazioni);\n  }, [cavi, certificazioni, calculateStatisticsWithData]);\n\n  // Gestione snackbar\n  const showSnackbar = (message, severity = 'info') => {\n    setSnackbar({\n      open: true,\n      message,\n      severity\n    });\n  };\n  const closeSnackbar = () => {\n    setSnackbar({\n      ...snackbar,\n      open: false\n    });\n  };\n  const filterCavi = () => {\n    let filtered = cavi;\n\n    // Filtro per ricerca testuale avanzata\n    if (searchTerm) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(cavo => {\n        var _cavo$tipologia, _cavo$ubicazione_part, _cavo$ubicazione_arri, _cavo$sezione, _cavo$utility;\n        return cavo.id_cavo.toLowerCase().includes(searchLower) || ((_cavo$tipologia = cavo.tipologia) === null || _cavo$tipologia === void 0 ? void 0 : _cavo$tipologia.toLowerCase().includes(searchLower)) || ((_cavo$ubicazione_part = cavo.ubicazione_partenza) === null || _cavo$ubicazione_part === void 0 ? void 0 : _cavo$ubicazione_part.toLowerCase().includes(searchLower)) || ((_cavo$ubicazione_arri = cavo.ubicazione_arrivo) === null || _cavo$ubicazione_arri === void 0 ? void 0 : _cavo$ubicazione_arri.toLowerCase().includes(searchLower)) || ((_cavo$sezione = cavo.sezione) === null || _cavo$sezione === void 0 ? void 0 : _cavo$sezione.toLowerCase().includes(searchLower)) || ((_cavo$utility = cavo.utility) === null || _cavo$utility === void 0 ? void 0 : _cavo$utility.toLowerCase().includes(searchLower));\n      });\n    }\n\n    // Filtri specifici avanzati\n    if (filters.stato) {\n      filtered = filtered.filter(cavo => cavo.stato_installazione === filters.stato);\n    }\n    if (filters.tipologia) {\n      filtered = filtered.filter(cavo => cavo.tipologia === filters.tipologia);\n    }\n\n    // Filtro per stato certificazione\n    if (filters.certificazione) {\n      if (filters.certificazione === 'CERTIFICATO') {\n        filtered = filtered.filter(cavo => certificazioni.some(cert => cert.id_cavo === cavo.id_cavo));\n      } else if (filters.certificazione === 'NON_CERTIFICATO') {\n        filtered = filtered.filter(cavo => !certificazioni.some(cert => cert.id_cavo === cavo.id_cavo));\n      }\n    }\n\n    // Ordinamento - speciale per ID cavo per mantenere ordine numerico\n    filtered.sort((a, b) => {\n      let aValue = a[sortBy];\n      let bValue = b[sortBy];\n\n      // Ordinamento speciale per ID cavo (C001, C002, etc.)\n      if (sortBy === 'id_cavo') {\n        const getNumFromId = id => {\n          const match = id.match(/(\\d+)/);\n          return match ? parseInt(match[1], 10) : 0;\n        };\n        const aNum = getNumFromId(aValue);\n        const bNum = getNumFromId(bValue);\n        if (sortOrder === 'asc') {\n          return aNum - bNum;\n        } else {\n          return bNum - aNum;\n        }\n      }\n\n      // Ordinamento normale per altri campi\n      if (typeof aValue === 'string') {\n        aValue = aValue.toLowerCase();\n        bValue = bValue.toLowerCase();\n      }\n      if (sortOrder === 'asc') {\n        return aValue > bValue ? 1 : -1;\n      } else {\n        return aValue < bValue ? 1 : -1;\n      }\n    });\n    setFilteredCavi(filtered);\n  };\n  const filterCertificazioni = () => {\n    let filtered = certificazioni;\n\n    // Ricerca testuale avanzata\n    if (searchTerm) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(cert => {\n        var _cert$operatore, _cert$numero_certific, _cert$note;\n        return cert.id_cavo.toLowerCase().includes(searchLower) || ((_cert$operatore = cert.operatore) === null || _cert$operatore === void 0 ? void 0 : _cert$operatore.toLowerCase().includes(searchLower)) || ((_cert$numero_certific = cert.numero_certificato) === null || _cert$numero_certific === void 0 ? void 0 : _cert$numero_certific.toLowerCase().includes(searchLower)) || ((_cert$note = cert.note) === null || _cert$note === void 0 ? void 0 : _cert$note.toLowerCase().includes(searchLower));\n      });\n    }\n\n    // Filtri avanzati\n    if (filters.operatore) {\n      filtered = filtered.filter(cert => cert.operatore === filters.operatore);\n    }\n    if (filters.strumento) {\n      filtered = filtered.filter(cert => cert.strumento === filters.strumento);\n    }\n    if (filters.risultatoTest) {\n      filtered = filtered.filter(cert => cert.risultato_finale === filters.risultatoTest);\n    }\n    if (filters.dataInizio) {\n      filtered = filtered.filter(cert => new Date(cert.data_certificazione) >= new Date(filters.dataInizio));\n    }\n    if (filters.dataFine) {\n      filtered = filtered.filter(cert => new Date(cert.data_certificazione) <= new Date(filters.dataFine));\n    }\n    if (filters.valoreIsolamento) {\n      const valore = parseFloat(filters.valoreIsolamento);\n      filtered = filtered.filter(cert => parseFloat(cert.valore_isolamento) >= valore);\n    }\n\n    // Ordinamento\n    filtered.sort((a, b) => {\n      let aValue = a[sortBy];\n      let bValue = b[sortBy];\n      if (sortBy === 'data_certificazione') {\n        aValue = new Date(aValue);\n        bValue = new Date(bValue);\n      } else if (typeof aValue === 'string') {\n        aValue = aValue.toLowerCase();\n        bValue = bValue.toLowerCase();\n      }\n      if (sortOrder === 'asc') {\n        return aValue > bValue ? 1 : -1;\n      } else {\n        return aValue < bValue ? 1 : -1;\n      }\n    });\n    setFilteredCertificazioni(filtered);\n  };\n\n  // Gestione selezione multipla - SOLO per certificazioni\n  const toggleBulkMode = () => {\n    if (activeTab !== 1) {\n      showSnackbar('La selezione multipla è disponibile solo per le certificazioni', 'warning');\n      return;\n    }\n    setBulkMode(!bulkMode);\n    setBulkSelection([]);\n    showSnackbar(!bulkMode ? 'Modalità selezione attivata - Clicca sulle certificazioni per selezionarle' : 'Modalità selezione disattivata', 'info');\n  };\n  const toggleItemSelection = itemId => {\n    setBulkSelection(prev => {\n      const newSelection = prev.includes(itemId) ? prev.filter(id => id !== itemId) : [...prev, itemId];\n      showSnackbar(`${newSelection.length} certificazioni selezionate`, 'info');\n      return newSelection;\n    });\n  };\n  const selectAllItems = () => {\n    if (activeTab !== 1) return;\n    const allIds = filteredCertificazioni.map(cert => cert.id_certificazione);\n    setBulkSelection(allIds);\n    showSnackbar(`Tutte le ${allIds.length} certificazioni selezionate`, 'success');\n  };\n  const clearSelection = () => {\n    setBulkSelection([]);\n    showSnackbar('Selezione cancellata', 'info');\n  };\n\n  // Funzione per verificare se un cavo è certificato\n  const isCavoCertificato = idCavo => {\n    return certificazioni.some(cert => cert.id_cavo === idCavo);\n  };\n\n  // Funzione per ottenere il messaggio di errore per cavi non certificabili\n  const getMessaggioErroreCertificazione = cavo => {\n    const isInstallato = cavo.stato_installazione === 'Installato' || cavo.stato_installazione === 'INSTALLATO' || cavo.stato_installazione === 'POSATO';\n    if (!isInstallato) {\n      return 'Il cavo deve essere posato/installato prima di poter essere certificato';\n    }\n    return 'Cavo non certificabile per motivi sconosciuti';\n  };\n\n  // Gestione tabs\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n    setCurrentPage(1);\n    setSearchTerm('');\n    setFilters({\n      stato: '',\n      tipologia: '',\n      operatore: ''\n    });\n  };\n\n  // Gestione dialogs\n  const openCreateDialog = (cavoPreselezionato = null) => {\n    setDialogType('create');\n    setSelectedItem(null);\n\n    // Se viene passato un cavo, precompila il form\n    if (cavoPreselezionato) {\n      setFormData({\n        id_cavo: cavoPreselezionato.id_cavo,\n        id_operatore: '',\n        id_strumento: '',\n        lunghezza_misurata: cavoPreselezionato.metratura_reale || cavoPreselezionato.metri_teorici || '',\n        valore_continuita: 'OK',\n        valore_isolamento: '',\n        valore_resistenza: 'OK',\n        note: '',\n        temperatura_ambiente: '',\n        umidita: '',\n        tensione_prova: '',\n        durata_prova: '',\n        risultato_finale: 'CONFORME'\n      });\n      showSnackbar(`Cavo ${cavoPreselezionato.id_cavo} selezionato automaticamente`, 'success');\n    } else {\n      // Reset form per nuova certificazione generica\n      setFormData({\n        id_cavo: '',\n        id_operatore: '',\n        id_strumento: '',\n        lunghezza_misurata: '',\n        valore_continuita: 'OK',\n        valore_isolamento: '',\n        valore_resistenza: 'OK',\n        note: '',\n        temperatura_ambiente: '',\n        umidita: '',\n        tensione_prova: '',\n        durata_prova: '',\n        risultato_finale: 'CONFORME'\n      });\n    }\n    setOpenDialog(true);\n  };\n  const closeDialog = () => {\n    setOpenDialog(false);\n    setSelectedItem(null);\n    setDialogType('');\n  };\n\n  // Gestione form\n  const handleFormChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const handleCavoSelect = cavo => {\n    setFormData(prev => ({\n      ...prev,\n      id_cavo: cavo.id_cavo,\n      lunghezza_misurata: cavo.metratura_reale || cavo.metri_teorici || ''\n    }));\n  };\n\n  // Funzione per collegare automaticamente un cavo\n  const collegaCavoAutomatico = async (cavoId, responsabile = 'cantiere') => {\n    try {\n      // Importa il servizio cavi\n      const caviService = (await import('../../services/caviService')).default;\n      let partenzaCollegata = false;\n      let arrivoCollegato = false;\n\n      // Prova a collegare il lato partenza\n      try {\n        await caviService.collegaCavo(cantiereId, cavoId, 'partenza', responsabile);\n        partenzaCollegata = true;\n        console.log('Lato partenza collegato con successo');\n      } catch (error) {\n        if (error.detail && error.detail.includes('già collegato')) {\n          partenzaCollegata = true;\n          console.log('Lato partenza già collegato');\n        } else {\n          console.error('Errore nel collegamento lato partenza:', error);\n          throw error;\n        }\n      }\n\n      // Prova a collegare il lato arrivo\n      try {\n        await caviService.collegaCavo(cantiereId, cavoId, 'arrivo', responsabile);\n        arrivoCollegato = true;\n        console.log('Lato arrivo collegato con successo');\n      } catch (error) {\n        if (error.detail && error.detail.includes('già collegato')) {\n          arrivoCollegato = true;\n          console.log('Lato arrivo già collegato');\n        } else {\n          console.error('Errore nel collegamento lato arrivo:', error);\n          throw error;\n        }\n      }\n      return partenzaCollegata && arrivoCollegato;\n    } catch (error) {\n      console.error('Errore nel collegamento automatico:', error);\n      throw error;\n    }\n  };\n\n  // Funzione per collegare un cavo direttamente dall'interfaccia di certificazione\n  const handleCollegaCavoFromCertification = async cavo => {\n    try {\n      const collegamenti = cavo.collegamenti || 0;\n      const statoCollegamenti = collegamenti === 0 ? 'Non collegato' : collegamenti === 1 ? 'Solo partenza collegata' : collegamenti === 2 ? 'Solo arrivo collegato' : 'Stato sconosciuto';\n      const conferma = window.confirm(`Collegamento automatico del cavo ${cavo.id_cavo}\\n\\n` + `Stato attuale: ${statoCollegamenti}\\n\\n` + `Il sistema collegherà automaticamente entrambi i lati del cavo a \"cantiere\".\\n\\n` + `Vuoi procedere?`);\n      if (!conferma) {\n        return;\n      }\n      setOperationInProgress(true);\n      showSnackbar('Collegamento automatico del cavo in corso...', 'info');\n      await collegaCavoAutomatico(cavo.id_cavo, 'cantiere');\n      showSnackbar(`Cavo ${cavo.id_cavo} collegato automaticamente su entrambi i lati`, 'success');\n\n      // Ricarica i cavi per aggiornare lo stato nell'interfaccia\n      await loadCavi();\n    } catch (error) {\n      console.error('Errore nel collegamento del cavo:', error);\n      showSnackbar('Errore nel collegamento automatico: ' + (error.message || 'Errore sconosciuto'), 'error');\n    } finally {\n      setOperationInProgress(false);\n    }\n  };\n\n  // Operazioni CRUD avanzate\n  const handleCreateCertificazione = async () => {\n    try {\n      if (!formData.id_cavo || !formData.id_operatore || !formData.id_strumento || !formData.valore_isolamento) {\n        showSnackbar('Compila tutti i campi obbligatori', 'warning');\n        return;\n      }\n\n      // Verifica che il cavo possa essere certificato\n      const cavo = cavi.find(c => c.id_cavo === formData.id_cavo);\n      if (!cavo) {\n        showSnackbar('Cavo non trovato', 'error');\n        return;\n      }\n      if (!puoEssereCertificato(cavo)) {\n        const messaggio = getMessaggioErroreCertificazione(cavo);\n        showSnackbar(`Impossibile certificare il cavo: ${messaggio}`, 'error');\n        return;\n      }\n\n      // Verifica che il cavo non sia già certificato\n      if (isCavoCertificato(formData.id_cavo)) {\n        showSnackbar('Il cavo è già stato certificato', 'warning');\n        return;\n      }\n\n      // Verifica se il cavo è collegato, altrimenti collega automaticamente\n      if (!isCavoCollegato(cavo)) {\n        const statoCollegamenti = cavo.collegamenti === 0 ? 'Non collegato' : cavo.collegamenti === 1 ? 'Solo partenza collegata' : cavo.collegamenti === 2 ? 'Solo arrivo collegato' : 'Stato sconosciuto';\n        const conferma = window.confirm(`ATTENZIONE: Il cavo ${cavo.id_cavo} non risulta completamente collegato.\\n\\n` + `Stato collegamenti: ${statoCollegamenti}\\n\\n` + `Vuoi collegare automaticamente entrambi i lati del cavo e procedere con la certificazione?\\n\\n` + `(Il sistema collegherà automaticamente il cavo a \"cantiere\" su entrambi i lati)`);\n        if (!conferma) {\n          return;\n        }\n\n        // Collega automaticamente il cavo\n        try {\n          setOperationInProgress(true);\n          showSnackbar('Collegamento automatico del cavo in corso...', 'info');\n          await collegaCavoAutomatico(cavo.id_cavo, 'cantiere');\n          showSnackbar('Cavo collegato automaticamente su entrambi i lati', 'success');\n\n          // Ricarica i cavi per aggiornare lo stato\n          await loadCavi();\n        } catch (error) {\n          showSnackbar('Errore nel collegamento automatico: ' + (error.message || 'Errore sconosciuto'), 'error');\n          return;\n        }\n      }\n      setOperationInProgress(true);\n      await certificazioneService.createCertificazione(cantiereId, formData);\n      showSnackbar('Certificazione creata con successo', 'success');\n      closeDialog();\n      await loadCertificazioni();\n      calculateStatistics();\n    } catch (error) {\n      showSnackbar('Errore nella creazione della certificazione: ' + (error.message || 'Errore sconosciuto'), 'error');\n    } finally {\n      setOperationInProgress(false);\n    }\n  };\n  const handleGeneratePdf = async certificazione => {\n    try {\n      setOperationInProgress(true);\n      showSnackbar('Generazione PDF in corso...', 'info');\n      console.log(`🔍 DEBUG PDF - Generando PDF per certificazione:`, {\n        id_certificazione: certificazione.id_certificazione,\n        numero_certificato: certificazione.numero_certificato,\n        id_cavo: certificazione.id_cavo,\n        cantiere_id: cantiereId\n      });\n      const response = await certificazioneService.generatePdf(cantiereId, certificazione.id_certificazione);\n      if (response.file_url) {\n        // Apri il PDF in una nuova finestra\n        const newWindow = window.open(response.file_url, '_blank');\n        if (newWindow) {\n          showSnackbar('PDF generato e aperto in una nuova finestra', 'success');\n        } else {\n          // Se il popup è bloccato, offri il download diretto\n          const link = document.createElement('a');\n          link.href = response.file_url;\n          link.download = `certificazione_${certificazione.numero_certificato}.pdf`;\n          document.body.appendChild(link);\n          link.click();\n          document.body.removeChild(link);\n          showSnackbar('PDF scaricato nella cartella Download', 'success');\n        }\n      } else if (response.pdf_content) {\n        // Se il PDF viene restituito come contenuto base64\n        const blob = new Blob([atob(response.pdf_content)], {\n          type: 'application/pdf'\n        });\n        const url = URL.createObjectURL(blob);\n        const link = document.createElement('a');\n        link.href = url;\n        link.download = `certificazione_${certificazione.numero_certificato}.pdf`;\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        URL.revokeObjectURL(url);\n        showSnackbar('PDF scaricato con successo', 'success');\n      } else {\n        showSnackbar('Errore: Formato PDF non riconosciuto', 'error');\n      }\n    } catch (error) {\n      console.error('Errore generazione PDF:', error);\n      showSnackbar('Errore nella generazione del PDF: ' + (error.message || 'Errore sconosciuto'), 'error');\n    } finally {\n      setOperationInProgress(false);\n    }\n  };\n  const handleDeleteCertificazione = async certificazione => {\n    if (window.confirm(`Sei sicuro di voler eliminare la certificazione ${certificazione.numero_certificato}?`)) {\n      try {\n        setOperationInProgress(true);\n        await certificazioneService.deleteCertificazione(cantiereId, certificazione.id_certificazione);\n        showSnackbar('Certificazione eliminata con successo', 'success');\n        await loadCertificazioni();\n        calculateStatistics();\n      } catch (error) {\n        showSnackbar('Errore nell\\'eliminazione della certificazione: ' + (error.message || 'Errore sconosciuto'), 'error');\n      } finally {\n        setOperationInProgress(false);\n      }\n    }\n  };\n\n  // Operazioni bulk\n  const handleBulkDelete = async () => {\n    if (bulkSelection.length === 0) {\n      showSnackbar('Seleziona almeno un elemento', 'warning');\n      return;\n    }\n    if (window.confirm(`Sei sicuro di voler eliminare ${bulkSelection.length} certificazioni?`)) {\n      try {\n        setOperationInProgress(true);\n        for (const id of bulkSelection) {\n          await certificazioneService.deleteCertificazione(cantiereId, id);\n        }\n        showSnackbar(`${bulkSelection.length} certificazioni eliminate con successo`, 'success');\n        setBulkSelection([]);\n        await loadCertificazioni();\n        calculateStatistics();\n      } catch (error) {\n        showSnackbar('Errore nell\\'eliminazione delle certificazioni', 'error');\n      } finally {\n        setOperationInProgress(false);\n      }\n    }\n  };\n  const handleBulkExport = async () => {\n    if (bulkSelection.length === 0) {\n      showSnackbar('Seleziona almeno un elemento', 'warning');\n      return;\n    }\n    try {\n      setOperationInProgress(true);\n      // Implementa export bulk\n      const selectedCerts = certificazioni.filter(cert => bulkSelection.includes(cert.id_certificazione));\n\n      // Crea CSV\n      const csvContent = generateCSV(selectedCerts);\n      downloadCSV(csvContent, `certificazioni_${new Date().toISOString().split('T')[0]}.csv`);\n      showSnackbar(`${bulkSelection.length} certificazioni esportate`, 'success');\n    } catch (error) {\n      showSnackbar('Errore nell\\'esportazione', 'error');\n    } finally {\n      setOperationInProgress(false);\n    }\n  };\n\n  // Funzioni di export\n  const generateCSV = data => {\n    const headers = ['ID Cavo', 'Numero Certificato', 'Data', 'Operatore', 'Strumento', 'Lunghezza', 'Isolamento', 'Risultato'];\n    const rows = data.map(cert => [cert.id_cavo, cert.numero_certificato, new Date(cert.data_certificazione).toLocaleDateString(), cert.operatore, cert.strumento, cert.lunghezza_misurata, cert.valore_isolamento, cert.risultato_finale]);\n    return [headers, ...rows].map(row => row.join(',')).join('\\n');\n  };\n  const downloadCSV = (content, filename) => {\n    const blob = new Blob([content], {\n      type: 'text/csv;charset=utf-8;'\n    });\n    const link = document.createElement('a');\n    if (link.download !== undefined) {\n      const url = URL.createObjectURL(blob);\n      link.setAttribute('href', url);\n      link.setAttribute('download', filename);\n      link.style.visibility = 'hidden';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n    }\n  };\n  const handleExportAll = () => {\n    const csvContent = generateCSV(filteredCertificazioni);\n    downloadCSV(csvContent, `tutte_certificazioni_${new Date().toISOString().split('T')[0]}.csv`);\n    showSnackbar('Esportazione completata', 'success');\n  };\n\n  // Espone metodi tramite ref\n  useImperativeHandle(ref, () => ({\n    handleOptionSelect: option => {\n      if (option === 'creaCertificazione') {\n        openCreateDialog();\n      } else if (option === 'visualizzaCertificazioni') {\n        setActiveTab(1);\n      }\n    },\n    createCertificationForCavo: cavo => {\n      openCreateDialog(cavo);\n    },\n    viewCertificationForCavo: cavo => {\n      // Trova la certificazione per questo cavo\n      const certificazione = certificazioni.find(cert => cert.id_cavo === cavo.id_cavo);\n      if (certificazione) {\n        setSelectedItem(certificazione);\n        setDialogType('view');\n        setOpenDialog(true);\n      } else {\n        showSnackbar('Certificazione non trovata per questo cavo', 'error');\n      }\n    },\n    generatePdfForCavo: cavo => {\n      // Trova la certificazione per questo cavo e genera il PDF\n      console.log(`🔍 DEBUG PDF - Cercando certificazione per cavo ${cavo.id_cavo}`);\n      console.log(`🔍 DEBUG PDF - Certificazioni disponibili:`, certificazioni.map(c => ({\n        id: c.id_certificazione,\n        cavo: c.id_cavo\n      })));\n      const certificazione = certificazioni.find(cert => cert.id_cavo === cavo.id_cavo);\n      if (certificazione) {\n        console.log(`🔍 DEBUG PDF - Certificazione trovata:`, certificazione);\n        handleGeneratePdf(certificazione);\n      } else {\n        console.log(`❌ DEBUG PDF - Nessuna certificazione trovata per cavo ${cavo.id_cavo}`);\n        showSnackbar('Certificazione non trovata per questo cavo', 'error');\n      }\n    }\n  }));\n\n  // Calcola elementi per paginazione\n  const getCurrentPageItems = items => {\n    const startIndex = (currentPage - 1) * itemsPerPage;\n    const endIndex = startIndex + itemsPerPage;\n    return items.slice(startIndex, endIndex);\n  };\n  const getTotalPages = items => Math.ceil(items.length / itemsPerPage);\n\n  // Dashboard minimal con statistiche essenziali\n  const renderDashboard = () => /*#__PURE__*/_jsxDEV(Paper, {\n    sx: {\n      p: 2,\n      mb: 3,\n      bgcolor: 'grey.50'\n    },\n    children: /*#__PURE__*/_jsxDEV(Stack, {\n      direction: \"row\",\n      spacing: 4,\n      alignItems: \"center\",\n      justifyContent: \"space-between\",\n      flexWrap: \"wrap\",\n      children: [/*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        alignItems: \"center\",\n        spacing: 1,\n        children: [/*#__PURE__*/_jsxDEV(CableIcon, {\n          color: \"primary\",\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 964,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            fontWeight: \"bold\",\n            sx: {\n              lineHeight: 1\n            },\n            children: statistics.totaleCavi\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 966,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"Totale\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 969,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 965,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 963,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        alignItems: \"center\",\n        spacing: 1,\n        children: [/*#__PURE__*/_jsxDEV(CheckIcon, {\n          color: \"success\",\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 976,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            fontWeight: \"bold\",\n            sx: {\n              lineHeight: 1\n            },\n            children: statistics.caviCertificati\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 978,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"Certificati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 981,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 977,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 975,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        alignItems: \"center\",\n        spacing: 1,\n        children: [/*#__PURE__*/_jsxDEV(BuildIcon, {\n          color: \"info\",\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 988,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            fontWeight: \"bold\",\n            sx: {\n              lineHeight: 1\n            },\n            children: statistics.caviCertificabili\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 990,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"Pronti\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 993,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 989,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 987,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        alignItems: \"center\",\n        spacing: 1,\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: 32,\n            height: 32,\n            borderRadius: '50%',\n            bgcolor: statistics.percentualeCompletamento >= 80 ? 'success.main' : statistics.percentualeCompletamento >= 50 ? 'warning.main' : 'error.main',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            fontWeight: \"bold\",\n            color: \"white\",\n            children: [statistics.percentualeCompletamento, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1010,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1000,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            fontWeight: \"medium\",\n            sx: {\n              lineHeight: 1\n            },\n            children: \"Completamento\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1015,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: [statistics.certificazioniOggi, \" oggi\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1018,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1014,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 999,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 961,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 960,\n    columnNumber: 5\n  }, this);\n\n  // Componente barra di ricerca avanzata\n  const renderSearchAndFilters = () => /*#__PURE__*/_jsxDEV(Paper, {\n    sx: {\n      p: 2,\n      mb: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 2,\n      alignItems: \"center\",\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          placeholder: \"Cerca cavi, certificazioni, operatori...\",\n          value: searchTerm,\n          onChange: e => setSearchTerm(e.target.value),\n          InputProps: {\n            startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"start\",\n              children: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1042,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1041,\n              columnNumber: 17\n            }, this),\n            endAdornment: searchTerm && /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"end\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: () => setSearchTerm(''),\n                size: \"small\",\n                children: /*#__PURE__*/_jsxDEV(ClearIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1048,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1047,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1046,\n              columnNumber: 17\n            }, this)\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1034,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1033,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 2,\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          fullWidth: true,\n          variant: \"outlined\",\n          onClick: () => setAdvancedFiltersOpen(!advancedFiltersOpen),\n          color: Object.values(filters).some(f => f) ? 'primary' : 'inherit',\n          children: [\"Filtri \", Object.values(filters).filter(f => f).length > 0 && `(${Object.values(filters).filter(f => f).length})`]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1057,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1056,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 2,\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          fullWidth: true,\n          variant: \"outlined\",\n          onClick: toggleBulkMode,\n          color: bulkMode ? 'secondary' : 'inherit',\n          disabled: activeTab === 0,\n          children: bulkMode ? 'Esci Selezione' : 'Selezione Multipla'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1068,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1067,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 2,\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          fullWidth: true,\n          variant: \"outlined\",\n          onClick: handleExportAll,\n          disabled: activeTab === 0 || filteredCertificazioni.length === 0,\n          children: activeTab === 0 ? 'Export (solo certificazioni)' : 'Esporta Certificazioni'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1080,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1079,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 2,\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          fullWidth: true,\n          variant: \"contained\",\n          onClick: openCreateDialog,\n          children: \"Nuova Certificazione\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1091,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1090,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1032,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n      in: advancedFiltersOpen,\n      children: [/*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          my: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1103,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"text.secondary\",\n        sx: {\n          mb: 2\n        },\n        children: activeTab === 0 ? 'Filtri per Cavi' : 'Filtri per Certificazioni'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1104,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: [activeTab === 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Stato Installazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1114,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: filters.stato,\n                onChange: e => setFilters({\n                  ...filters,\n                  stato: e.target.value\n                }),\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: \"Tutti\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1119,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"INSTALLATO\",\n                  children: \"Installato\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1120,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"NON_INSTALLATO\",\n                  children: \"Non Installato\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1121,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"IN_CORSO\",\n                  children: \"In Corso\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1122,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1115,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1113,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1112,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1129,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: filters.tipologia,\n                onChange: e => setFilters({\n                  ...filters,\n                  tipologia: e.target.value\n                }),\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: \"Tutte\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1134,\n                  columnNumber: 21\n                }, this), [...new Set(cavi.map(c => c.tipologia))].filter(Boolean).map(tip => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: tip,\n                  children: tip\n                }, tip, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1136,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1130,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1128,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1127,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Stato Certificazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1144,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: filters.certificazione,\n                onChange: e => setFilters({\n                  ...filters,\n                  certificazione: e.target.value\n                }),\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: \"Tutti\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1149,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"CERTIFICATO\",\n                  children: \"Certificato\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1150,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"NON_CERTIFICATO\",\n                  children: \"Non Certificato\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1151,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1145,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1143,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1142,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true), activeTab === 1 && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Operatore\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1163,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: filters.operatore,\n                onChange: e => setFilters({\n                  ...filters,\n                  operatore: e.target.value\n                }),\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: \"Tutti\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1168,\n                  columnNumber: 21\n                }, this), [...new Set(certificazioni.map(c => c.operatore))].filter(Boolean).map(op => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: op,\n                  children: op\n                }, op, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1170,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1164,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1162,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1161,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Risultato Test\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1178,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: filters.risultatoTest,\n                onChange: e => setFilters({\n                  ...filters,\n                  risultatoTest: e.target.value\n                }),\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: \"Tutti\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1183,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"CONFORME\",\n                  children: \"Conforme\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1184,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"NON_CONFORME\",\n                  children: \"Non Conforme\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1185,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"DA_VERIFICARE\",\n                  children: \"Da Verificare\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1186,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1179,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1177,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1176,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              size: \"small\",\n              label: \"Isolamento Min (M\\u03A9)\",\n              type: \"number\",\n              value: filters.valoreIsolamento,\n              onChange: e => setFilters({\n                ...filters,\n                valoreIsolamento: e.target.value\n              }),\n              placeholder: \"es. 500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1192,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1191,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              size: \"small\",\n              label: \"Data Inizio\",\n              type: \"date\",\n              value: filters.dataInizio,\n              onChange: e => setFilters({\n                ...filters,\n                dataInizio: e.target.value\n              }),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1204,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1203,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              size: \"small\",\n              label: \"Data Fine\",\n              type: \"date\",\n              value: filters.dataFine,\n              onChange: e => setFilters({\n                ...filters,\n                dataFine: e.target.value\n              }),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1216,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1215,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            spacing: 1,\n            justifyContent: \"flex-end\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              size: \"small\",\n              onClick: () => setFilters({\n                stato: '',\n                tipologia: '',\n                operatore: '',\n                dataInizio: '',\n                dataFine: '',\n                valoreIsolamento: '',\n                risultatoTest: '',\n                strumento: '',\n                certificazione: ''\n              }),\n              children: \"Pulisci Tutti i Filtri\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1231,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1230,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1229,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1108,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1102,\n      columnNumber: 7\n    }, this), bulkMode && bulkSelection.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          my: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1250,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        spacing: 2,\n        alignItems: \"center\",\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: [bulkSelection.length, \" elementi selezionati\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1252,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          variant: \"outlined\",\n          onClick: selectAllItems,\n          children: \"Seleziona Tutto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1255,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          variant: \"outlined\",\n          onClick: clearSelection,\n          children: \"Deseleziona\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1262,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          variant: \"outlined\",\n          onClick: handleBulkExport,\n          children: \"Esporta Selezionati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1269,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          variant: \"outlined\",\n          color: \"error\",\n          onClick: handleBulkDelete,\n          children: \"Elimina Selezionati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1276,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1251,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1031,\n    columnNumber: 5\n  }, this);\n\n  // Renderizza la tabella dei cavi\n  const renderCaviTable = () => {\n    const currentItems = getCurrentPageItems(filteredCavi);\n    if (filteredCavi.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        children: searchTerm || filters.stato || filters.tipologia ? 'Nessun cavo trovato con i filtri applicati' : 'Nessun cavo disponibile'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1296,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  alignItems: \"center\",\n                  spacing: 1,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"bold\",\n                    children: \"ID Cavo\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1312,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: () => {\n                      setSortBy('id_cavo');\n                      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\n                    },\n                    children: sortBy === 'id_cavo' ? sortOrder === 'asc' ? /*#__PURE__*/_jsxDEV(ExpandLessIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1317,\n                      columnNumber: 70\n                    }, this) : /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1317,\n                      columnNumber: 91\n                    }, this) : /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1317,\n                      columnNumber: 113\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1313,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1311,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1310,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1321,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Sezione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1322,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Partenza\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1323,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Arrivo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1324,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Metri\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1325,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1326,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Collegamenti\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1327,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Certificato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1328,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Azioni\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1329,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1309,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1308,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: currentItems.map(cavo => {\n              const isCertificato = isCavoCertificato(cavo.id_cavo);\n              const puoCertificare = puoEssereCertificato(cavo);\n              const messaggioErrore = !puoCertificare ? getMessaggioErroreCertificazione(cavo) : '';\n              return /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"medium\",\n                    children: cavo.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1341,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1340,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.tipologia\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1345,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.sezione\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1346,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.ubicazione_partenza\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1347,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.ubicazione_arrivo\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1348,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: [cavo.metratura_reale || cavo.metri_teorici, \" m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1349,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: cavo.stato_installazione,\n                    color: cavo.stato_installazione === 'INSTALLATO' ? 'success' : 'default'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1351,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1350,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: (() => {\n                    const collegamenti = cavo.collegamenti || 0;\n                    const statoCollegamento = collegamenti === 0 ? 'Non collegato' : collegamenti === 1 ? 'Solo partenza' : collegamenti === 2 ? 'Solo arrivo' : collegamenti === 3 ? 'Completo' : 'Sconosciuto';\n                    const colore = collegamenti === 3 ? 'success' : collegamenti === 0 ? 'error' : 'warning';\n                    return /*#__PURE__*/_jsxDEV(Tooltip, {\n                      title: `Partenza: ${cavo.responsabile_partenza || 'Non collegato'} | Arrivo: ${cavo.responsabile_arrivo || 'Non collegato'}`,\n                      children: /*#__PURE__*/_jsxDEV(Chip, {\n                        size: \"small\",\n                        label: statoCollegamento,\n                        color: colore,\n                        icon: collegamenti === 3 ? /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1374,\n                          columnNumber: 58\n                        }, this) : /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1374,\n                          columnNumber: 74\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1370,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1369,\n                      columnNumber: 27\n                    }, this);\n                  })()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1357,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: isCertificato ? /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    icon: /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1384,\n                      columnNumber: 33\n                    }, this),\n                    label: \"Certificato\",\n                    color: \"success\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1382,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    icon: /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1391,\n                      columnNumber: 33\n                    }, this),\n                    label: \"Non certificato\",\n                    color: \"warning\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1389,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1380,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: isCertificato ? /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Cavo gi\\xE0 certificato\",\n                    children: /*#__PURE__*/_jsxDEV(Chip, {\n                      icon: /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1401,\n                        columnNumber: 35\n                      }, this),\n                      label: \"Certificato\",\n                      color: \"success\",\n                      size: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1400,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1399,\n                    columnNumber: 25\n                  }, this) : puoCertificare ? /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Crea certificazione per questo cavo\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => openCreateDialog(cavo),\n                      color: \"primary\",\n                      children: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1414,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1409,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1408,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: messaggioErrore,\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: /*#__PURE__*/_jsxDEV(IconButton, {\n                        size: \"small\",\n                        disabled: true,\n                        onClick: () => showSnackbar(messaggioErrore, 'warning'),\n                        children: /*#__PURE__*/_jsxDEV(BlockIcon, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1425,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1420,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1419,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1418,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1397,\n                  columnNumber: 21\n                }, this)]\n              }, cavo.id_cavo, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1339,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1332,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1307,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1306,\n        columnNumber: 9\n      }, this), getTotalPages(filteredCavi) > 1 && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          mt: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Pagination, {\n          count: getTotalPages(filteredCavi),\n          page: currentPage,\n          onChange: (event, value) => setCurrentPage(value),\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1440,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1439,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true);\n  };\n\n  // Renderizza la tabella delle certificazioni\n  const renderCertificazioniTable = () => {\n    const currentItems = getCurrentPageItems(filteredCertificazioni);\n    if (filteredCertificazioni.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        children: searchTerm || filters.operatore ? 'Nessuna certificazione trovata con i filtri applicati' : 'Nessuna certificazione disponibile'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1458,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [bulkMode && /*#__PURE__*/_jsxDEV(TableCell, {\n                padding: \"checkbox\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: bulkSelection.length === filteredCertificazioni.length ? clearSelection : selectAllItems,\n                  children: bulkSelection.length === filteredCertificazioni.length ? /*#__PURE__*/_jsxDEV(ClearIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1478,\n                    columnNumber: 81\n                  }, this) : /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1478,\n                    columnNumber: 97\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1474,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1473,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  alignItems: \"center\",\n                  spacing: 1,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"bold\",\n                    children: \"N\\xB0 Certificato\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1484,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: () => {\n                      setSortBy('numero_certificato');\n                      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\n                    },\n                    children: sortBy === 'numero_certificato' ? sortOrder === 'asc' ? /*#__PURE__*/_jsxDEV(ExpandLessIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1489,\n                      columnNumber: 81\n                    }, this) : /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1489,\n                      columnNumber: 102\n                    }, this) : /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1489,\n                      columnNumber: 124\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1485,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1483,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1482,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"ID Cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1493,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  alignItems: \"center\",\n                  spacing: 1,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"bold\",\n                    children: \"Data\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1496,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: () => {\n                      setSortBy('data_certificazione');\n                      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\n                    },\n                    children: sortBy === 'data_certificazione' ? sortOrder === 'asc' ? /*#__PURE__*/_jsxDEV(ExpandLessIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1501,\n                      columnNumber: 82\n                    }, this) : /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1501,\n                      columnNumber: 103\n                    }, this) : /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1501,\n                      columnNumber: 125\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1497,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1495,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1494,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Operatore\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1505,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Strumento\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1506,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Lunghezza\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1507,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Isolamento\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1508,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Risultato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1509,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Azioni\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1510,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1471,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1470,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: currentItems.map(cert => /*#__PURE__*/_jsxDEV(TableRow, {\n              selected: bulkSelection.includes(cert.id_certificazione),\n              hover: true,\n              children: [bulkMode && /*#__PURE__*/_jsxDEV(TableCell, {\n                padding: \"checkbox\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: () => toggleItemSelection(cert.id_certificazione),\n                  color: bulkSelection.includes(cert.id_certificazione) ? 'primary' : 'default',\n                  children: bulkSelection.includes(cert.id_certificazione) ? /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1527,\n                    columnNumber: 75\n                  }, this) : /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1527,\n                    columnNumber: 91\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1522,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1521,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  fontWeight: \"medium\",\n                  children: cert.numero_certificato\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1532,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1531,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  size: \"small\",\n                  label: cert.id_cavo,\n                  variant: \"outlined\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1537,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1536,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: new Date(cert.data_certificazione).toLocaleDateString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1539,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: cert.operatore || cert.id_operatore\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1541,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1540,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: cert.id_strumento ? (() => {\n                    const strumento = strumenti.find(s => s.id_strumento === cert.id_strumento);\n                    return strumento ? `${strumento.nome} - ${strumento.marca}` : 'Strumento non trovato';\n                  })() : cert.strumento_utilizzato || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1544,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1543,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: [cert.lunghezza_misurata, \" m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1555,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1554,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  size: \"small\",\n                  label: `${cert.valore_isolamento} MΩ`,\n                  color: parseFloat(cert.valore_isolamento) >= 500 ? 'success' : 'warning',\n                  icon: parseFloat(cert.valore_isolamento) >= 500 ? /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1562,\n                    columnNumber: 73\n                  }, this) : /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1562,\n                    columnNumber: 89\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1558,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1557,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  size: \"small\",\n                  label: cert.risultato_finale || 'CONFORME',\n                  color: cert.risultato_finale === 'CONFORME' ? 'success' : cert.risultato_finale === 'NON_CONFORME' ? 'error' : 'warning'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1566,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1565,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  spacing: 0.5,\n                  children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Visualizza dettagli\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => {\n                        setSelectedItem(cert);\n                        setDialogType('view');\n                        setOpenDialog(true);\n                      },\n                      children: /*#__PURE__*/_jsxDEV(ViewIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1583,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1575,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1574,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Genera PDF\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => handleGeneratePdf(cert),\n                      disabled: operationInProgress,\n                      children: /*#__PURE__*/_jsxDEV(PdfIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1592,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1587,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1586,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Elimina\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      color: \"error\",\n                      onClick: () => handleDeleteCertificazione(cert),\n                      disabled: operationInProgress,\n                      children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1602,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1596,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1595,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1573,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1572,\n                columnNumber: 19\n              }, this)]\n            }, cert.id_certificazione, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1515,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1513,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1469,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1468,\n        columnNumber: 9\n      }, this), getTotalPages(filteredCertificazioni) > 1 && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          mt: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Pagination, {\n          count: getTotalPages(filteredCertificazioni),\n          page: currentPage,\n          onChange: (event, value) => setCurrentPage(value),\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1615,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1614,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true);\n  };\n\n  // Renderizza il dialog per creare/modificare certificazione\n  const renderCertificazioneDialog = () => {\n    if (dialogType !== 'create' && dialogType !== 'edit') return null;\n    return /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openDialog,\n      onClose: closeDialog,\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: dialogType === 'create' ? 'Nuova Certificazione' : 'Modifica Certificazione'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1633,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          sx: {\n            mt: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Autocomplete, {\n              options: cavi.filter(cavo => {\n                // Mostra solo cavi che possono essere certificati o quello già selezionato\n                const isSelected = cavo.id_cavo === formData.id_cavo;\n                const isNotCertified = !certificazioni.some(cert => cert.id_cavo === cavo.id_cavo);\n                const canBeCertified = puoEssereCertificato(cavo);\n                return isSelected || isNotCertified && canBeCertified;\n              }),\n              getOptionLabel: option => `${option.id_cavo} - ${option.tipologia}`,\n              value: cavi.find(c => c.id_cavo === formData.id_cavo) || null,\n              onChange: (event, newValue) => {\n                if (newValue) {\n                  handleCavoSelect(newValue);\n                } else {\n                  setFormData(prev => ({\n                    ...prev,\n                    id_cavo: '',\n                    lunghezza_misurata: ''\n                  }));\n                }\n              },\n              renderInput: params => /*#__PURE__*/_jsxDEV(TextField, {\n                ...params,\n                label: \"Cavo *\",\n                placeholder: \"Seleziona un cavo posato\",\n                required: true,\n                helperText: \"Solo cavi posati/installati (il collegamento pu\\xF2 essere gestito al momento)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1658,\n                columnNumber: 19\n              }, this),\n              renderOption: (props, option) => {\n                const collegamenti = option.collegamenti || 0;\n                const isCollegato = collegamenti === 3;\n                return /*#__PURE__*/_jsxDEV(Box, {\n                  component: \"li\",\n                  ...props,\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      width: '100%'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Stack, {\n                      direction: \"row\",\n                      justifyContent: \"space-between\",\n                      alignItems: \"center\",\n                      children: [/*#__PURE__*/_jsxDEV(Box, {\n                        children: [/*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          fontWeight: \"medium\",\n                          children: option.id_cavo\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1675,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"caption\",\n                          color: \"text.secondary\",\n                          children: [option.tipologia, \" - \", option.ubicazione_partenza, \" \\u2192 \", option.ubicazione_arrivo]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1678,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1674,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n                        direction: \"row\",\n                        spacing: 1,\n                        children: [/*#__PURE__*/_jsxDEV(Chip, {\n                          size: \"small\",\n                          label: option.stato_installazione,\n                          color: option.stato_installazione === 'INSTALLATO' ? 'success' : 'default'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1683,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                          size: \"small\",\n                          label: isCollegato ? 'Collegato' : 'Da collegare',\n                          color: isCollegato ? 'success' : 'warning',\n                          icon: isCollegato ? /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1692,\n                            columnNumber: 51\n                          }, this) : /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1692,\n                            columnNumber: 67\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1688,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1682,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1673,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1672,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1671,\n                  columnNumber: 21\n                }, this);\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1639,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1638,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Operatore *\",\n              value: formData.id_operatore,\n              onChange: e => handleFormChange('id_operatore', e.target.value),\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1704,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1703,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              required: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Strumento *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1715,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: formData.id_strumento,\n                onChange: e => handleFormChange('id_strumento', e.target.value),\n                label: \"Strumento *\",\n                children: strumenti.map(strumento => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: strumento.id_strumento,\n                  children: [strumento.nome, \" - \", strumento.marca, \" \", strumento.modello, \" (S/N: \", strumento.numero_serie, \")\"]\n                }, strumento.id_strumento, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1722,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1716,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1714,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1713,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Lunghezza Misurata (m) *\",\n              type: \"number\",\n              value: formData.lunghezza_misurata,\n              onChange: e => handleFormChange('lunghezza_misurata', e.target.value),\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1731,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1730,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Continuit\\xE0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1743,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: formData.valore_continuita,\n                onChange: e => handleFormChange('valore_continuita', e.target.value),\n                label: \"Continuit\\xE0\",\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"OK\",\n                  children: \"OK\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1749,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"NOK\",\n                  children: \"NOK\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1750,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1744,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1742,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1741,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Isolamento (M\\u03A9) *\",\n              type: \"number\",\n              value: formData.valore_isolamento,\n              onChange: e => handleFormChange('valore_isolamento', e.target.value),\n              required: true,\n              helperText: \"Valore minimo consigliato: 500 M\\u03A9\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1756,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1755,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Resistenza\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1769,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: formData.valore_resistenza,\n                onChange: e => handleFormChange('valore_resistenza', e.target.value),\n                label: \"Resistenza\",\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"OK\",\n                  children: \"OK\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1775,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"NOK\",\n                  children: \"NOK\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1776,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1770,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1768,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1767,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Divider, {\n              sx: {\n                my: 2\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"Stato Collegamenti Cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1784,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1783,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1782,\n            columnNumber: 13\n          }, this), formData.id_cavo && (() => {\n            const cavo = cavi.find(c => c.id_cavo === formData.id_cavo);\n            if (!cavo) return null;\n            const collegamenti = cavo.collegamenti || 0;\n            const isCollegato = collegamenti === 3;\n            return /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(Paper, {\n                sx: {\n                  p: 2,\n                  bgcolor: isCollegato ? 'success.light' : 'warning.light'\n                },\n                children: /*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  alignItems: \"center\",\n                  spacing: 2,\n                  children: [isCollegato ? /*#__PURE__*/_jsxDEV(CheckIcon, {\n                    color: \"success\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1801,\n                    columnNumber: 38\n                  }, this) : /*#__PURE__*/_jsxDEV(WarningIcon, {\n                    color: \"warning\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1801,\n                    columnNumber: 70\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"bold\",\n                      children: isCollegato ? 'Cavo Completamente Collegato' : 'Cavo Non Completamente Collegato'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1803,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      children: [\"Stato: \", collegamenti === 0 ? 'Non collegato' : collegamenti === 1 ? 'Solo partenza collegata' : collegamenti === 2 ? 'Solo arrivo collegato' : collegamenti === 3 ? 'Completamente collegato' : 'Stato sconosciuto']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1806,\n                      columnNumber: 25\n                    }, this), !isCollegato && /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        mt: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        display: \"block\",\n                        sx: {\n                          mb: 1\n                        },\n                        children: \"\\u26A0\\uFE0F Il cavo pu\\xF2 essere certificato ma ricorda di completare i collegamenti prima della messa in servizio\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1815,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Button, {\n                        size: \"medium\",\n                        variant: \"contained\",\n                        color: \"warning\",\n                        startIcon: /*#__PURE__*/_jsxDEV(LinkIcon, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1822,\n                          columnNumber: 42\n                        }, this),\n                        onClick: () => handleCollegaCavoFromCertification(cavo),\n                        disabled: operationInProgress,\n                        sx: {\n                          mt: 1,\n                          fontWeight: 'bold',\n                          textTransform: 'none',\n                          boxShadow: 3,\n                          '&:hover': {\n                            boxShadow: 6,\n                            transform: 'translateY(-1px)'\n                          },\n                          animation: 'pulse 2s infinite',\n                          '@keyframes pulse': {\n                            '0%': {\n                              boxShadow: '0 0 0 0 rgba(255, 152, 0, 0.7)'\n                            },\n                            '70%': {\n                              boxShadow: '0 0 0 10px rgba(255, 152, 0, 0)'\n                            },\n                            '100%': {\n                              boxShadow: '0 0 0 0 rgba(255, 152, 0, 0)'\n                            }\n                          }\n                        },\n                        children: \"\\uD83D\\uDD17 Collega Automaticamente\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1818,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1814,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1802,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1800,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1799,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1798,\n              columnNumber: 17\n            }, this);\n          })(), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Divider, {\n              sx: {\n                my: 2\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"Parametri Ambientali e Test Avanzati\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1862,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1861,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1860,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Temperatura Ambiente (\\xB0C)\",\n              type: \"number\",\n              value: formData.temperatura_ambiente,\n              onChange: e => handleFormChange('temperatura_ambiente', e.target.value),\n              helperText: \"Temperatura durante il test\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1869,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1868,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Umidit\\xE0 (%)\",\n              type: \"number\",\n              value: formData.umidita,\n              onChange: e => handleFormChange('umidita', e.target.value),\n              helperText: \"Umidit\\xE0 relativa\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1880,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1879,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Tensione di Prova (V)\",\n              type: \"number\",\n              value: formData.tensione_prova,\n              onChange: e => handleFormChange('tensione_prova', e.target.value),\n              helperText: \"Tensione applicata per il test\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1891,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1890,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Durata Prova (min)\",\n              type: \"number\",\n              value: formData.durata_prova,\n              onChange: e => handleFormChange('durata_prova', e.target.value),\n              helperText: \"Durata del test in minuti\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1902,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1901,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Risultato Finale\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1914,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: formData.risultato_finale,\n                onChange: e => handleFormChange('risultato_finale', e.target.value),\n                label: \"Risultato Finale\",\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"CONFORME\",\n                  children: /*#__PURE__*/_jsxDEV(Stack, {\n                    direction: \"row\",\n                    alignItems: \"center\",\n                    spacing: 1,\n                    children: [/*#__PURE__*/_jsxDEV(CheckIcon, {\n                      color: \"success\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1922,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: \"Conforme\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1923,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1921,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1920,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"NON_CONFORME\",\n                  children: /*#__PURE__*/_jsxDEV(Stack, {\n                    direction: \"row\",\n                    alignItems: \"center\",\n                    spacing: 1,\n                    children: [/*#__PURE__*/_jsxDEV(ErrorIcon, {\n                      color: \"error\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1928,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: \"Non Conforme\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1929,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1927,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1926,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"DA_VERIFICARE\",\n                  children: /*#__PURE__*/_jsxDEV(Stack, {\n                    direction: \"row\",\n                    alignItems: \"center\",\n                    spacing: 1,\n                    children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n                      color: \"warning\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1934,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: \"Da Verificare\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1935,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1933,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1932,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1915,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1913,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1912,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Note\",\n              multiline: true,\n              rows: 3,\n              value: formData.note,\n              onChange: e => handleFormChange('note', e.target.value),\n              placeholder: \"Inserisci eventuali note, osservazioni o anomalie riscontrate durante il test...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1943,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1942,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1637,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1636,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeDialog,\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1956,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCreateCertificazione,\n          variant: \"contained\",\n          disabled: loading || !formData.id_cavo || !formData.id_operatore || !formData.id_strumento || !formData.valore_isolamento,\n          children: dialogType === 'create' ? 'Crea Certificazione' : 'Salva Modifiche'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1957,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1955,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1632,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il dialog di visualizzazione dettagli\n  const renderViewDialog = () => {\n    if (dialogType !== 'view' || !selectedItem) return null;\n    return /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openDialog,\n      onClose: closeDialog,\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: [\"Dettagli Certificazione - \", selectedItem.numero_certificato]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1975,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          sx: {\n            mt: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: \"Informazioni Cavo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1983,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"ID Cavo: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: selectedItem.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1987,\n                    columnNumber: 30\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1986,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Lunghezza Misurata: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [selectedItem.lunghezza_misurata, \" m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1990,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1989,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1982,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1981,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1980,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: \"Informazioni Certificazione\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1999,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Numero: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: selectedItem.numero_certificato\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2003,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2002,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Data: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: new Date(selectedItem.data_certificazione).toLocaleDateString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2006,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2005,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Operatore: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: selectedItem.operatore || selectedItem.id_operatore\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2009,\n                    columnNumber: 32\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2008,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1998,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1997,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1996,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: \"Risultati Test\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2018,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  container: true,\n                  spacing: 2,\n                  children: [/*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 4,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: \"Continuit\\xE0\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2023,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                      size: \"small\",\n                      label: selectedItem.valore_continuita,\n                      color: selectedItem.valore_continuita === 'OK' ? 'success' : 'error'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2026,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2022,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 4,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: \"Isolamento\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2033,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                      size: \"small\",\n                      label: `${selectedItem.valore_isolamento} MΩ`,\n                      color: parseFloat(selectedItem.valore_isolamento) >= 500 ? 'success' : 'warning'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2036,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2032,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 4,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: \"Resistenza\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2043,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                      size: \"small\",\n                      label: selectedItem.valore_resistenza,\n                      color: selectedItem.valore_resistenza === 'OK' ? 'success' : 'error'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2046,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2042,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2021,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2017,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2016,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2015,\n            columnNumber: 13\n          }, this), selectedItem.note && /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: \"Note\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2061,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: selectedItem.note\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2064,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2060,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2059,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2058,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1979,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1978,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeDialog,\n          children: \"Chiudi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2074,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => handleGeneratePdf(selectedItem),\n          variant: \"contained\",\n          disabled: loading,\n          children: \"Genera PDF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2075,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2073,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1974,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"xl\",\n    sx: {\n      py: 3\n    },\n    children: [renderDashboard(), (loading || operationInProgress) && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(LinearProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2097,\n        columnNumber: 11\n      }, this), progress > 0 && /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        color: \"text.secondary\",\n        sx: {\n          mt: 1\n        },\n        children: [\"Caricamento... \", progress, \"%\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2099,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2096,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Tabs, {\n        value: activeTab,\n        onChange: handleTabChange,\n        indicatorColor: \"primary\",\n        textColor: \"primary\",\n        variant: \"fullWidth\",\n        children: [/*#__PURE__*/_jsxDEV(Tab, {\n          label: /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              fontWeight: \"bold\",\n              children: \"Cavi da Certificare\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2118,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: [filteredCavi.length, \" cavi totali\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2121,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2117,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2115,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          label: /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              fontWeight: \"bold\",\n              children: \"Certificazioni Completate\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2130,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: [filteredCertificazioni.length, \" certificazioni\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2133,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2129,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2127,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2108,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2107,\n      columnNumber: 7\n    }, this), renderSearchAndFilters(), !loading && activeTab === 0 && renderCaviTable(), !loading && activeTab === 1 && renderCertificazioniTable(), renderCertificazioneDialog(), renderViewDialog(), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: snackbar.open,\n      autoHideDuration: 6000,\n      onClose: closeSnackbar,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'right'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: closeSnackbar,\n        severity: snackbar.severity,\n        sx: {\n          width: '100%'\n        },\n        children: snackbar.message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2160,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2154,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 2090,\n    columnNumber: 5\n  }, this);\n}, \"HU6/uSM1q2dgr5LQFJqfjt3UCA8=\")), \"HU6/uSM1q2dgr5LQFJqfjt3UCA8=\");\n_c2 = CertificazioneCaviImproved;\nexport default CertificazioneCaviImproved;\nvar _c, _c2;\n$RefreshReg$(_c, \"CertificazioneCaviImproved$forwardRef\");\n$RefreshReg$(_c2, \"CertificazioneCaviImproved\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "forwardRef", "useImperativeHandle", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "Autocomplete", "CircularProgress", "<PERSON><PERSON>", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "IconButton", "Tabs", "Tab", "Pagination", "InputAdornment", "Divider", "<PERSON><PERSON>", "Chip", "<PERSON><PERSON><PERSON>", "LinearProgress", "Collapse", "Snackbar", "Container", "Add", "AddIcon", "Search", "SearchIcon", "Clear", "ClearIcon", "CheckCircle", "CheckIcon", "Warning", "WarningIcon", "ExpandMore", "ExpandMoreIcon", "ExpandLess", "ExpandLessIcon", "Block", "BlockIcon", "Visibility", "ViewIcon", "PictureAsPdf", "PdfIcon", "Delete", "DeleteIcon", "Error", "ErrorIcon", "Cable", "CableIcon", "Build", "BuildIcon", "Link", "LinkIcon", "Download", "DownloadIcon", "certificazioneService", "caviService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CertificazioneCaviImproved", "_s", "_c", "cantiereId", "onSuccess", "onError", "ref", "loading", "setLoading", "activeTab", "setActiveTab", "certificazioni", "setCertificazioni", "cavi", "<PERSON><PERSON><PERSON>", "strumenti", "setStrumenti", "searchTerm", "setSearchTerm", "filteredCavi", "setFilteredCavi", "filteredCertificazioni", "setFilteredCertificazioni", "advancedFiltersOpen", "setAdvancedFiltersOpen", "filters", "setFilters", "stato", "tipologia", "operatore", "dataInizio", "dataFine", "valoreIsolamento", "risultatoTest", "strumento", "certificazione", "currentPage", "setCurrentPage", "itemsPerPage", "sortBy", "setSortBy", "sortOrder", "setSortOrder", "openDialog", "setOpenDialog", "dialogType", "setDialogType", "selectedItem", "setSelectedItem", "bulkSelection", "setBulkSelection", "bulkMode", "setBulkMode", "snackbar", "setSnackbar", "open", "message", "severity", "progress", "setProgress", "operationInProgress", "setOperationInProgress", "formData", "setFormData", "id_cavo", "id_operatore", "id_strumento", "<PERSON><PERSON><PERSON>_misurata", "valore_continuita", "valore_isolamento", "valore_resistenza", "note", "temperatura_ambiente", "<PERSON><PERSON><PERSON>", "tensione_prova", "durata_prova", "risultato_finale", "statistics", "setStatistics", "totaleCavi", "caviCertificati", "caviNonCertificati", "percentualeCompletamento", "certificazioniOggi", "certificazioniSettimana", "loadInitialData", "filterCavi", "filterCertificazioni", "calculateStatistics", "caviData", "loadCavi", "certificazioniData", "loadCertificazioni", "loadStrumenti", "calculateStatisticsWithData", "error", "showSnackbar", "data", "getCertificazioni", "console", "get<PERSON><PERSON>", "sortedData", "sort", "a", "b", "getNumFromId", "id", "match", "parseInt", "getStrumenti", "puoEssereCertificato", "cavo", "isInstallato", "stato_installazione", "isCavoCollegato", "isCollegato", "colle<PERSON>nti", "hasResponsabili", "responsabile_partenza", "responsabile_arrivo", "log", "length", "Math", "round", "oggi", "Date", "toDateString", "filter", "cert", "data_certificazione", "unaSettimanaFa", "setDate", "getDate", "caviCertificabili", "caviNonCertificabili", "caviCollegati", "newStatistics", "closeSnackbar", "filtered", "searchLower", "toLowerCase", "_cavo$tipologia", "_cavo$ubicazione_part", "_cavo$ubicazione_arri", "_cavo$sezione", "_cavo$utility", "includes", "ubicazione_partenza", "ubicazione_arrivo", "sezione", "utility", "some", "aValue", "bValue", "aNum", "bNum", "_cert$operatore", "_cert$numero_certific", "_cert$note", "numero_certificato", "valore", "parseFloat", "toggleBulkMode", "toggleItemSelection", "itemId", "prev", "newSelection", "selectAllItems", "allIds", "map", "id_certificazione", "clearSelection", "isCavoCertificato", "idCavo", "getMessaggioErroreCertificazione", "handleTabChange", "event", "newValue", "openCreateDialog", "cavoPreselezionato", "metratura_reale", "metri_te<PERSON>ci", "closeDialog", "handleFormChange", "field", "value", "handleCavoSelect", "collegaCavoAutomatico", "cavoId", "responsabile", "default", "partenzaCollegata", "arrivoCollegato", "collegaCavo", "detail", "handleCollegaCavoFromCertification", "statoCollegamenti", "conferma", "window", "confirm", "handleCreateCertificazione", "find", "c", "messaggio", "createCertificazione", "handleGeneratePdf", "cantiere_id", "response", "generatePdf", "file_url", "newWindow", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "pdf_content", "blob", "Blob", "atob", "type", "url", "URL", "createObjectURL", "revokeObjectURL", "handleDeleteCertificazione", "deleteCertificazione", "handleBulkDelete", "handleBulkExport", "<PERSON><PERSON><PERSON><PERSON>", "csv<PERSON><PERSON>nt", "generateCSV", "downloadCSV", "toISOString", "split", "headers", "rows", "toLocaleDateString", "row", "join", "content", "filename", "undefined", "setAttribute", "style", "visibility", "handleExportAll", "handleOptionSelect", "option", "createCertificationForCavo", "viewCertificationForCavo", "generatePdfForCavo", "getCurrentPageItems", "items", "startIndex", "endIndex", "slice", "getTotalPages", "ceil", "renderDashboard", "sx", "p", "mb", "bgcolor", "children", "direction", "spacing", "alignItems", "justifyContent", "flexWrap", "color", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "fontWeight", "lineHeight", "width", "height", "borderRadius", "display", "renderSearchAndFilters", "container", "item", "xs", "md", "fullWidth", "placeholder", "onChange", "e", "target", "InputProps", "startAdornment", "position", "endAdornment", "onClick", "size", "Object", "values", "f", "disabled", "in", "my", "Set", "Boolean", "tip", "op", "label", "InputLabelProps", "shrink", "renderCaviTable", "currentItems", "component", "isCertificato", "puoCertificare", "messaggioErrore", "statoCollegamento", "colore", "title", "icon", "mt", "count", "page", "renderCertificazioniTable", "padding", "selected", "hover", "s", "nome", "marca", "strumento_utilizzato", "renderCertificazioneDialog", "onClose", "max<PERSON><PERSON><PERSON>", "options", "isSelected", "isNotCertified", "canBeCertified", "getOptionLabel", "renderInput", "params", "required", "helperText", "renderOption", "props", "modello", "numero_serie", "startIcon", "textTransform", "boxShadow", "transform", "animation", "multiline", "renderViewDialog", "gutterBottom", "py", "indicatorColor", "textColor", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "_c2", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/cavi/CertificazioneCaviImproved.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, forwardRef, useImperativeHandle } from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Grid,\n  Card,\n  CardContent,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Autocomplete,\n  CircularProgress,\n  Alert,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  IconButton,\n  Tabs,\n  Tab,\n  Pagination,\n  InputAdornment,\n  Divider,\n  Stack,\n  Chip,\n  Tooltip,\n  LinearProgress,\n  Collapse,\n  Snackbar,\n  Container\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Search as SearchIcon,\n  Clear as ClearIcon,\n  CheckCircle as CheckIcon,\n  Warning as WarningIcon,\n  ExpandMore as ExpandMoreIcon,\n  ExpandLess as ExpandLessIcon,\n  Block as BlockIcon,\n  Visibility as ViewIcon,\n  PictureAsPdf as PdfIcon,\n  Delete as DeleteIcon,\n  Error as ErrorIcon,\n  Cable as CableIcon,\n  Build as BuildIcon,\n  Link as LinkIcon,\n  Download as DownloadIcon\n} from '@mui/icons-material';\n\nimport certificazioneService from '../../services/certificazioneService';\nimport caviService from '../../services/caviService';\n\nconst CertificazioneCaviImproved = forwardRef(({ cantiereId, onSuccess, onError }, ref) => {\n  // Stati principali\n  const [loading, setLoading] = useState(false);\n  const [activeTab, setActiveTab] = useState(0);\n  const [certificazioni, setCertificazioni] = useState([]);\n  const [cavi, setCavi] = useState([]);\n  const [strumenti, setStrumenti] = useState([]);\n\n  // Stati per ricerca e filtri avanzati\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filteredCavi, setFilteredCavi] = useState([]);\n  const [filteredCertificazioni, setFilteredCertificazioni] = useState([]);\n  const [advancedFiltersOpen, setAdvancedFiltersOpen] = useState(false);\n  const [filters, setFilters] = useState({\n    stato: '',\n    tipologia: '',\n    operatore: '',\n    dataInizio: '',\n    dataFine: '',\n    valoreIsolamento: '',\n    risultatoTest: '',\n    strumento: '',\n    certificazione: ''\n  });\n\n  // Stati per paginazione e ordinamento\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage] = useState(10);\n  const [sortBy, setSortBy] = useState('id_cavo');\n  const [sortOrder, setSortOrder] = useState('asc'); // Ordine crescente di default per ID cavo\n\n  // Stati per dialogs e modali\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedItem, setSelectedItem] = useState(null);\n  const [bulkSelection, setBulkSelection] = useState([]);\n  const [bulkMode, setBulkMode] = useState(false);\n\n  // Stati per notifiche e feedback\n  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' });\n  const [progress, setProgress] = useState(0);\n  const [operationInProgress, setOperationInProgress] = useState(false);\n\n  // Stati per form certificazione avanzato\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    id_operatore: '',\n    id_strumento: '',\n    lunghezza_misurata: '',\n    valore_continuita: 'OK',\n    valore_isolamento: '',\n    valore_resistenza: 'OK',\n    note: '',\n    temperatura_ambiente: '',\n    umidita: '',\n    tensione_prova: '',\n    durata_prova: '',\n    risultato_finale: 'CONFORME'\n  });\n\n  // Stati per statistiche e dashboard\n  const [statistics, setStatistics] = useState({\n    totaleCavi: 0,\n    caviCertificati: 0,\n    caviNonCertificati: 0,\n    percentualeCompletamento: 0,\n    certificazioniOggi: 0,\n    certificazioniSettimana: 0\n  });\n\n  // Carica dati iniziali\n  useEffect(() => {\n    loadInitialData();\n  }, [cantiereId]);\n\n  // Filtra cavi in base alla ricerca\n  useEffect(() => {\n    filterCavi();\n  }, [cavi, searchTerm, filters, sortBy, sortOrder]);\n\n  // Filtra certificazioni\n  useEffect(() => {\n    filterCertificazioni();\n  }, [certificazioni, searchTerm, filters, sortBy, sortOrder]);\n\n  // Ricalcola statistiche quando cambiano i dati\n  useEffect(() => {\n    calculateStatistics();\n  }, [cavi, certificazioni]);\n\n  // Riapplica filtri quando cambia il tab attivo E applica filtri iniziali\n  useEffect(() => {\n    if (activeTab === 0) {\n      filterCavi();\n    } else if (activeTab === 1) {\n      filterCertificazioni();\n    }\n  }, [activeTab, cavi, certificazioni]); // Aggiunto cavi e certificazioni come dipendenze\n\n  const loadInitialData = async () => {\n    try {\n      setLoading(true);\n      setProgress(0);\n\n      // Carica dati in sequenza con progress\n      setProgress(25);\n      const caviData = await loadCavi();\n\n      setProgress(50);\n      const certificazioniData = await loadCertificazioni();\n\n      setProgress(75);\n      await loadStrumenti();\n\n      setProgress(100);\n\n      // Calcola le statistiche usando i dati appena caricati\n      // invece di aspettare che gli stati si aggiornino\n      calculateStatisticsWithData(caviData, certificazioniData);\n\n    } catch (error) {\n      showSnackbar('Errore nel caricamento dei dati iniziali', 'error');\n      onError('Errore nel caricamento dei dati iniziali');\n    } finally {\n      setLoading(false);\n      setProgress(0);\n    }\n  };\n\n  const loadCertificazioni = async () => {\n    try {\n      const data = await certificazioneService.getCertificazioni(cantiereId);\n      setCertificazioni(data);\n      return data;\n    } catch (error) {\n      console.error('Errore nel caricamento delle certificazioni:', error);\n      throw error;\n    }\n  };\n\n  const loadCavi = async () => {\n    try {\n      const data = await caviService.getCavi(cantiereId);\n      // Ordina sempre i cavi per ID in ordine crescente (C001, C002, etc.)\n      const sortedData = data.sort((a, b) => {\n        // Estrai il numero dall'ID del cavo (es. C001 -> 1)\n        const getNumFromId = (id) => {\n          const match = id.match(/(\\d+)/);\n          return match ? parseInt(match[1], 10) : 0;\n        };\n        return getNumFromId(a.id_cavo) - getNumFromId(b.id_cavo);\n      });\n      setCavi(sortedData);\n      return sortedData;\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      throw error;\n    }\n  };\n\n  const loadStrumenti = async () => {\n    try {\n      const data = await certificazioneService.getStrumenti(cantiereId);\n      setStrumenti(data);\n      return data;\n    } catch (error) {\n      console.error('Errore nel caricamento degli strumenti:', error);\n      throw error;\n    }\n  };\n\n  // Funzione per verificare se un cavo può essere certificato\n  const puoEssereCertificato = useCallback((cavo) => {\n    // Verifica che il cavo sia installato/posato\n    const isInstallato = cavo.stato_installazione === 'Installato' ||\n                        cavo.stato_installazione === 'INSTALLATO' ||\n                        cavo.stato_installazione === 'POSATO';\n\n    // Per la certificazione basta che sia posato\n    // Il collegamento può essere gestito durante la certificazione\n    return isInstallato;\n  }, []);\n\n  // Funzione per verificare se un cavo è completamente collegato\n  const isCavoCollegato = useCallback((cavo) => {\n    const isCollegato = cavo.collegamenti === 3;\n    const hasResponsabili = cavo.responsabile_partenza && cavo.responsabile_arrivo;\n    return isCollegato && hasResponsabili;\n  }, []);\n\n  // Calcola statistiche avanzate usando i dati passati come parametri\n  const calculateStatisticsWithData = useCallback((caviData, certificazioniData) => {\n    if (!caviData || !certificazioniData) {\n      console.log('Dati mancanti per il calcolo delle statistiche:', { caviData: !!caviData, certificazioniData: !!certificazioniData });\n      return;\n    }\n\n    console.log('Calcolo statistiche con dati:', { cavi: caviData.length, certificazioni: certificazioniData.length });\n\n    const totaleCavi = caviData.length;\n    const caviCertificati = certificazioniData.length;\n    const caviNonCertificati = totaleCavi - caviCertificati;\n    const percentualeCompletamento = totaleCavi > 0 ? Math.round((caviCertificati / totaleCavi) * 100) : 0;\n\n    // Calcola certificazioni di oggi\n    const oggi = new Date().toDateString();\n    const certificazioniOggi = certificazioniData.filter(cert =>\n      new Date(cert.data_certificazione).toDateString() === oggi\n    ).length;\n\n    // Calcola certificazioni della settimana\n    const unaSettimanaFa = new Date();\n    unaSettimanaFa.setDate(unaSettimanaFa.getDate() - 7);\n    const certificazioniSettimana = certificazioniData.filter(cert =>\n      new Date(cert.data_certificazione) >= unaSettimanaFa\n    ).length;\n\n    // Calcola cavi certificabili (solo posati/installati)\n    const caviCertificabili = caviData.filter(cavo => puoEssereCertificato(cavo)).length;\n    const caviNonCertificabili = totaleCavi - caviCertificabili;\n\n    // Calcola cavi collegati completamente\n    const caviCollegati = caviData.filter(cavo => isCavoCollegato(cavo)).length;\n\n    const newStatistics = {\n      totaleCavi,\n      caviCertificati,\n      caviNonCertificati,\n      caviCertificabili,\n      caviNonCertificabili,\n      caviCollegati,\n      percentualeCompletamento,\n      certificazioniOggi,\n      certificazioniSettimana\n    };\n\n    console.log('Nuove statistiche calcolate:', newStatistics);\n    setStatistics(newStatistics);\n  }, [puoEssereCertificato, isCavoCollegato]);\n\n  // Calcola statistiche avanzate usando gli stati correnti\n  const calculateStatistics = useCallback(() => {\n    if (!cavi || !certificazioni) {\n      console.log('Stati non pronti per il calcolo delle statistiche');\n      return;\n    }\n    calculateStatisticsWithData(cavi, certificazioni);\n  }, [cavi, certificazioni, calculateStatisticsWithData]);\n\n  // Gestione snackbar\n  const showSnackbar = (message, severity = 'info') => {\n    setSnackbar({ open: true, message, severity });\n  };\n\n  const closeSnackbar = () => {\n    setSnackbar({ ...snackbar, open: false });\n  };\n\n  const filterCavi = () => {\n    let filtered = cavi;\n\n    // Filtro per ricerca testuale avanzata\n    if (searchTerm) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(cavo =>\n        cavo.id_cavo.toLowerCase().includes(searchLower) ||\n        cavo.tipologia?.toLowerCase().includes(searchLower) ||\n        cavo.ubicazione_partenza?.toLowerCase().includes(searchLower) ||\n        cavo.ubicazione_arrivo?.toLowerCase().includes(searchLower) ||\n        cavo.sezione?.toLowerCase().includes(searchLower) ||\n        cavo.utility?.toLowerCase().includes(searchLower)\n      );\n    }\n\n    // Filtri specifici avanzati\n    if (filters.stato) {\n      filtered = filtered.filter(cavo => cavo.stato_installazione === filters.stato);\n    }\n    if (filters.tipologia) {\n      filtered = filtered.filter(cavo => cavo.tipologia === filters.tipologia);\n    }\n\n    // Filtro per stato certificazione\n    if (filters.certificazione) {\n      if (filters.certificazione === 'CERTIFICATO') {\n        filtered = filtered.filter(cavo =>\n          certificazioni.some(cert => cert.id_cavo === cavo.id_cavo)\n        );\n      } else if (filters.certificazione === 'NON_CERTIFICATO') {\n        filtered = filtered.filter(cavo =>\n          !certificazioni.some(cert => cert.id_cavo === cavo.id_cavo)\n        );\n      }\n    }\n\n    // Ordinamento - speciale per ID cavo per mantenere ordine numerico\n    filtered.sort((a, b) => {\n      let aValue = a[sortBy];\n      let bValue = b[sortBy];\n\n      // Ordinamento speciale per ID cavo (C001, C002, etc.)\n      if (sortBy === 'id_cavo') {\n        const getNumFromId = (id) => {\n          const match = id.match(/(\\d+)/);\n          return match ? parseInt(match[1], 10) : 0;\n        };\n        const aNum = getNumFromId(aValue);\n        const bNum = getNumFromId(bValue);\n\n        if (sortOrder === 'asc') {\n          return aNum - bNum;\n        } else {\n          return bNum - aNum;\n        }\n      }\n\n      // Ordinamento normale per altri campi\n      if (typeof aValue === 'string') {\n        aValue = aValue.toLowerCase();\n        bValue = bValue.toLowerCase();\n      }\n\n      if (sortOrder === 'asc') {\n        return aValue > bValue ? 1 : -1;\n      } else {\n        return aValue < bValue ? 1 : -1;\n      }\n    });\n\n    setFilteredCavi(filtered);\n  };\n\n  const filterCertificazioni = () => {\n    let filtered = certificazioni;\n\n    // Ricerca testuale avanzata\n    if (searchTerm) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(cert =>\n        cert.id_cavo.toLowerCase().includes(searchLower) ||\n        cert.operatore?.toLowerCase().includes(searchLower) ||\n        cert.numero_certificato?.toLowerCase().includes(searchLower) ||\n        cert.note?.toLowerCase().includes(searchLower)\n      );\n    }\n\n    // Filtri avanzati\n    if (filters.operatore) {\n      filtered = filtered.filter(cert => cert.operatore === filters.operatore);\n    }\n    if (filters.strumento) {\n      filtered = filtered.filter(cert => cert.strumento === filters.strumento);\n    }\n    if (filters.risultatoTest) {\n      filtered = filtered.filter(cert => cert.risultato_finale === filters.risultatoTest);\n    }\n    if (filters.dataInizio) {\n      filtered = filtered.filter(cert =>\n        new Date(cert.data_certificazione) >= new Date(filters.dataInizio)\n      );\n    }\n    if (filters.dataFine) {\n      filtered = filtered.filter(cert =>\n        new Date(cert.data_certificazione) <= new Date(filters.dataFine)\n      );\n    }\n    if (filters.valoreIsolamento) {\n      const valore = parseFloat(filters.valoreIsolamento);\n      filtered = filtered.filter(cert =>\n        parseFloat(cert.valore_isolamento) >= valore\n      );\n    }\n\n    // Ordinamento\n    filtered.sort((a, b) => {\n      let aValue = a[sortBy];\n      let bValue = b[sortBy];\n\n      if (sortBy === 'data_certificazione') {\n        aValue = new Date(aValue);\n        bValue = new Date(bValue);\n      } else if (typeof aValue === 'string') {\n        aValue = aValue.toLowerCase();\n        bValue = bValue.toLowerCase();\n      }\n\n      if (sortOrder === 'asc') {\n        return aValue > bValue ? 1 : -1;\n      } else {\n        return aValue < bValue ? 1 : -1;\n      }\n    });\n\n    setFilteredCertificazioni(filtered);\n  };\n\n  // Gestione selezione multipla - SOLO per certificazioni\n  const toggleBulkMode = () => {\n    if (activeTab !== 1) {\n      showSnackbar('La selezione multipla è disponibile solo per le certificazioni', 'warning');\n      return;\n    }\n    setBulkMode(!bulkMode);\n    setBulkSelection([]);\n    showSnackbar(\n      !bulkMode\n        ? 'Modalità selezione attivata - Clicca sulle certificazioni per selezionarle'\n        : 'Modalità selezione disattivata',\n      'info'\n    );\n  };\n\n  const toggleItemSelection = (itemId) => {\n    setBulkSelection(prev => {\n      const newSelection = prev.includes(itemId)\n        ? prev.filter(id => id !== itemId)\n        : [...prev, itemId];\n\n      showSnackbar(\n        `${newSelection.length} certificazioni selezionate`,\n        'info'\n      );\n      return newSelection;\n    });\n  };\n\n  const selectAllItems = () => {\n    if (activeTab !== 1) return;\n\n    const allIds = filteredCertificazioni.map(cert => cert.id_certificazione);\n    setBulkSelection(allIds);\n    showSnackbar(`Tutte le ${allIds.length} certificazioni selezionate`, 'success');\n  };\n\n  const clearSelection = () => {\n    setBulkSelection([]);\n    showSnackbar('Selezione cancellata', 'info');\n  };\n\n  // Funzione per verificare se un cavo è certificato\n  const isCavoCertificato = (idCavo) => {\n    return certificazioni.some(cert => cert.id_cavo === idCavo);\n  };\n\n  // Funzione per ottenere il messaggio di errore per cavi non certificabili\n  const getMessaggioErroreCertificazione = (cavo) => {\n    const isInstallato = cavo.stato_installazione === 'Installato' ||\n                        cavo.stato_installazione === 'INSTALLATO' ||\n                        cavo.stato_installazione === 'POSATO';\n\n    if (!isInstallato) {\n      return 'Il cavo deve essere posato/installato prima di poter essere certificato';\n    }\n\n    return 'Cavo non certificabile per motivi sconosciuti';\n  };\n\n  // Gestione tabs\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n    setCurrentPage(1);\n    setSearchTerm('');\n    setFilters({ stato: '', tipologia: '', operatore: '' });\n  };\n\n  // Gestione dialogs\n  const openCreateDialog = (cavoPreselezionato = null) => {\n    setDialogType('create');\n    setSelectedItem(null);\n\n    // Se viene passato un cavo, precompila il form\n    if (cavoPreselezionato) {\n      setFormData({\n        id_cavo: cavoPreselezionato.id_cavo,\n        id_operatore: '',\n        id_strumento: '',\n        lunghezza_misurata: cavoPreselezionato.metratura_reale || cavoPreselezionato.metri_teorici || '',\n        valore_continuita: 'OK',\n        valore_isolamento: '',\n        valore_resistenza: 'OK',\n        note: '',\n        temperatura_ambiente: '',\n        umidita: '',\n        tensione_prova: '',\n        durata_prova: '',\n        risultato_finale: 'CONFORME'\n      });\n      showSnackbar(`Cavo ${cavoPreselezionato.id_cavo} selezionato automaticamente`, 'success');\n    } else {\n      // Reset form per nuova certificazione generica\n      setFormData({\n        id_cavo: '',\n        id_operatore: '',\n        id_strumento: '',\n        lunghezza_misurata: '',\n        valore_continuita: 'OK',\n        valore_isolamento: '',\n        valore_resistenza: 'OK',\n        note: '',\n        temperatura_ambiente: '',\n        umidita: '',\n        tensione_prova: '',\n        durata_prova: '',\n        risultato_finale: 'CONFORME'\n      });\n    }\n\n    setOpenDialog(true);\n  };\n\n  const closeDialog = () => {\n    setOpenDialog(false);\n    setSelectedItem(null);\n    setDialogType('');\n  };\n\n  // Gestione form\n  const handleFormChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  const handleCavoSelect = (cavo) => {\n    setFormData(prev => ({\n      ...prev,\n      id_cavo: cavo.id_cavo,\n      lunghezza_misurata: cavo.metratura_reale || cavo.metri_teorici || ''\n    }));\n  };\n\n  // Funzione per collegare automaticamente un cavo\n  const collegaCavoAutomatico = async (cavoId, responsabile = 'cantiere') => {\n    try {\n      // Importa il servizio cavi\n      const caviService = (await import('../../services/caviService')).default;\n\n      let partenzaCollegata = false;\n      let arrivoCollegato = false;\n\n      // Prova a collegare il lato partenza\n      try {\n        await caviService.collegaCavo(cantiereId, cavoId, 'partenza', responsabile);\n        partenzaCollegata = true;\n        console.log('Lato partenza collegato con successo');\n      } catch (error) {\n        if (error.detail && error.detail.includes('già collegato')) {\n          partenzaCollegata = true;\n          console.log('Lato partenza già collegato');\n        } else {\n          console.error('Errore nel collegamento lato partenza:', error);\n          throw error;\n        }\n      }\n\n      // Prova a collegare il lato arrivo\n      try {\n        await caviService.collegaCavo(cantiereId, cavoId, 'arrivo', responsabile);\n        arrivoCollegato = true;\n        console.log('Lato arrivo collegato con successo');\n      } catch (error) {\n        if (error.detail && error.detail.includes('già collegato')) {\n          arrivoCollegato = true;\n          console.log('Lato arrivo già collegato');\n        } else {\n          console.error('Errore nel collegamento lato arrivo:', error);\n          throw error;\n        }\n      }\n\n      return partenzaCollegata && arrivoCollegato;\n    } catch (error) {\n      console.error('Errore nel collegamento automatico:', error);\n      throw error;\n    }\n  };\n\n  // Funzione per collegare un cavo direttamente dall'interfaccia di certificazione\n  const handleCollegaCavoFromCertification = async (cavo) => {\n    try {\n      const collegamenti = cavo.collegamenti || 0;\n      const statoCollegamenti = collegamenti === 0 ? 'Non collegato' :\n                               collegamenti === 1 ? 'Solo partenza collegata' :\n                               collegamenti === 2 ? 'Solo arrivo collegato' :\n                               'Stato sconosciuto';\n\n      const conferma = window.confirm(\n        `Collegamento automatico del cavo ${cavo.id_cavo}\\n\\n` +\n        `Stato attuale: ${statoCollegamenti}\\n\\n` +\n        `Il sistema collegherà automaticamente entrambi i lati del cavo a \"cantiere\".\\n\\n` +\n        `Vuoi procedere?`\n      );\n\n      if (!conferma) {\n        return;\n      }\n\n      setOperationInProgress(true);\n      showSnackbar('Collegamento automatico del cavo in corso...', 'info');\n\n      await collegaCavoAutomatico(cavo.id_cavo, 'cantiere');\n      showSnackbar(`Cavo ${cavo.id_cavo} collegato automaticamente su entrambi i lati`, 'success');\n\n      // Ricarica i cavi per aggiornare lo stato nell'interfaccia\n      await loadCavi();\n\n    } catch (error) {\n      console.error('Errore nel collegamento del cavo:', error);\n      showSnackbar('Errore nel collegamento automatico: ' + (error.message || 'Errore sconosciuto'), 'error');\n    } finally {\n      setOperationInProgress(false);\n    }\n  };\n\n  // Operazioni CRUD avanzate\n  const handleCreateCertificazione = async () => {\n    try {\n      if (!formData.id_cavo || !formData.id_operatore || !formData.id_strumento || !formData.valore_isolamento) {\n        showSnackbar('Compila tutti i campi obbligatori', 'warning');\n        return;\n      }\n\n      // Verifica che il cavo possa essere certificato\n      const cavo = cavi.find(c => c.id_cavo === formData.id_cavo);\n      if (!cavo) {\n        showSnackbar('Cavo non trovato', 'error');\n        return;\n      }\n\n      if (!puoEssereCertificato(cavo)) {\n        const messaggio = getMessaggioErroreCertificazione(cavo);\n        showSnackbar(`Impossibile certificare il cavo: ${messaggio}`, 'error');\n        return;\n      }\n\n      // Verifica che il cavo non sia già certificato\n      if (isCavoCertificato(formData.id_cavo)) {\n        showSnackbar('Il cavo è già stato certificato', 'warning');\n        return;\n      }\n\n      // Verifica se il cavo è collegato, altrimenti collega automaticamente\n      if (!isCavoCollegato(cavo)) {\n        const statoCollegamenti = cavo.collegamenti === 0 ? 'Non collegato' :\n                                 cavo.collegamenti === 1 ? 'Solo partenza collegata' :\n                                 cavo.collegamenti === 2 ? 'Solo arrivo collegato' :\n                                 'Stato sconosciuto';\n\n        const conferma = window.confirm(\n          `ATTENZIONE: Il cavo ${cavo.id_cavo} non risulta completamente collegato.\\n\\n` +\n          `Stato collegamenti: ${statoCollegamenti}\\n\\n` +\n          `Vuoi collegare automaticamente entrambi i lati del cavo e procedere con la certificazione?\\n\\n` +\n          `(Il sistema collegherà automaticamente il cavo a \"cantiere\" su entrambi i lati)`\n        );\n\n        if (!conferma) {\n          return;\n        }\n\n        // Collega automaticamente il cavo\n        try {\n          setOperationInProgress(true);\n          showSnackbar('Collegamento automatico del cavo in corso...', 'info');\n\n          await collegaCavoAutomatico(cavo.id_cavo, 'cantiere');\n          showSnackbar('Cavo collegato automaticamente su entrambi i lati', 'success');\n\n          // Ricarica i cavi per aggiornare lo stato\n          await loadCavi();\n        } catch (error) {\n          showSnackbar('Errore nel collegamento automatico: ' + (error.message || 'Errore sconosciuto'), 'error');\n          return;\n        }\n      }\n\n      setOperationInProgress(true);\n      await certificazioneService.createCertificazione(cantiereId, formData);\n      showSnackbar('Certificazione creata con successo', 'success');\n      closeDialog();\n      await loadCertificazioni();\n      calculateStatistics();\n    } catch (error) {\n      showSnackbar('Errore nella creazione della certificazione: ' + (error.message || 'Errore sconosciuto'), 'error');\n    } finally {\n      setOperationInProgress(false);\n    }\n  };\n\n  const handleGeneratePdf = async (certificazione) => {\n    try {\n      setOperationInProgress(true);\n      showSnackbar('Generazione PDF in corso...', 'info');\n\n      console.log(`🔍 DEBUG PDF - Generando PDF per certificazione:`, {\n        id_certificazione: certificazione.id_certificazione,\n        numero_certificato: certificazione.numero_certificato,\n        id_cavo: certificazione.id_cavo,\n        cantiere_id: cantiereId\n      });\n\n      const response = await certificazioneService.generatePdf(cantiereId, certificazione.id_certificazione);\n\n      if (response.file_url) {\n        // Apri il PDF in una nuova finestra\n        const newWindow = window.open(response.file_url, '_blank');\n        if (newWindow) {\n          showSnackbar('PDF generato e aperto in una nuova finestra', 'success');\n        } else {\n          // Se il popup è bloccato, offri il download diretto\n          const link = document.createElement('a');\n          link.href = response.file_url;\n          link.download = `certificazione_${certificazione.numero_certificato}.pdf`;\n          document.body.appendChild(link);\n          link.click();\n          document.body.removeChild(link);\n          showSnackbar('PDF scaricato nella cartella Download', 'success');\n        }\n      } else if (response.pdf_content) {\n        // Se il PDF viene restituito come contenuto base64\n        const blob = new Blob([atob(response.pdf_content)], { type: 'application/pdf' });\n        const url = URL.createObjectURL(blob);\n        const link = document.createElement('a');\n        link.href = url;\n        link.download = `certificazione_${certificazione.numero_certificato}.pdf`;\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        URL.revokeObjectURL(url);\n        showSnackbar('PDF scaricato con successo', 'success');\n      } else {\n        showSnackbar('Errore: Formato PDF non riconosciuto', 'error');\n      }\n    } catch (error) {\n      console.error('Errore generazione PDF:', error);\n      showSnackbar('Errore nella generazione del PDF: ' + (error.message || 'Errore sconosciuto'), 'error');\n    } finally {\n      setOperationInProgress(false);\n    }\n  };\n\n  const handleDeleteCertificazione = async (certificazione) => {\n    if (window.confirm(`Sei sicuro di voler eliminare la certificazione ${certificazione.numero_certificato}?`)) {\n      try {\n        setOperationInProgress(true);\n        await certificazioneService.deleteCertificazione(cantiereId, certificazione.id_certificazione);\n        showSnackbar('Certificazione eliminata con successo', 'success');\n        await loadCertificazioni();\n        calculateStatistics();\n      } catch (error) {\n        showSnackbar('Errore nell\\'eliminazione della certificazione: ' + (error.message || 'Errore sconosciuto'), 'error');\n      } finally {\n        setOperationInProgress(false);\n      }\n    }\n  };\n\n  // Operazioni bulk\n  const handleBulkDelete = async () => {\n    if (bulkSelection.length === 0) {\n      showSnackbar('Seleziona almeno un elemento', 'warning');\n      return;\n    }\n\n    if (window.confirm(`Sei sicuro di voler eliminare ${bulkSelection.length} certificazioni?`)) {\n      try {\n        setOperationInProgress(true);\n        for (const id of bulkSelection) {\n          await certificazioneService.deleteCertificazione(cantiereId, id);\n        }\n        showSnackbar(`${bulkSelection.length} certificazioni eliminate con successo`, 'success');\n        setBulkSelection([]);\n        await loadCertificazioni();\n        calculateStatistics();\n      } catch (error) {\n        showSnackbar('Errore nell\\'eliminazione delle certificazioni', 'error');\n      } finally {\n        setOperationInProgress(false);\n      }\n    }\n  };\n\n  const handleBulkExport = async () => {\n    if (bulkSelection.length === 0) {\n      showSnackbar('Seleziona almeno un elemento', 'warning');\n      return;\n    }\n\n    try {\n      setOperationInProgress(true);\n      // Implementa export bulk\n      const selectedCerts = certificazioni.filter(cert =>\n        bulkSelection.includes(cert.id_certificazione)\n      );\n\n      // Crea CSV\n      const csvContent = generateCSV(selectedCerts);\n      downloadCSV(csvContent, `certificazioni_${new Date().toISOString().split('T')[0]}.csv`);\n\n      showSnackbar(`${bulkSelection.length} certificazioni esportate`, 'success');\n    } catch (error) {\n      showSnackbar('Errore nell\\'esportazione', 'error');\n    } finally {\n      setOperationInProgress(false);\n    }\n  };\n\n  // Funzioni di export\n  const generateCSV = (data) => {\n    const headers = ['ID Cavo', 'Numero Certificato', 'Data', 'Operatore', 'Strumento', 'Lunghezza', 'Isolamento', 'Risultato'];\n    const rows = data.map(cert => [\n      cert.id_cavo,\n      cert.numero_certificato,\n      new Date(cert.data_certificazione).toLocaleDateString(),\n      cert.operatore,\n      cert.strumento,\n      cert.lunghezza_misurata,\n      cert.valore_isolamento,\n      cert.risultato_finale\n    ]);\n\n    return [headers, ...rows].map(row => row.join(',')).join('\\n');\n  };\n\n  const downloadCSV = (content, filename) => {\n    const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });\n    const link = document.createElement('a');\n    if (link.download !== undefined) {\n      const url = URL.createObjectURL(blob);\n      link.setAttribute('href', url);\n      link.setAttribute('download', filename);\n      link.style.visibility = 'hidden';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n    }\n  };\n\n  const handleExportAll = () => {\n    const csvContent = generateCSV(filteredCertificazioni);\n    downloadCSV(csvContent, `tutte_certificazioni_${new Date().toISOString().split('T')[0]}.csv`);\n    showSnackbar('Esportazione completata', 'success');\n  };\n\n  // Espone metodi tramite ref\n  useImperativeHandle(ref, () => ({\n    handleOptionSelect: (option) => {\n      if (option === 'creaCertificazione') {\n        openCreateDialog();\n      } else if (option === 'visualizzaCertificazioni') {\n        setActiveTab(1);\n      }\n    },\n    createCertificationForCavo: (cavo) => {\n      openCreateDialog(cavo);\n    },\n    viewCertificationForCavo: (cavo) => {\n      // Trova la certificazione per questo cavo\n      const certificazione = certificazioni.find(cert => cert.id_cavo === cavo.id_cavo);\n      if (certificazione) {\n        setSelectedItem(certificazione);\n        setDialogType('view');\n        setOpenDialog(true);\n      } else {\n        showSnackbar('Certificazione non trovata per questo cavo', 'error');\n      }\n    },\n    generatePdfForCavo: (cavo) => {\n      // Trova la certificazione per questo cavo e genera il PDF\n      console.log(`🔍 DEBUG PDF - Cercando certificazione per cavo ${cavo.id_cavo}`);\n      console.log(`🔍 DEBUG PDF - Certificazioni disponibili:`, certificazioni.map(c => ({ id: c.id_certificazione, cavo: c.id_cavo })));\n\n      const certificazione = certificazioni.find(cert => cert.id_cavo === cavo.id_cavo);\n      if (certificazione) {\n        console.log(`🔍 DEBUG PDF - Certificazione trovata:`, certificazione);\n        handleGeneratePdf(certificazione);\n      } else {\n        console.log(`❌ DEBUG PDF - Nessuna certificazione trovata per cavo ${cavo.id_cavo}`);\n        showSnackbar('Certificazione non trovata per questo cavo', 'error');\n      }\n    }\n  }));\n\n  // Calcola elementi per paginazione\n  const getCurrentPageItems = (items) => {\n    const startIndex = (currentPage - 1) * itemsPerPage;\n    const endIndex = startIndex + itemsPerPage;\n    return items.slice(startIndex, endIndex);\n  };\n\n  const getTotalPages = (items) => Math.ceil(items.length / itemsPerPage);\n\n\n\n  // Dashboard minimal con statistiche essenziali\n  const renderDashboard = () => (\n    <Paper sx={{ p: 2, mb: 3, bgcolor: 'grey.50' }}>\n      <Stack direction=\"row\" spacing={4} alignItems=\"center\" justifyContent=\"space-between\" flexWrap=\"wrap\">\n        {/* Statistiche essenziali in formato compatto */}\n        <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n          <CableIcon color=\"primary\" fontSize=\"small\" />\n          <Box>\n            <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n              {statistics.totaleCavi}\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              Totale\n            </Typography>\n          </Box>\n        </Stack>\n\n        <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n          <CheckIcon color=\"success\" fontSize=\"small\" />\n          <Box>\n            <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n              {statistics.caviCertificati}\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              Certificati\n            </Typography>\n          </Box>\n        </Stack>\n\n        <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n          <BuildIcon color=\"info\" fontSize=\"small\" />\n          <Box>\n            <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n              {statistics.caviCertificabili}\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              Pronti\n            </Typography>\n          </Box>\n        </Stack>\n\n        <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n          <Box sx={{\n            width: 32,\n            height: 32,\n            borderRadius: '50%',\n            bgcolor: statistics.percentualeCompletamento >= 80 ? 'success.main' :\n                     statistics.percentualeCompletamento >= 50 ? 'warning.main' : 'error.main',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          }}>\n            <Typography variant=\"caption\" fontWeight=\"bold\" color=\"white\">\n              {statistics.percentualeCompletamento}%\n            </Typography>\n          </Box>\n          <Box>\n            <Typography variant=\"body2\" fontWeight=\"medium\" sx={{ lineHeight: 1 }}>\n              Completamento\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              {statistics.certificazioniOggi} oggi\n            </Typography>\n          </Box>\n        </Stack>\n\n\n      </Stack>\n    </Paper>\n  );\n\n  // Componente barra di ricerca avanzata\n  const renderSearchAndFilters = () => (\n    <Paper sx={{ p: 2, mb: 3 }}>\n      <Grid container spacing={2} alignItems=\"center\">\n        <Grid item xs={12} md={4}>\n          <TextField\n            fullWidth\n            placeholder=\"Cerca cavi, certificazioni, operatori...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            InputProps={{\n              startAdornment: (\n                <InputAdornment position=\"start\">\n                  <SearchIcon />\n                </InputAdornment>\n              ),\n              endAdornment: searchTerm && (\n                <InputAdornment position=\"end\">\n                  <IconButton onClick={() => setSearchTerm('')} size=\"small\">\n                    <ClearIcon />\n                  </IconButton>\n                </InputAdornment>\n              )\n            }}\n          />\n        </Grid>\n\n        <Grid item xs={12} md={2}>\n          <Button\n            fullWidth\n            variant=\"outlined\"\n            onClick={() => setAdvancedFiltersOpen(!advancedFiltersOpen)}\n            color={Object.values(filters).some(f => f) ? 'primary' : 'inherit'}\n          >\n            Filtri {Object.values(filters).filter(f => f).length > 0 && `(${Object.values(filters).filter(f => f).length})`}\n          </Button>\n        </Grid>\n\n        <Grid item xs={12} md={2}>\n          <Button\n            fullWidth\n            variant=\"outlined\"\n            onClick={toggleBulkMode}\n            color={bulkMode ? 'secondary' : 'inherit'}\n            disabled={activeTab === 0}\n          >\n            {bulkMode ? 'Esci Selezione' : 'Selezione Multipla'}\n          </Button>\n        </Grid>\n\n        <Grid item xs={12} md={2}>\n          <Button\n            fullWidth\n            variant=\"outlined\"\n            onClick={handleExportAll}\n            disabled={activeTab === 0 || filteredCertificazioni.length === 0}\n          >\n            {activeTab === 0 ? 'Export (solo certificazioni)' : 'Esporta Certificazioni'}\n          </Button>\n        </Grid>\n\n        <Grid item xs={12} md={2}>\n          <Button\n            fullWidth\n            variant=\"contained\"\n            onClick={openCreateDialog}\n          >\n            Nuova Certificazione\n          </Button>\n        </Grid>\n      </Grid>\n\n      {/* Filtri avanzati - Diversi per ogni tab */}\n      <Collapse in={advancedFiltersOpen}>\n        <Divider sx={{ my: 2 }} />\n        <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n          {activeTab === 0 ? 'Filtri per Cavi' : 'Filtri per Certificazioni'}\n        </Typography>\n\n        <Grid container spacing={2}>\n          {/* Filtri per tab Cavi */}\n          {activeTab === 0 && (\n            <>\n              <Grid item xs={12} md={3}>\n                <FormControl fullWidth size=\"small\">\n                  <InputLabel>Stato Installazione</InputLabel>\n                  <Select\n                    value={filters.stato}\n                    onChange={(e) => setFilters({...filters, stato: e.target.value})}\n                  >\n                    <MenuItem value=\"\">Tutti</MenuItem>\n                    <MenuItem value=\"INSTALLATO\">Installato</MenuItem>\n                    <MenuItem value=\"NON_INSTALLATO\">Non Installato</MenuItem>\n                    <MenuItem value=\"IN_CORSO\">In Corso</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12} md={3}>\n                <FormControl fullWidth size=\"small\">\n                  <InputLabel>Tipologia</InputLabel>\n                  <Select\n                    value={filters.tipologia}\n                    onChange={(e) => setFilters({...filters, tipologia: e.target.value})}\n                  >\n                    <MenuItem value=\"\">Tutte</MenuItem>\n                    {[...new Set(cavi.map(c => c.tipologia))].filter(Boolean).map(tip => (\n                      <MenuItem key={tip} value={tip}>{tip}</MenuItem>\n                    ))}\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12} md={3}>\n                <FormControl fullWidth size=\"small\">\n                  <InputLabel>Stato Certificazione</InputLabel>\n                  <Select\n                    value={filters.certificazione}\n                    onChange={(e) => setFilters({...filters, certificazione: e.target.value})}\n                  >\n                    <MenuItem value=\"\">Tutti</MenuItem>\n                    <MenuItem value=\"CERTIFICATO\">Certificato</MenuItem>\n                    <MenuItem value=\"NON_CERTIFICATO\">Non Certificato</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n            </>\n          )}\n\n          {/* Filtri per tab Certificazioni */}\n          {activeTab === 1 && (\n            <>\n              <Grid item xs={12} md={3}>\n                <FormControl fullWidth size=\"small\">\n                  <InputLabel>Operatore</InputLabel>\n                  <Select\n                    value={filters.operatore}\n                    onChange={(e) => setFilters({...filters, operatore: e.target.value})}\n                  >\n                    <MenuItem value=\"\">Tutti</MenuItem>\n                    {[...new Set(certificazioni.map(c => c.operatore))].filter(Boolean).map(op => (\n                      <MenuItem key={op} value={op}>{op}</MenuItem>\n                    ))}\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12} md={3}>\n                <FormControl fullWidth size=\"small\">\n                  <InputLabel>Risultato Test</InputLabel>\n                  <Select\n                    value={filters.risultatoTest}\n                    onChange={(e) => setFilters({...filters, risultatoTest: e.target.value})}\n                  >\n                    <MenuItem value=\"\">Tutti</MenuItem>\n                    <MenuItem value=\"CONFORME\">Conforme</MenuItem>\n                    <MenuItem value=\"NON_CONFORME\">Non Conforme</MenuItem>\n                    <MenuItem value=\"DA_VERIFICARE\">Da Verificare</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12} md={3}>\n                <TextField\n                  fullWidth\n                  size=\"small\"\n                  label=\"Isolamento Min (MΩ)\"\n                  type=\"number\"\n                  value={filters.valoreIsolamento}\n                  onChange={(e) => setFilters({...filters, valoreIsolamento: e.target.value})}\n                  placeholder=\"es. 500\"\n                />\n              </Grid>\n\n              <Grid item xs={12} md={3}>\n                <TextField\n                  fullWidth\n                  size=\"small\"\n                  label=\"Data Inizio\"\n                  type=\"date\"\n                  value={filters.dataInizio}\n                  onChange={(e) => setFilters({...filters, dataInizio: e.target.value})}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n\n              <Grid item xs={12} md={3}>\n                <TextField\n                  fullWidth\n                  size=\"small\"\n                  label=\"Data Fine\"\n                  type=\"date\"\n                  value={filters.dataFine}\n                  onChange={(e) => setFilters({...filters, dataFine: e.target.value})}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n            </>\n          )}\n\n          <Grid item xs={12}>\n            <Stack direction=\"row\" spacing={1} justifyContent=\"flex-end\">\n              <Button\n                variant=\"outlined\"\n                size=\"small\"\n                onClick={() => setFilters({\n                  stato: '', tipologia: '', operatore: '', dataInizio: '',\n                  dataFine: '', valoreIsolamento: '', risultatoTest: '', strumento: '',\n                  certificazione: ''\n                })}\n              >\n                Pulisci Tutti i Filtri\n              </Button>\n            </Stack>\n          </Grid>\n        </Grid>\n      </Collapse>\n\n      {/* Barra azioni bulk */}\n      {bulkMode && bulkSelection.length > 0 && (\n        <>\n          <Divider sx={{ my: 2 }} />\n          <Stack direction=\"row\" spacing={2} alignItems=\"center\">\n            <Typography variant=\"body2\">\n              {bulkSelection.length} elementi selezionati\n            </Typography>\n            <Button\n              size=\"small\"\n              variant=\"outlined\"\n              onClick={selectAllItems}\n            >\n              Seleziona Tutto\n            </Button>\n            <Button\n              size=\"small\"\n              variant=\"outlined\"\n              onClick={clearSelection}\n            >\n              Deseleziona\n            </Button>\n            <Button\n              size=\"small\"\n              variant=\"outlined\"\n              onClick={handleBulkExport}\n            >\n              Esporta Selezionati\n            </Button>\n            <Button\n              size=\"small\"\n              variant=\"outlined\"\n              color=\"error\"\n              onClick={handleBulkDelete}\n            >\n              Elimina Selezionati\n            </Button>\n          </Stack>\n        </>\n      )}\n    </Paper>\n  );\n\n  // Renderizza la tabella dei cavi\n  const renderCaviTable = () => {\n    const currentItems = getCurrentPageItems(filteredCavi);\n\n    if (filteredCavi.length === 0) {\n      return (\n        <Alert severity=\"info\">\n          {searchTerm || filters.stato || filters.tipologia\n            ? 'Nessun cavo trovato con i filtri applicati'\n            : 'Nessun cavo disponibile'}\n        </Alert>\n      );\n    }\n\n    return (\n      <>\n        <TableContainer component={Paper}>\n          <Table size=\"small\">\n            <TableHead>\n              <TableRow>\n                <TableCell>\n                  <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n                    <Typography variant=\"body2\" fontWeight=\"bold\">ID Cavo</Typography>\n                    <IconButton size=\"small\" onClick={() => {\n                      setSortBy('id_cavo');\n                      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\n                    }}>\n                      {sortBy === 'id_cavo' ? (sortOrder === 'asc' ? <ExpandLessIcon /> : <ExpandMoreIcon />) : <ExpandMoreIcon />}\n                    </IconButton>\n                  </Stack>\n                </TableCell>\n                <TableCell>Tipologia</TableCell>\n                <TableCell>Sezione</TableCell>\n                <TableCell>Partenza</TableCell>\n                <TableCell>Arrivo</TableCell>\n                <TableCell>Metri</TableCell>\n                <TableCell>Stato</TableCell>\n                <TableCell>Collegamenti</TableCell>\n                <TableCell>Certificato</TableCell>\n                <TableCell>Azioni</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {currentItems.map((cavo) => {\n                const isCertificato = isCavoCertificato(cavo.id_cavo);\n                const puoCertificare = puoEssereCertificato(cavo);\n                const messaggioErrore = !puoCertificare ? getMessaggioErroreCertificazione(cavo) : '';\n\n                return (\n                  <TableRow key={cavo.id_cavo}>\n                    <TableCell>\n                      <Typography variant=\"body2\" fontWeight=\"medium\">\n                        {cavo.id_cavo}\n                      </Typography>\n                    </TableCell>\n                    <TableCell>{cavo.tipologia}</TableCell>\n                    <TableCell>{cavo.sezione}</TableCell>\n                    <TableCell>{cavo.ubicazione_partenza}</TableCell>\n                    <TableCell>{cavo.ubicazione_arrivo}</TableCell>\n                    <TableCell>{cavo.metratura_reale || cavo.metri_teorici} m</TableCell>\n                    <TableCell>\n                      <Chip\n                        size=\"small\"\n                        label={cavo.stato_installazione}\n                        color={cavo.stato_installazione === 'INSTALLATO' ? 'success' : 'default'}\n                      />\n                    </TableCell>\n                    <TableCell>\n                      {(() => {\n                        const collegamenti = cavo.collegamenti || 0;\n                        const statoCollegamento = collegamenti === 0 ? 'Non collegato' :\n                                                 collegamenti === 1 ? 'Solo partenza' :\n                                                 collegamenti === 2 ? 'Solo arrivo' :\n                                                 collegamenti === 3 ? 'Completo' :\n                                                 'Sconosciuto';\n                        const colore = collegamenti === 3 ? 'success' :\n                                      collegamenti === 0 ? 'error' : 'warning';\n\n                        return (\n                          <Tooltip title={`Partenza: ${cavo.responsabile_partenza || 'Non collegato'} | Arrivo: ${cavo.responsabile_arrivo || 'Non collegato'}`}>\n                            <Chip\n                              size=\"small\"\n                              label={statoCollegamento}\n                              color={colore}\n                              icon={collegamenti === 3 ? <CheckIcon /> : <WarningIcon />}\n                            />\n                          </Tooltip>\n                        );\n                      })()}\n                    </TableCell>\n                    <TableCell>\n                      {isCertificato ? (\n                        <Chip\n                          size=\"small\"\n                          icon={<CheckIcon />}\n                          label=\"Certificato\"\n                          color=\"success\"\n                        />\n                      ) : (\n                        <Chip\n                          size=\"small\"\n                          icon={<WarningIcon />}\n                          label=\"Non certificato\"\n                          color=\"warning\"\n                        />\n                      )}\n                    </TableCell>\n                    <TableCell>\n                      {isCertificato ? (\n                        <Tooltip title=\"Cavo già certificato\">\n                          <Chip\n                            icon={<CheckIcon />}\n                            label=\"Certificato\"\n                            color=\"success\"\n                            size=\"small\"\n                          />\n                        </Tooltip>\n                      ) : puoCertificare ? (\n                        <Tooltip title=\"Crea certificazione per questo cavo\">\n                          <IconButton\n                            size=\"small\"\n                            onClick={() => openCreateDialog(cavo)}\n                            color=\"primary\"\n                          >\n                            <AddIcon />\n                          </IconButton>\n                        </Tooltip>\n                      ) : (\n                        <Tooltip title={messaggioErrore}>\n                          <span>\n                            <IconButton\n                              size=\"small\"\n                              disabled\n                              onClick={() => showSnackbar(messaggioErrore, 'warning')}\n                            >\n                              <BlockIcon />\n                            </IconButton>\n                          </span>\n                        </Tooltip>\n                      )}\n                    </TableCell>\n                  </TableRow>\n                );\n              })}\n            </TableBody>\n          </Table>\n        </TableContainer>\n\n        {getTotalPages(filteredCavi) > 1 && (\n          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>\n            <Pagination\n              count={getTotalPages(filteredCavi)}\n              page={currentPage}\n              onChange={(event, value) => setCurrentPage(value)}\n              color=\"primary\"\n            />\n          </Box>\n        )}\n      </>\n    );\n  };\n\n  // Renderizza la tabella delle certificazioni\n  const renderCertificazioniTable = () => {\n    const currentItems = getCurrentPageItems(filteredCertificazioni);\n\n    if (filteredCertificazioni.length === 0) {\n      return (\n        <Alert severity=\"info\">\n          {searchTerm || filters.operatore\n            ? 'Nessuna certificazione trovata con i filtri applicati'\n            : 'Nessuna certificazione disponibile'}\n        </Alert>\n      );\n    }\n\n    return (\n      <>\n        <TableContainer component={Paper}>\n          <Table size=\"small\">\n            <TableHead>\n              <TableRow>\n                {bulkMode && (\n                  <TableCell padding=\"checkbox\">\n                    <IconButton\n                      size=\"small\"\n                      onClick={bulkSelection.length === filteredCertificazioni.length ? clearSelection : selectAllItems}\n                    >\n                      {bulkSelection.length === filteredCertificazioni.length ? <ClearIcon /> : <CheckIcon />}\n                    </IconButton>\n                  </TableCell>\n                )}\n                <TableCell>\n                  <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n                    <Typography variant=\"body2\" fontWeight=\"bold\">N° Certificato</Typography>\n                    <IconButton size=\"small\" onClick={() => {\n                      setSortBy('numero_certificato');\n                      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\n                    }}>\n                      {sortBy === 'numero_certificato' ? (sortOrder === 'asc' ? <ExpandLessIcon /> : <ExpandMoreIcon />) : <ExpandMoreIcon />}\n                    </IconButton>\n                  </Stack>\n                </TableCell>\n                <TableCell>ID Cavo</TableCell>\n                <TableCell>\n                  <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n                    <Typography variant=\"body2\" fontWeight=\"bold\">Data</Typography>\n                    <IconButton size=\"small\" onClick={() => {\n                      setSortBy('data_certificazione');\n                      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\n                    }}>\n                      {sortBy === 'data_certificazione' ? (sortOrder === 'asc' ? <ExpandLessIcon /> : <ExpandMoreIcon />) : <ExpandMoreIcon />}\n                    </IconButton>\n                  </Stack>\n                </TableCell>\n                <TableCell>Operatore</TableCell>\n                <TableCell>Strumento</TableCell>\n                <TableCell>Lunghezza</TableCell>\n                <TableCell>Isolamento</TableCell>\n                <TableCell>Risultato</TableCell>\n                <TableCell>Azioni</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {currentItems.map((cert) => (\n                <TableRow\n                  key={cert.id_certificazione}\n                  selected={bulkSelection.includes(cert.id_certificazione)}\n                  hover\n                >\n                  {bulkMode && (\n                    <TableCell padding=\"checkbox\">\n                      <IconButton\n                        size=\"small\"\n                        onClick={() => toggleItemSelection(cert.id_certificazione)}\n                        color={bulkSelection.includes(cert.id_certificazione) ? 'primary' : 'default'}\n                      >\n                        {bulkSelection.includes(cert.id_certificazione) ? <CheckIcon /> : <AddIcon />}\n                      </IconButton>\n                    </TableCell>\n                  )}\n                  <TableCell>\n                    <Typography variant=\"body2\" fontWeight=\"medium\">\n                      {cert.numero_certificato}\n                    </Typography>\n                  </TableCell>\n                  <TableCell>\n                    <Chip size=\"small\" label={cert.id_cavo} variant=\"outlined\" />\n                  </TableCell>\n                  <TableCell>{new Date(cert.data_certificazione).toLocaleDateString()}</TableCell>\n                  <TableCell>\n                    <Typography variant=\"body2\">{cert.operatore || cert.id_operatore}</Typography>\n                  </TableCell>\n                  <TableCell>\n                    <Typography variant=\"body2\">\n                      {cert.id_strumento ?\n                        (() => {\n                          const strumento = strumenti.find(s => s.id_strumento === cert.id_strumento);\n                          return strumento ? `${strumento.nome} - ${strumento.marca}` : 'Strumento non trovato';\n                        })()\n                        : (cert.strumento_utilizzato || 'N/A')\n                      }\n                    </Typography>\n                  </TableCell>\n                  <TableCell>\n                    <Typography variant=\"body2\">{cert.lunghezza_misurata} m</Typography>\n                  </TableCell>\n                  <TableCell>\n                    <Chip\n                      size=\"small\"\n                      label={`${cert.valore_isolamento} MΩ`}\n                      color={parseFloat(cert.valore_isolamento) >= 500 ? 'success' : 'warning'}\n                      icon={parseFloat(cert.valore_isolamento) >= 500 ? <CheckIcon /> : <WarningIcon />}\n                    />\n                  </TableCell>\n                  <TableCell>\n                    <Chip\n                      size=\"small\"\n                      label={cert.risultato_finale || 'CONFORME'}\n                      color={cert.risultato_finale === 'CONFORME' ? 'success' : cert.risultato_finale === 'NON_CONFORME' ? 'error' : 'warning'}\n                    />\n                  </TableCell>\n                  <TableCell>\n                    <Stack direction=\"row\" spacing={0.5}>\n                      <Tooltip title=\"Visualizza dettagli\">\n                        <IconButton\n                          size=\"small\"\n                          onClick={() => {\n                            setSelectedItem(cert);\n                            setDialogType('view');\n                            setOpenDialog(true);\n                          }}\n                        >\n                          <ViewIcon />\n                        </IconButton>\n                      </Tooltip>\n                      <Tooltip title=\"Genera PDF\">\n                        <IconButton\n                          size=\"small\"\n                          onClick={() => handleGeneratePdf(cert)}\n                          disabled={operationInProgress}\n                        >\n                          <PdfIcon />\n                        </IconButton>\n                      </Tooltip>\n                      <Tooltip title=\"Elimina\">\n                        <IconButton\n                          size=\"small\"\n                          color=\"error\"\n                          onClick={() => handleDeleteCertificazione(cert)}\n                          disabled={operationInProgress}\n                        >\n                          <DeleteIcon />\n                        </IconButton>\n                      </Tooltip>\n                    </Stack>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </TableBody>\n          </Table>\n        </TableContainer>\n\n        {getTotalPages(filteredCertificazioni) > 1 && (\n          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>\n            <Pagination\n              count={getTotalPages(filteredCertificazioni)}\n              page={currentPage}\n              onChange={(event, value) => setCurrentPage(value)}\n              color=\"primary\"\n            />\n          </Box>\n        )}\n      </>\n    );\n  };\n\n  // Renderizza il dialog per creare/modificare certificazione\n  const renderCertificazioneDialog = () => {\n    if (dialogType !== 'create' && dialogType !== 'edit') return null;\n\n    return (\n      <Dialog open={openDialog} onClose={closeDialog} maxWidth=\"md\" fullWidth>\n        <DialogTitle>\n          {dialogType === 'create' ? 'Nuova Certificazione' : 'Modifica Certificazione'}\n        </DialogTitle>\n        <DialogContent>\n          <Grid container spacing={2} sx={{ mt: 1 }}>\n            <Grid item xs={12} md={6}>\n              <Autocomplete\n                options={cavi.filter(cavo => {\n                  // Mostra solo cavi che possono essere certificati o quello già selezionato\n                  const isSelected = cavo.id_cavo === formData.id_cavo;\n                  const isNotCertified = !certificazioni.some(cert => cert.id_cavo === cavo.id_cavo);\n                  const canBeCertified = puoEssereCertificato(cavo);\n\n                  return isSelected || (isNotCertified && canBeCertified);\n                })}\n                getOptionLabel={(option) => `${option.id_cavo} - ${option.tipologia}`}\n                value={cavi.find(c => c.id_cavo === formData.id_cavo) || null}\n                onChange={(event, newValue) => {\n                  if (newValue) {\n                    handleCavoSelect(newValue);\n                  } else {\n                    setFormData(prev => ({ ...prev, id_cavo: '', lunghezza_misurata: '' }));\n                  }\n                }}\n                renderInput={(params) => (\n                  <TextField\n                    {...params}\n                    label=\"Cavo *\"\n                    placeholder=\"Seleziona un cavo posato\"\n                    required\n                    helperText=\"Solo cavi posati/installati (il collegamento può essere gestito al momento)\"\n                  />\n                )}\n                renderOption={(props, option) => {\n                  const collegamenti = option.collegamenti || 0;\n                  const isCollegato = collegamenti === 3;\n\n                  return (\n                    <Box component=\"li\" {...props}>\n                      <Box sx={{ width: '100%' }}>\n                        <Stack direction=\"row\" justifyContent=\"space-between\" alignItems=\"center\">\n                          <Box>\n                            <Typography variant=\"body2\" fontWeight=\"medium\">\n                              {option.id_cavo}\n                            </Typography>\n                            <Typography variant=\"caption\" color=\"text.secondary\">\n                              {option.tipologia} - {option.ubicazione_partenza} → {option.ubicazione_arrivo}\n                            </Typography>\n                          </Box>\n                          <Stack direction=\"row\" spacing={1}>\n                            <Chip\n                              size=\"small\"\n                              label={option.stato_installazione}\n                              color={option.stato_installazione === 'INSTALLATO' ? 'success' : 'default'}\n                            />\n                            <Chip\n                              size=\"small\"\n                              label={isCollegato ? 'Collegato' : 'Da collegare'}\n                              color={isCollegato ? 'success' : 'warning'}\n                              icon={isCollegato ? <CheckIcon /> : <WarningIcon />}\n                            />\n                          </Stack>\n                        </Stack>\n                      </Box>\n                    </Box>\n                  );\n                }}\n              />\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Operatore *\"\n                value={formData.id_operatore}\n                onChange={(e) => handleFormChange('id_operatore', e.target.value)}\n                required\n              />\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <FormControl fullWidth required>\n                <InputLabel>Strumento *</InputLabel>\n                <Select\n                  value={formData.id_strumento}\n                  onChange={(e) => handleFormChange('id_strumento', e.target.value)}\n                  label=\"Strumento *\"\n                >\n                  {strumenti.map((strumento) => (\n                    <MenuItem key={strumento.id_strumento} value={strumento.id_strumento}>\n                      {strumento.nome} - {strumento.marca} {strumento.modello} (S/N: {strumento.numero_serie})\n                    </MenuItem>\n                  ))}\n                </Select>\n              </FormControl>\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Lunghezza Misurata (m) *\"\n                type=\"number\"\n                value={formData.lunghezza_misurata}\n                onChange={(e) => handleFormChange('lunghezza_misurata', e.target.value)}\n                required\n              />\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <FormControl fullWidth>\n                <InputLabel>Continuità</InputLabel>\n                <Select\n                  value={formData.valore_continuita}\n                  onChange={(e) => handleFormChange('valore_continuita', e.target.value)}\n                  label=\"Continuità\"\n                >\n                  <MenuItem value=\"OK\">OK</MenuItem>\n                  <MenuItem value=\"NOK\">NOK</MenuItem>\n                </Select>\n              </FormControl>\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <TextField\n                fullWidth\n                label=\"Isolamento (MΩ) *\"\n                type=\"number\"\n                value={formData.valore_isolamento}\n                onChange={(e) => handleFormChange('valore_isolamento', e.target.value)}\n                required\n                helperText=\"Valore minimo consigliato: 500 MΩ\"\n              />\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <FormControl fullWidth>\n                <InputLabel>Resistenza</InputLabel>\n                <Select\n                  value={formData.valore_resistenza}\n                  onChange={(e) => handleFormChange('valore_resistenza', e.target.value)}\n                  label=\"Resistenza\"\n                >\n                  <MenuItem value=\"OK\">OK</MenuItem>\n                  <MenuItem value=\"NOK\">NOK</MenuItem>\n                </Select>\n              </FormControl>\n            </Grid>\n\n            {/* Sezione Collegamenti */}\n            <Grid item xs={12}>\n              <Divider sx={{ my: 2 }}>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Stato Collegamenti Cavo\n                </Typography>\n              </Divider>\n            </Grid>\n\n            {formData.id_cavo && (() => {\n              const cavo = cavi.find(c => c.id_cavo === formData.id_cavo);\n              if (!cavo) return null;\n\n              const collegamenti = cavo.collegamenti || 0;\n              const isCollegato = collegamenti === 3;\n\n              return (\n                <Grid item xs={12}>\n                  <Paper sx={{ p: 2, bgcolor: isCollegato ? 'success.light' : 'warning.light' }}>\n                    <Stack direction=\"row\" alignItems=\"center\" spacing={2}>\n                      {isCollegato ? <CheckIcon color=\"success\" /> : <WarningIcon color=\"warning\" />}\n                      <Box>\n                        <Typography variant=\"body2\" fontWeight=\"bold\">\n                          {isCollegato ? 'Cavo Completamente Collegato' : 'Cavo Non Completamente Collegato'}\n                        </Typography>\n                        <Typography variant=\"caption\">\n                          Stato: {collegamenti === 0 ? 'Non collegato' :\n                                  collegamenti === 1 ? 'Solo partenza collegata' :\n                                  collegamenti === 2 ? 'Solo arrivo collegato' :\n                                  collegamenti === 3 ? 'Completamente collegato' :\n                                  'Stato sconosciuto'}\n                        </Typography>\n                        {!isCollegato && (\n                          <Box sx={{ mt: 1 }}>\n                            <Typography variant=\"caption\" display=\"block\" sx={{ mb: 1 }}>\n                              ⚠️ Il cavo può essere certificato ma ricorda di completare i collegamenti prima della messa in servizio\n                            </Typography>\n                            <Button\n                              size=\"medium\"\n                              variant=\"contained\"\n                              color=\"warning\"\n                              startIcon={<LinkIcon />}\n                              onClick={() => handleCollegaCavoFromCertification(cavo)}\n                              disabled={operationInProgress}\n                              sx={{\n                                mt: 1,\n                                fontWeight: 'bold',\n                                textTransform: 'none',\n                                boxShadow: 3,\n                                '&:hover': {\n                                  boxShadow: 6,\n                                  transform: 'translateY(-1px)'\n                                },\n                                animation: 'pulse 2s infinite',\n                                '@keyframes pulse': {\n                                  '0%': {\n                                    boxShadow: '0 0 0 0 rgba(255, 152, 0, 0.7)'\n                                  },\n                                  '70%': {\n                                    boxShadow: '0 0 0 10px rgba(255, 152, 0, 0)'\n                                  },\n                                  '100%': {\n                                    boxShadow: '0 0 0 0 rgba(255, 152, 0, 0)'\n                                  }\n                                }\n                              }}\n                            >\n                              🔗 Collega Automaticamente\n                            </Button>\n                          </Box>\n                        )}\n                      </Box>\n                    </Stack>\n                  </Paper>\n                </Grid>\n              );\n            })()}\n\n            {/* Campi avanzati */}\n            <Grid item xs={12}>\n              <Divider sx={{ my: 2 }}>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Parametri Ambientali e Test Avanzati\n                </Typography>\n              </Divider>\n            </Grid>\n\n            <Grid item xs={12} md={3}>\n              <TextField\n                fullWidth\n                label=\"Temperatura Ambiente (°C)\"\n                type=\"number\"\n                value={formData.temperatura_ambiente}\n                onChange={(e) => handleFormChange('temperatura_ambiente', e.target.value)}\n                helperText=\"Temperatura durante il test\"\n              />\n            </Grid>\n\n            <Grid item xs={12} md={3}>\n              <TextField\n                fullWidth\n                label=\"Umidità (%)\"\n                type=\"number\"\n                value={formData.umidita}\n                onChange={(e) => handleFormChange('umidita', e.target.value)}\n                helperText=\"Umidità relativa\"\n              />\n            </Grid>\n\n            <Grid item xs={12} md={3}>\n              <TextField\n                fullWidth\n                label=\"Tensione di Prova (V)\"\n                type=\"number\"\n                value={formData.tensione_prova}\n                onChange={(e) => handleFormChange('tensione_prova', e.target.value)}\n                helperText=\"Tensione applicata per il test\"\n              />\n            </Grid>\n\n            <Grid item xs={12} md={3}>\n              <TextField\n                fullWidth\n                label=\"Durata Prova (min)\"\n                type=\"number\"\n                value={formData.durata_prova}\n                onChange={(e) => handleFormChange('durata_prova', e.target.value)}\n                helperText=\"Durata del test in minuti\"\n              />\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <FormControl fullWidth>\n                <InputLabel>Risultato Finale</InputLabel>\n                <Select\n                  value={formData.risultato_finale}\n                  onChange={(e) => handleFormChange('risultato_finale', e.target.value)}\n                  label=\"Risultato Finale\"\n                >\n                  <MenuItem value=\"CONFORME\">\n                    <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n                      <CheckIcon color=\"success\" />\n                      <Typography>Conforme</Typography>\n                    </Stack>\n                  </MenuItem>\n                  <MenuItem value=\"NON_CONFORME\">\n                    <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n                      <ErrorIcon color=\"error\" />\n                      <Typography>Non Conforme</Typography>\n                    </Stack>\n                  </MenuItem>\n                  <MenuItem value=\"DA_VERIFICARE\">\n                    <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n                      <WarningIcon color=\"warning\" />\n                      <Typography>Da Verificare</Typography>\n                    </Stack>\n                  </MenuItem>\n                </Select>\n              </FormControl>\n            </Grid>\n\n            <Grid item xs={12}>\n              <TextField\n                fullWidth\n                label=\"Note\"\n                multiline\n                rows={3}\n                value={formData.note}\n                onChange={(e) => handleFormChange('note', e.target.value)}\n                placeholder=\"Inserisci eventuali note, osservazioni o anomalie riscontrate durante il test...\"\n              />\n            </Grid>\n          </Grid>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeDialog}>Annulla</Button>\n          <Button\n            onClick={handleCreateCertificazione}\n            variant=\"contained\"\n            disabled={loading || !formData.id_cavo || !formData.id_operatore || !formData.id_strumento || !formData.valore_isolamento}\n          >\n            {dialogType === 'create' ? 'Crea Certificazione' : 'Salva Modifiche'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n    );\n  };\n\n  // Renderizza il dialog di visualizzazione dettagli\n  const renderViewDialog = () => {\n    if (dialogType !== 'view' || !selectedItem) return null;\n\n    return (\n      <Dialog open={openDialog} onClose={closeDialog} maxWidth=\"md\" fullWidth>\n        <DialogTitle>\n          Dettagli Certificazione - {selectedItem.numero_certificato}\n        </DialogTitle>\n        <DialogContent>\n          <Grid container spacing={2} sx={{ mt: 1 }}>\n            <Grid item xs={12} md={6}>\n              <Card variant=\"outlined\">\n                <CardContent>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Informazioni Cavo\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    ID Cavo: <strong>{selectedItem.id_cavo}</strong>\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Lunghezza Misurata: <strong>{selectedItem.lunghezza_misurata} m</strong>\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <Card variant=\"outlined\">\n                <CardContent>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Informazioni Certificazione\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Numero: <strong>{selectedItem.numero_certificato}</strong>\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Data: <strong>{new Date(selectedItem.data_certificazione).toLocaleDateString()}</strong>\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Operatore: <strong>{selectedItem.operatore || selectedItem.id_operatore}</strong>\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n\n            <Grid item xs={12}>\n              <Card variant=\"outlined\">\n                <CardContent>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Risultati Test\n                  </Typography>\n                  <Grid container spacing={2}>\n                    <Grid item xs={4}>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        Continuità\n                      </Typography>\n                      <Chip\n                        size=\"small\"\n                        label={selectedItem.valore_continuita}\n                        color={selectedItem.valore_continuita === 'OK' ? 'success' : 'error'}\n                      />\n                    </Grid>\n                    <Grid item xs={4}>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        Isolamento\n                      </Typography>\n                      <Chip\n                        size=\"small\"\n                        label={`${selectedItem.valore_isolamento} MΩ`}\n                        color={parseFloat(selectedItem.valore_isolamento) >= 500 ? 'success' : 'warning'}\n                      />\n                    </Grid>\n                    <Grid item xs={4}>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        Resistenza\n                      </Typography>\n                      <Chip\n                        size=\"small\"\n                        label={selectedItem.valore_resistenza}\n                        color={selectedItem.valore_resistenza === 'OK' ? 'success' : 'error'}\n                      />\n                    </Grid>\n                  </Grid>\n                </CardContent>\n              </Card>\n            </Grid>\n\n            {selectedItem.note && (\n              <Grid item xs={12}>\n                <Card variant=\"outlined\">\n                  <CardContent>\n                    <Typography variant=\"h6\" gutterBottom>\n                      Note\n                    </Typography>\n                    <Typography variant=\"body2\">\n                      {selectedItem.note}\n                    </Typography>\n                  </CardContent>\n                </Card>\n              </Grid>\n            )}\n          </Grid>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeDialog}>Chiudi</Button>\n          <Button\n            onClick={() => handleGeneratePdf(selectedItem)}\n            variant=\"contained\"\n            disabled={loading}\n          >\n            Genera PDF\n          </Button>\n        </DialogActions>\n      </Dialog>\n    );\n  };\n\n\n\n  return (\n    <Container maxWidth=\"xl\" sx={{ py: 3 }}>\n      {/* Dashboard con statistiche */}\n      {renderDashboard()}\n\n      {/* Progress bar per operazioni in corso */}\n      {(loading || operationInProgress) && (\n        <Box sx={{ mb: 2 }}>\n          <LinearProgress />\n          {progress > 0 && (\n            <Typography variant=\"caption\" color=\"text.secondary\" sx={{ mt: 1 }}>\n              Caricamento... {progress}%\n            </Typography>\n          )}\n        </Box>\n      )}\n\n      {/* Tabs per navigazione */}\n      <Paper sx={{ mb: 3 }}>\n        <Tabs\n          value={activeTab}\n          onChange={handleTabChange}\n          indicatorColor=\"primary\"\n          textColor=\"primary\"\n          variant=\"fullWidth\"\n        >\n          <Tab\n            label={\n              <Box>\n                <Typography variant=\"body2\" fontWeight=\"bold\">\n                  Cavi da Certificare\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  {filteredCavi.length} cavi totali\n                </Typography>\n              </Box>\n            }\n          />\n          <Tab\n            label={\n              <Box>\n                <Typography variant=\"body2\" fontWeight=\"bold\">\n                  Certificazioni Completate\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  {filteredCertificazioni.length} certificazioni\n                </Typography>\n              </Box>\n            }\n          />\n        </Tabs>\n      </Paper>\n\n      {/* Barra di ricerca e filtri avanzati */}\n      {renderSearchAndFilters()}\n\n      {/* Contenuto delle tabs */}\n      {!loading && activeTab === 0 && renderCaviTable()}\n      {!loading && activeTab === 1 && renderCertificazioniTable()}\n\n      {/* Dialogs */}\n      {renderCertificazioneDialog()}\n      {renderViewDialog()}\n\n      {/* Snackbar per notifiche */}\n      <Snackbar\n        open={snackbar.open}\n        autoHideDuration={6000}\n        onClose={closeSnackbar}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}\n      >\n        <Alert onClose={closeSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>\n          {snackbar.message}\n        </Alert>\n      </Snackbar>\n\n\n    </Container>\n  );\n});\n\nexport default CertificazioneCaviImproved;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,UAAU,EAAEC,mBAAmB,QAAQ,OAAO;AAChG,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,YAAY,EACZC,gBAAgB,EAChBC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,UAAU,EACVC,IAAI,EACJC,GAAG,EACHC,UAAU,EACVC,cAAc,EACdC,OAAO,EACPC,KAAK,EACLC,IAAI,EACJC,OAAO,EACPC,cAAc,EACdC,QAAQ,EACRC,QAAQ,EACRC,SAAS,QACJ,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,SAAS,EACxBC,OAAO,IAAIC,WAAW,EACtBC,UAAU,IAAIC,cAAc,EAC5BC,UAAU,IAAIC,cAAc,EAC5BC,KAAK,IAAIC,SAAS,EAClBC,UAAU,IAAIC,QAAQ,EACtBC,YAAY,IAAIC,OAAO,EACvBC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,KAAK,IAAIC,SAAS,EAClBC,KAAK,IAAIC,SAAS,EAClBC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,QACnB,qBAAqB;AAE5B,OAAOC,qBAAqB,MAAM,sCAAsC;AACxE,OAAOC,WAAW,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErD,MAAMC,0BAA0B,gBAAAC,EAAA,cAAG/E,UAAU,CAAAgF,EAAA,GAAAD,EAAA,CAAC,CAAC;EAAEE,UAAU;EAAEC,SAAS;EAAEC;AAAQ,CAAC,EAAEC,GAAG,KAAK;EAAAL,EAAA;EACzF;EACA,MAAM,CAACM,OAAO,EAAEC,UAAU,CAAC,GAAGzF,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0F,SAAS,EAAEC,YAAY,CAAC,GAAG3F,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAAC4F,cAAc,EAAEC,iBAAiB,CAAC,GAAG7F,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC8F,IAAI,EAAEC,OAAO,CAAC,GAAG/F,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACgG,SAAS,EAAEC,YAAY,CAAC,GAAGjG,QAAQ,CAAC,EAAE,CAAC;;EAE9C;EACA,MAAM,CAACkG,UAAU,EAAEC,aAAa,CAAC,GAAGnG,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACoG,YAAY,EAAEC,eAAe,CAAC,GAAGrG,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACsG,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGvG,QAAQ,CAAC,EAAE,CAAC;EACxE,MAAM,CAACwG,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGzG,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC0G,OAAO,EAAEC,UAAU,CAAC,GAAG3G,QAAQ,CAAC;IACrC4G,KAAK,EAAE,EAAE;IACTC,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE,EAAE;IACbC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE,EAAE;IACZC,gBAAgB,EAAE,EAAE;IACpBC,aAAa,EAAE,EAAE;IACjBC,SAAS,EAAE,EAAE;IACbC,cAAc,EAAE;EAClB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGtH,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACuH,YAAY,CAAC,GAAGvH,QAAQ,CAAC,EAAE,CAAC;EACnC,MAAM,CAACwH,MAAM,EAAEC,SAAS,CAAC,GAAGzH,QAAQ,CAAC,SAAS,CAAC;EAC/C,MAAM,CAAC0H,SAAS,EAAEC,YAAY,CAAC,GAAG3H,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;EAEnD;EACA,MAAM,CAAC4H,UAAU,EAAEC,aAAa,CAAC,GAAG7H,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC8H,UAAU,EAAEC,aAAa,CAAC,GAAG/H,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACgI,YAAY,EAAEC,eAAe,CAAC,GAAGjI,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACkI,aAAa,EAAEC,gBAAgB,CAAC,GAAGnI,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACoI,QAAQ,EAAEC,WAAW,CAAC,GAAGrI,QAAQ,CAAC,KAAK,CAAC;;EAE/C;EACA,MAAM,CAACsI,QAAQ,EAAEC,WAAW,CAAC,GAAGvI,QAAQ,CAAC;IAAEwI,IAAI,EAAE,KAAK;IAAEC,OAAO,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAO,CAAC,CAAC;EACxF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG5I,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAAC6I,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG9I,QAAQ,CAAC,KAAK,CAAC;;EAErE;EACA,MAAM,CAAC+I,QAAQ,EAAEC,WAAW,CAAC,GAAGhJ,QAAQ,CAAC;IACvCiJ,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,YAAY,EAAE,EAAE;IAChBC,kBAAkB,EAAE,EAAE;IACtBC,iBAAiB,EAAE,IAAI;IACvBC,iBAAiB,EAAE,EAAE;IACrBC,iBAAiB,EAAE,IAAI;IACvBC,IAAI,EAAE,EAAE;IACRC,oBAAoB,EAAE,EAAE;IACxBC,OAAO,EAAE,EAAE;IACXC,cAAc,EAAE,EAAE;IAClBC,YAAY,EAAE,EAAE;IAChBC,gBAAgB,EAAE;EACpB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG/J,QAAQ,CAAC;IAC3CgK,UAAU,EAAE,CAAC;IACbC,eAAe,EAAE,CAAC;IAClBC,kBAAkB,EAAE,CAAC;IACrBC,wBAAwB,EAAE,CAAC;IAC3BC,kBAAkB,EAAE,CAAC;IACrBC,uBAAuB,EAAE;EAC3B,CAAC,CAAC;;EAEF;EACApK,SAAS,CAAC,MAAM;IACdqK,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAAClF,UAAU,CAAC,CAAC;;EAEhB;EACAnF,SAAS,CAAC,MAAM;IACdsK,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAACzE,IAAI,EAAEI,UAAU,EAAEQ,OAAO,EAAEc,MAAM,EAAEE,SAAS,CAAC,CAAC;;EAElD;EACAzH,SAAS,CAAC,MAAM;IACduK,oBAAoB,CAAC,CAAC;EACxB,CAAC,EAAE,CAAC5E,cAAc,EAAEM,UAAU,EAAEQ,OAAO,EAAEc,MAAM,EAAEE,SAAS,CAAC,CAAC;;EAE5D;EACAzH,SAAS,CAAC,MAAM;IACdwK,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAAC3E,IAAI,EAAEF,cAAc,CAAC,CAAC;;EAE1B;EACA3F,SAAS,CAAC,MAAM;IACd,IAAIyF,SAAS,KAAK,CAAC,EAAE;MACnB6E,UAAU,CAAC,CAAC;IACd,CAAC,MAAM,IAAI7E,SAAS,KAAK,CAAC,EAAE;MAC1B8E,oBAAoB,CAAC,CAAC;IACxB;EACF,CAAC,EAAE,CAAC9E,SAAS,EAAEI,IAAI,EAAEF,cAAc,CAAC,CAAC,CAAC,CAAC;;EAEvC,MAAM0E,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF7E,UAAU,CAAC,IAAI,CAAC;MAChBmD,WAAW,CAAC,CAAC,CAAC;;MAEd;MACAA,WAAW,CAAC,EAAE,CAAC;MACf,MAAM8B,QAAQ,GAAG,MAAMC,QAAQ,CAAC,CAAC;MAEjC/B,WAAW,CAAC,EAAE,CAAC;MACf,MAAMgC,kBAAkB,GAAG,MAAMC,kBAAkB,CAAC,CAAC;MAErDjC,WAAW,CAAC,EAAE,CAAC;MACf,MAAMkC,aAAa,CAAC,CAAC;MAErBlC,WAAW,CAAC,GAAG,CAAC;;MAEhB;MACA;MACAmC,2BAA2B,CAACL,QAAQ,EAAEE,kBAAkB,CAAC;IAE3D,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,YAAY,CAAC,0CAA0C,EAAE,OAAO,CAAC;MACjE3F,OAAO,CAAC,0CAA0C,CAAC;IACrD,CAAC,SAAS;MACRG,UAAU,CAAC,KAAK,CAAC;MACjBmD,WAAW,CAAC,CAAC,CAAC;IAChB;EACF,CAAC;EAED,MAAMiC,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,MAAMK,IAAI,GAAG,MAAMvG,qBAAqB,CAACwG,iBAAiB,CAAC/F,UAAU,CAAC;MACtES,iBAAiB,CAACqF,IAAI,CAAC;MACvB,OAAOA,IAAI;IACb,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;MACpE,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAML,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACF,MAAMO,IAAI,GAAG,MAAMtG,WAAW,CAACyG,OAAO,CAACjG,UAAU,CAAC;MAClD;MACA,MAAMkG,UAAU,GAAGJ,IAAI,CAACK,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QACrC;QACA,MAAMC,YAAY,GAAIC,EAAE,IAAK;UAC3B,MAAMC,KAAK,GAAGD,EAAE,CAACC,KAAK,CAAC,OAAO,CAAC;UAC/B,OAAOA,KAAK,GAAGC,QAAQ,CAACD,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;QAC3C,CAAC;QACD,OAAOF,YAAY,CAACF,CAAC,CAACvC,OAAO,CAAC,GAAGyC,YAAY,CAACD,CAAC,CAACxC,OAAO,CAAC;MAC1D,CAAC,CAAC;MACFlD,OAAO,CAACuF,UAAU,CAAC;MACnB,OAAOA,UAAU;IACnB,CAAC,CAAC,OAAON,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMF,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMI,IAAI,GAAG,MAAMvG,qBAAqB,CAACmH,YAAY,CAAC1G,UAAU,CAAC;MACjEa,YAAY,CAACiF,IAAI,CAAC;MAClB,OAAOA,IAAI;IACb,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/D,MAAMA,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMe,oBAAoB,GAAG7L,WAAW,CAAE8L,IAAI,IAAK;IACjD;IACA,MAAMC,YAAY,GAAGD,IAAI,CAACE,mBAAmB,KAAK,YAAY,IAC1CF,IAAI,CAACE,mBAAmB,KAAK,YAAY,IACzCF,IAAI,CAACE,mBAAmB,KAAK,QAAQ;;IAEzD;IACA;IACA,OAAOD,YAAY;EACrB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAME,eAAe,GAAGjM,WAAW,CAAE8L,IAAI,IAAK;IAC5C,MAAMI,WAAW,GAAGJ,IAAI,CAACK,YAAY,KAAK,CAAC;IAC3C,MAAMC,eAAe,GAAGN,IAAI,CAACO,qBAAqB,IAAIP,IAAI,CAACQ,mBAAmB;IAC9E,OAAOJ,WAAW,IAAIE,eAAe;EACvC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMvB,2BAA2B,GAAG7K,WAAW,CAAC,CAACwK,QAAQ,EAAEE,kBAAkB,KAAK;IAChF,IAAI,CAACF,QAAQ,IAAI,CAACE,kBAAkB,EAAE;MACpCQ,OAAO,CAACqB,GAAG,CAAC,iDAAiD,EAAE;QAAE/B,QAAQ,EAAE,CAAC,CAACA,QAAQ;QAAEE,kBAAkB,EAAE,CAAC,CAACA;MAAmB,CAAC,CAAC;MAClI;IACF;IAEAQ,OAAO,CAACqB,GAAG,CAAC,+BAA+B,EAAE;MAAE3G,IAAI,EAAE4E,QAAQ,CAACgC,MAAM;MAAE9G,cAAc,EAAEgF,kBAAkB,CAAC8B;IAAO,CAAC,CAAC;IAElH,MAAM1C,UAAU,GAAGU,QAAQ,CAACgC,MAAM;IAClC,MAAMzC,eAAe,GAAGW,kBAAkB,CAAC8B,MAAM;IACjD,MAAMxC,kBAAkB,GAAGF,UAAU,GAAGC,eAAe;IACvD,MAAME,wBAAwB,GAAGH,UAAU,GAAG,CAAC,GAAG2C,IAAI,CAACC,KAAK,CAAE3C,eAAe,GAAGD,UAAU,GAAI,GAAG,CAAC,GAAG,CAAC;;IAEtG;IACA,MAAM6C,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC;IACtC,MAAM3C,kBAAkB,GAAGQ,kBAAkB,CAACoC,MAAM,CAACC,IAAI,IACvD,IAAIH,IAAI,CAACG,IAAI,CAACC,mBAAmB,CAAC,CAACH,YAAY,CAAC,CAAC,KAAKF,IACxD,CAAC,CAACH,MAAM;;IAER;IACA,MAAMS,cAAc,GAAG,IAAIL,IAAI,CAAC,CAAC;IACjCK,cAAc,CAACC,OAAO,CAACD,cAAc,CAACE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;IACpD,MAAMhD,uBAAuB,GAAGO,kBAAkB,CAACoC,MAAM,CAACC,IAAI,IAC5D,IAAIH,IAAI,CAACG,IAAI,CAACC,mBAAmB,CAAC,IAAIC,cACxC,CAAC,CAACT,MAAM;;IAER;IACA,MAAMY,iBAAiB,GAAG5C,QAAQ,CAACsC,MAAM,CAAChB,IAAI,IAAID,oBAAoB,CAACC,IAAI,CAAC,CAAC,CAACU,MAAM;IACpF,MAAMa,oBAAoB,GAAGvD,UAAU,GAAGsD,iBAAiB;;IAE3D;IACA,MAAME,aAAa,GAAG9C,QAAQ,CAACsC,MAAM,CAAChB,IAAI,IAAIG,eAAe,CAACH,IAAI,CAAC,CAAC,CAACU,MAAM;IAE3E,MAAMe,aAAa,GAAG;MACpBzD,UAAU;MACVC,eAAe;MACfC,kBAAkB;MAClBoD,iBAAiB;MACjBC,oBAAoB;MACpBC,aAAa;MACbrD,wBAAwB;MACxBC,kBAAkB;MAClBC;IACF,CAAC;IAEDe,OAAO,CAACqB,GAAG,CAAC,8BAA8B,EAAEgB,aAAa,CAAC;IAC1D1D,aAAa,CAAC0D,aAAa,CAAC;EAC9B,CAAC,EAAE,CAAC1B,oBAAoB,EAAEI,eAAe,CAAC,CAAC;;EAE3C;EACA,MAAM1B,mBAAmB,GAAGvK,WAAW,CAAC,MAAM;IAC5C,IAAI,CAAC4F,IAAI,IAAI,CAACF,cAAc,EAAE;MAC5BwF,OAAO,CAACqB,GAAG,CAAC,mDAAmD,CAAC;MAChE;IACF;IACA1B,2BAA2B,CAACjF,IAAI,EAAEF,cAAc,CAAC;EACnD,CAAC,EAAE,CAACE,IAAI,EAAEF,cAAc,EAAEmF,2BAA2B,CAAC,CAAC;;EAEvD;EACA,MAAME,YAAY,GAAGA,CAACxC,OAAO,EAAEC,QAAQ,GAAG,MAAM,KAAK;IACnDH,WAAW,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO;MAAEC;IAAS,CAAC,CAAC;EAChD,CAAC;EAED,MAAMgF,aAAa,GAAGA,CAAA,KAAM;IAC1BnF,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAEE,IAAI,EAAE;IAAM,CAAC,CAAC;EAC3C,CAAC;EAED,MAAM+B,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIoD,QAAQ,GAAG7H,IAAI;;IAEnB;IACA,IAAII,UAAU,EAAE;MACd,MAAM0H,WAAW,GAAG1H,UAAU,CAAC2H,WAAW,CAAC,CAAC;MAC5CF,QAAQ,GAAGA,QAAQ,CAACX,MAAM,CAAChB,IAAI;QAAA,IAAA8B,eAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,aAAA,EAAAC,aAAA;QAAA,OAC7BlC,IAAI,CAAC/C,OAAO,CAAC4E,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC,MAAAE,eAAA,GAChD9B,IAAI,CAACnF,SAAS,cAAAiH,eAAA,uBAAdA,eAAA,CAAgBD,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC,OAAAG,qBAAA,GACnD/B,IAAI,CAACoC,mBAAmB,cAAAL,qBAAA,uBAAxBA,qBAAA,CAA0BF,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC,OAAAI,qBAAA,GAC7DhC,IAAI,CAACqC,iBAAiB,cAAAL,qBAAA,uBAAtBA,qBAAA,CAAwBH,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC,OAAAK,aAAA,GAC3DjC,IAAI,CAACsC,OAAO,cAAAL,aAAA,uBAAZA,aAAA,CAAcJ,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC,OAAAM,aAAA,GACjDlC,IAAI,CAACuC,OAAO,cAAAL,aAAA,uBAAZA,aAAA,CAAcL,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC;MAAA,CACnD,CAAC;IACH;;IAEA;IACA,IAAIlH,OAAO,CAACE,KAAK,EAAE;MACjB+G,QAAQ,GAAGA,QAAQ,CAACX,MAAM,CAAChB,IAAI,IAAIA,IAAI,CAACE,mBAAmB,KAAKxF,OAAO,CAACE,KAAK,CAAC;IAChF;IACA,IAAIF,OAAO,CAACG,SAAS,EAAE;MACrB8G,QAAQ,GAAGA,QAAQ,CAACX,MAAM,CAAChB,IAAI,IAAIA,IAAI,CAACnF,SAAS,KAAKH,OAAO,CAACG,SAAS,CAAC;IAC1E;;IAEA;IACA,IAAIH,OAAO,CAACU,cAAc,EAAE;MAC1B,IAAIV,OAAO,CAACU,cAAc,KAAK,aAAa,EAAE;QAC5CuG,QAAQ,GAAGA,QAAQ,CAACX,MAAM,CAAChB,IAAI,IAC7BpG,cAAc,CAAC4I,IAAI,CAACvB,IAAI,IAAIA,IAAI,CAAChE,OAAO,KAAK+C,IAAI,CAAC/C,OAAO,CAC3D,CAAC;MACH,CAAC,MAAM,IAAIvC,OAAO,CAACU,cAAc,KAAK,iBAAiB,EAAE;QACvDuG,QAAQ,GAAGA,QAAQ,CAACX,MAAM,CAAChB,IAAI,IAC7B,CAACpG,cAAc,CAAC4I,IAAI,CAACvB,IAAI,IAAIA,IAAI,CAAChE,OAAO,KAAK+C,IAAI,CAAC/C,OAAO,CAC5D,CAAC;MACH;IACF;;IAEA;IACA0E,QAAQ,CAACpC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACtB,IAAIgD,MAAM,GAAGjD,CAAC,CAAChE,MAAM,CAAC;MACtB,IAAIkH,MAAM,GAAGjD,CAAC,CAACjE,MAAM,CAAC;;MAEtB;MACA,IAAIA,MAAM,KAAK,SAAS,EAAE;QACxB,MAAMkE,YAAY,GAAIC,EAAE,IAAK;UAC3B,MAAMC,KAAK,GAAGD,EAAE,CAACC,KAAK,CAAC,OAAO,CAAC;UAC/B,OAAOA,KAAK,GAAGC,QAAQ,CAACD,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;QAC3C,CAAC;QACD,MAAM+C,IAAI,GAAGjD,YAAY,CAAC+C,MAAM,CAAC;QACjC,MAAMG,IAAI,GAAGlD,YAAY,CAACgD,MAAM,CAAC;QAEjC,IAAIhH,SAAS,KAAK,KAAK,EAAE;UACvB,OAAOiH,IAAI,GAAGC,IAAI;QACpB,CAAC,MAAM;UACL,OAAOA,IAAI,GAAGD,IAAI;QACpB;MACF;;MAEA;MACA,IAAI,OAAOF,MAAM,KAAK,QAAQ,EAAE;QAC9BA,MAAM,GAAGA,MAAM,CAACZ,WAAW,CAAC,CAAC;QAC7Ba,MAAM,GAAGA,MAAM,CAACb,WAAW,CAAC,CAAC;MAC/B;MAEA,IAAInG,SAAS,KAAK,KAAK,EAAE;QACvB,OAAO+G,MAAM,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MACjC,CAAC,MAAM;QACL,OAAOD,MAAM,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MACjC;IACF,CAAC,CAAC;IAEFrI,eAAe,CAACsH,QAAQ,CAAC;EAC3B,CAAC;EAED,MAAMnD,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAImD,QAAQ,GAAG/H,cAAc;;IAE7B;IACA,IAAIM,UAAU,EAAE;MACd,MAAM0H,WAAW,GAAG1H,UAAU,CAAC2H,WAAW,CAAC,CAAC;MAC5CF,QAAQ,GAAGA,QAAQ,CAACX,MAAM,CAACC,IAAI;QAAA,IAAA4B,eAAA,EAAAC,qBAAA,EAAAC,UAAA;QAAA,OAC7B9B,IAAI,CAAChE,OAAO,CAAC4E,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC,MAAAiB,eAAA,GAChD5B,IAAI,CAACnG,SAAS,cAAA+H,eAAA,uBAAdA,eAAA,CAAgBhB,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC,OAAAkB,qBAAA,GACnD7B,IAAI,CAAC+B,kBAAkB,cAAAF,qBAAA,uBAAvBA,qBAAA,CAAyBjB,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC,OAAAmB,UAAA,GAC5D9B,IAAI,CAACzD,IAAI,cAAAuF,UAAA,uBAATA,UAAA,CAAWlB,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC;MAAA,CAChD,CAAC;IACH;;IAEA;IACA,IAAIlH,OAAO,CAACI,SAAS,EAAE;MACrB6G,QAAQ,GAAGA,QAAQ,CAACX,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACnG,SAAS,KAAKJ,OAAO,CAACI,SAAS,CAAC;IAC1E;IACA,IAAIJ,OAAO,CAACS,SAAS,EAAE;MACrBwG,QAAQ,GAAGA,QAAQ,CAACX,MAAM,CAACC,IAAI,IAAIA,IAAI,CAAC9F,SAAS,KAAKT,OAAO,CAACS,SAAS,CAAC;IAC1E;IACA,IAAIT,OAAO,CAACQ,aAAa,EAAE;MACzByG,QAAQ,GAAGA,QAAQ,CAACX,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACpD,gBAAgB,KAAKnD,OAAO,CAACQ,aAAa,CAAC;IACrF;IACA,IAAIR,OAAO,CAACK,UAAU,EAAE;MACtB4G,QAAQ,GAAGA,QAAQ,CAACX,MAAM,CAACC,IAAI,IAC7B,IAAIH,IAAI,CAACG,IAAI,CAACC,mBAAmB,CAAC,IAAI,IAAIJ,IAAI,CAACpG,OAAO,CAACK,UAAU,CACnE,CAAC;IACH;IACA,IAAIL,OAAO,CAACM,QAAQ,EAAE;MACpB2G,QAAQ,GAAGA,QAAQ,CAACX,MAAM,CAACC,IAAI,IAC7B,IAAIH,IAAI,CAACG,IAAI,CAACC,mBAAmB,CAAC,IAAI,IAAIJ,IAAI,CAACpG,OAAO,CAACM,QAAQ,CACjE,CAAC;IACH;IACA,IAAIN,OAAO,CAACO,gBAAgB,EAAE;MAC5B,MAAMgI,MAAM,GAAGC,UAAU,CAACxI,OAAO,CAACO,gBAAgB,CAAC;MACnD0G,QAAQ,GAAGA,QAAQ,CAACX,MAAM,CAACC,IAAI,IAC7BiC,UAAU,CAACjC,IAAI,CAAC3D,iBAAiB,CAAC,IAAI2F,MACxC,CAAC;IACH;;IAEA;IACAtB,QAAQ,CAACpC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACtB,IAAIgD,MAAM,GAAGjD,CAAC,CAAChE,MAAM,CAAC;MACtB,IAAIkH,MAAM,GAAGjD,CAAC,CAACjE,MAAM,CAAC;MAEtB,IAAIA,MAAM,KAAK,qBAAqB,EAAE;QACpCiH,MAAM,GAAG,IAAI3B,IAAI,CAAC2B,MAAM,CAAC;QACzBC,MAAM,GAAG,IAAI5B,IAAI,CAAC4B,MAAM,CAAC;MAC3B,CAAC,MAAM,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;QACrCA,MAAM,GAAGA,MAAM,CAACZ,WAAW,CAAC,CAAC;QAC7Ba,MAAM,GAAGA,MAAM,CAACb,WAAW,CAAC,CAAC;MAC/B;MAEA,IAAInG,SAAS,KAAK,KAAK,EAAE;QACvB,OAAO+G,MAAM,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MACjC,CAAC,MAAM;QACL,OAAOD,MAAM,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MACjC;IACF,CAAC,CAAC;IAEFnI,yBAAyB,CAACoH,QAAQ,CAAC;EACrC,CAAC;;EAED;EACA,MAAMwB,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIzJ,SAAS,KAAK,CAAC,EAAE;MACnBuF,YAAY,CAAC,gEAAgE,EAAE,SAAS,CAAC;MACzF;IACF;IACA5C,WAAW,CAAC,CAACD,QAAQ,CAAC;IACtBD,gBAAgB,CAAC,EAAE,CAAC;IACpB8C,YAAY,CACV,CAAC7C,QAAQ,GACL,4EAA4E,GAC5E,gCAAgC,EACpC,MACF,CAAC;EACH,CAAC;EAED,MAAMgH,mBAAmB,GAAIC,MAAM,IAAK;IACtClH,gBAAgB,CAACmH,IAAI,IAAI;MACvB,MAAMC,YAAY,GAAGD,IAAI,CAACnB,QAAQ,CAACkB,MAAM,CAAC,GACtCC,IAAI,CAACtC,MAAM,CAACrB,EAAE,IAAIA,EAAE,KAAK0D,MAAM,CAAC,GAChC,CAAC,GAAGC,IAAI,EAAED,MAAM,CAAC;MAErBpE,YAAY,CACV,GAAGsE,YAAY,CAAC7C,MAAM,6BAA6B,EACnD,MACF,CAAC;MACD,OAAO6C,YAAY;IACrB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI9J,SAAS,KAAK,CAAC,EAAE;IAErB,MAAM+J,MAAM,GAAGnJ,sBAAsB,CAACoJ,GAAG,CAACzC,IAAI,IAAIA,IAAI,CAAC0C,iBAAiB,CAAC;IACzExH,gBAAgB,CAACsH,MAAM,CAAC;IACxBxE,YAAY,CAAC,YAAYwE,MAAM,CAAC/C,MAAM,6BAA6B,EAAE,SAAS,CAAC;EACjF,CAAC;EAED,MAAMkD,cAAc,GAAGA,CAAA,KAAM;IAC3BzH,gBAAgB,CAAC,EAAE,CAAC;IACpB8C,YAAY,CAAC,sBAAsB,EAAE,MAAM,CAAC;EAC9C,CAAC;;EAED;EACA,MAAM4E,iBAAiB,GAAIC,MAAM,IAAK;IACpC,OAAOlK,cAAc,CAAC4I,IAAI,CAACvB,IAAI,IAAIA,IAAI,CAAChE,OAAO,KAAK6G,MAAM,CAAC;EAC7D,CAAC;;EAED;EACA,MAAMC,gCAAgC,GAAI/D,IAAI,IAAK;IACjD,MAAMC,YAAY,GAAGD,IAAI,CAACE,mBAAmB,KAAK,YAAY,IAC1CF,IAAI,CAACE,mBAAmB,KAAK,YAAY,IACzCF,IAAI,CAACE,mBAAmB,KAAK,QAAQ;IAEzD,IAAI,CAACD,YAAY,EAAE;MACjB,OAAO,yEAAyE;IAClF;IAEA,OAAO,+CAA+C;EACxD,CAAC;;EAED;EACA,MAAM+D,eAAe,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;IAC3CvK,YAAY,CAACuK,QAAQ,CAAC;IACtB5I,cAAc,CAAC,CAAC,CAAC;IACjBnB,aAAa,CAAC,EAAE,CAAC;IACjBQ,UAAU,CAAC;MAAEC,KAAK,EAAE,EAAE;MAAEC,SAAS,EAAE,EAAE;MAAEC,SAAS,EAAE;IAAG,CAAC,CAAC;EACzD,CAAC;;EAED;EACA,MAAMqJ,gBAAgB,GAAGA,CAACC,kBAAkB,GAAG,IAAI,KAAK;IACtDrI,aAAa,CAAC,QAAQ,CAAC;IACvBE,eAAe,CAAC,IAAI,CAAC;;IAErB;IACA,IAAImI,kBAAkB,EAAE;MACtBpH,WAAW,CAAC;QACVC,OAAO,EAAEmH,kBAAkB,CAACnH,OAAO;QACnCC,YAAY,EAAE,EAAE;QAChBC,YAAY,EAAE,EAAE;QAChBC,kBAAkB,EAAEgH,kBAAkB,CAACC,eAAe,IAAID,kBAAkB,CAACE,aAAa,IAAI,EAAE;QAChGjH,iBAAiB,EAAE,IAAI;QACvBC,iBAAiB,EAAE,EAAE;QACrBC,iBAAiB,EAAE,IAAI;QACvBC,IAAI,EAAE,EAAE;QACRC,oBAAoB,EAAE,EAAE;QACxBC,OAAO,EAAE,EAAE;QACXC,cAAc,EAAE,EAAE;QAClBC,YAAY,EAAE,EAAE;QAChBC,gBAAgB,EAAE;MACpB,CAAC,CAAC;MACFoB,YAAY,CAAC,QAAQmF,kBAAkB,CAACnH,OAAO,8BAA8B,EAAE,SAAS,CAAC;IAC3F,CAAC,MAAM;MACL;MACAD,WAAW,CAAC;QACVC,OAAO,EAAE,EAAE;QACXC,YAAY,EAAE,EAAE;QAChBC,YAAY,EAAE,EAAE;QAChBC,kBAAkB,EAAE,EAAE;QACtBC,iBAAiB,EAAE,IAAI;QACvBC,iBAAiB,EAAE,EAAE;QACrBC,iBAAiB,EAAE,IAAI;QACvBC,IAAI,EAAE,EAAE;QACRC,oBAAoB,EAAE,EAAE;QACxBC,OAAO,EAAE,EAAE;QACXC,cAAc,EAAE,EAAE;QAClBC,YAAY,EAAE,EAAE;QAChBC,gBAAgB,EAAE;MACpB,CAAC,CAAC;IACJ;IAEAhC,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAM0I,WAAW,GAAGA,CAAA,KAAM;IACxB1I,aAAa,CAAC,KAAK,CAAC;IACpBI,eAAe,CAAC,IAAI,CAAC;IACrBF,aAAa,CAAC,EAAE,CAAC;EACnB,CAAC;;EAED;EACA,MAAMyI,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACzC1H,WAAW,CAACsG,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACmB,KAAK,GAAGC;IACX,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMC,gBAAgB,GAAI3E,IAAI,IAAK;IACjChD,WAAW,CAACsG,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPrG,OAAO,EAAE+C,IAAI,CAAC/C,OAAO;MACrBG,kBAAkB,EAAE4C,IAAI,CAACqE,eAAe,IAAIrE,IAAI,CAACsE,aAAa,IAAI;IACpE,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMM,qBAAqB,GAAG,MAAAA,CAAOC,MAAM,EAAEC,YAAY,GAAG,UAAU,KAAK;IACzE,IAAI;MACF;MACA,MAAMlM,WAAW,GAAG,CAAC,MAAM,MAAM,CAAC,4BAA4B,CAAC,EAAEmM,OAAO;MAExE,IAAIC,iBAAiB,GAAG,KAAK;MAC7B,IAAIC,eAAe,GAAG,KAAK;;MAE3B;MACA,IAAI;QACF,MAAMrM,WAAW,CAACsM,WAAW,CAAC9L,UAAU,EAAEyL,MAAM,EAAE,UAAU,EAAEC,YAAY,CAAC;QAC3EE,iBAAiB,GAAG,IAAI;QACxB5F,OAAO,CAACqB,GAAG,CAAC,sCAAsC,CAAC;MACrD,CAAC,CAAC,OAAOzB,KAAK,EAAE;QACd,IAAIA,KAAK,CAACmG,MAAM,IAAInG,KAAK,CAACmG,MAAM,CAAChD,QAAQ,CAAC,eAAe,CAAC,EAAE;UAC1D6C,iBAAiB,GAAG,IAAI;UACxB5F,OAAO,CAACqB,GAAG,CAAC,6BAA6B,CAAC;QAC5C,CAAC,MAAM;UACLrB,OAAO,CAACJ,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;UAC9D,MAAMA,KAAK;QACb;MACF;;MAEA;MACA,IAAI;QACF,MAAMpG,WAAW,CAACsM,WAAW,CAAC9L,UAAU,EAAEyL,MAAM,EAAE,QAAQ,EAAEC,YAAY,CAAC;QACzEG,eAAe,GAAG,IAAI;QACtB7F,OAAO,CAACqB,GAAG,CAAC,oCAAoC,CAAC;MACnD,CAAC,CAAC,OAAOzB,KAAK,EAAE;QACd,IAAIA,KAAK,CAACmG,MAAM,IAAInG,KAAK,CAACmG,MAAM,CAAChD,QAAQ,CAAC,eAAe,CAAC,EAAE;UAC1D8C,eAAe,GAAG,IAAI;UACtB7F,OAAO,CAACqB,GAAG,CAAC,2BAA2B,CAAC;QAC1C,CAAC,MAAM;UACLrB,OAAO,CAACJ,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;UAC5D,MAAMA,KAAK;QACb;MACF;MAEA,OAAOgG,iBAAiB,IAAIC,eAAe;IAC7C,CAAC,CAAC,OAAOjG,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,MAAMA,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMoG,kCAAkC,GAAG,MAAOpF,IAAI,IAAK;IACzD,IAAI;MACF,MAAMK,YAAY,GAAGL,IAAI,CAACK,YAAY,IAAI,CAAC;MAC3C,MAAMgF,iBAAiB,GAAGhF,YAAY,KAAK,CAAC,GAAG,eAAe,GACrCA,YAAY,KAAK,CAAC,GAAG,yBAAyB,GAC9CA,YAAY,KAAK,CAAC,GAAG,uBAAuB,GAC5C,mBAAmB;MAE5C,MAAMiF,QAAQ,GAAGC,MAAM,CAACC,OAAO,CAC7B,oCAAoCxF,IAAI,CAAC/C,OAAO,MAAM,GACtD,kBAAkBoI,iBAAiB,MAAM,GACzC,kFAAkF,GAClF,iBACF,CAAC;MAED,IAAI,CAACC,QAAQ,EAAE;QACb;MACF;MAEAxI,sBAAsB,CAAC,IAAI,CAAC;MAC5BmC,YAAY,CAAC,8CAA8C,EAAE,MAAM,CAAC;MAEpE,MAAM2F,qBAAqB,CAAC5E,IAAI,CAAC/C,OAAO,EAAE,UAAU,CAAC;MACrDgC,YAAY,CAAC,QAAQe,IAAI,CAAC/C,OAAO,+CAA+C,EAAE,SAAS,CAAC;;MAE5F;MACA,MAAM0B,QAAQ,CAAC,CAAC;IAElB,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzDC,YAAY,CAAC,sCAAsC,IAAID,KAAK,CAACvC,OAAO,IAAI,oBAAoB,CAAC,EAAE,OAAO,CAAC;IACzG,CAAC,SAAS;MACRK,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;;EAED;EACA,MAAM2I,0BAA0B,GAAG,MAAAA,CAAA,KAAY;IAC7C,IAAI;MACF,IAAI,CAAC1I,QAAQ,CAACE,OAAO,IAAI,CAACF,QAAQ,CAACG,YAAY,IAAI,CAACH,QAAQ,CAACI,YAAY,IAAI,CAACJ,QAAQ,CAACO,iBAAiB,EAAE;QACxG2B,YAAY,CAAC,mCAAmC,EAAE,SAAS,CAAC;QAC5D;MACF;;MAEA;MACA,MAAMe,IAAI,GAAGlG,IAAI,CAAC4L,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC1I,OAAO,KAAKF,QAAQ,CAACE,OAAO,CAAC;MAC3D,IAAI,CAAC+C,IAAI,EAAE;QACTf,YAAY,CAAC,kBAAkB,EAAE,OAAO,CAAC;QACzC;MACF;MAEA,IAAI,CAACc,oBAAoB,CAACC,IAAI,CAAC,EAAE;QAC/B,MAAM4F,SAAS,GAAG7B,gCAAgC,CAAC/D,IAAI,CAAC;QACxDf,YAAY,CAAC,oCAAoC2G,SAAS,EAAE,EAAE,OAAO,CAAC;QACtE;MACF;;MAEA;MACA,IAAI/B,iBAAiB,CAAC9G,QAAQ,CAACE,OAAO,CAAC,EAAE;QACvCgC,YAAY,CAAC,iCAAiC,EAAE,SAAS,CAAC;QAC1D;MACF;;MAEA;MACA,IAAI,CAACkB,eAAe,CAACH,IAAI,CAAC,EAAE;QAC1B,MAAMqF,iBAAiB,GAAGrF,IAAI,CAACK,YAAY,KAAK,CAAC,GAAG,eAAe,GAC1CL,IAAI,CAACK,YAAY,KAAK,CAAC,GAAG,yBAAyB,GACnDL,IAAI,CAACK,YAAY,KAAK,CAAC,GAAG,uBAAuB,GACjD,mBAAmB;QAE5C,MAAMiF,QAAQ,GAAGC,MAAM,CAACC,OAAO,CAC7B,uBAAuBxF,IAAI,CAAC/C,OAAO,2CAA2C,GAC9E,uBAAuBoI,iBAAiB,MAAM,GAC9C,gGAAgG,GAChG,iFACF,CAAC;QAED,IAAI,CAACC,QAAQ,EAAE;UACb;QACF;;QAEA;QACA,IAAI;UACFxI,sBAAsB,CAAC,IAAI,CAAC;UAC5BmC,YAAY,CAAC,8CAA8C,EAAE,MAAM,CAAC;UAEpE,MAAM2F,qBAAqB,CAAC5E,IAAI,CAAC/C,OAAO,EAAE,UAAU,CAAC;UACrDgC,YAAY,CAAC,mDAAmD,EAAE,SAAS,CAAC;;UAE5E;UACA,MAAMN,QAAQ,CAAC,CAAC;QAClB,CAAC,CAAC,OAAOK,KAAK,EAAE;UACdC,YAAY,CAAC,sCAAsC,IAAID,KAAK,CAACvC,OAAO,IAAI,oBAAoB,CAAC,EAAE,OAAO,CAAC;UACvG;QACF;MACF;MAEAK,sBAAsB,CAAC,IAAI,CAAC;MAC5B,MAAMnE,qBAAqB,CAACkN,oBAAoB,CAACzM,UAAU,EAAE2D,QAAQ,CAAC;MACtEkC,YAAY,CAAC,oCAAoC,EAAE,SAAS,CAAC;MAC7DsF,WAAW,CAAC,CAAC;MACb,MAAM1F,kBAAkB,CAAC,CAAC;MAC1BJ,mBAAmB,CAAC,CAAC;IACvB,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdC,YAAY,CAAC,+CAA+C,IAAID,KAAK,CAACvC,OAAO,IAAI,oBAAoB,CAAC,EAAE,OAAO,CAAC;IAClH,CAAC,SAAS;MACRK,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;EAED,MAAMgJ,iBAAiB,GAAG,MAAO1K,cAAc,IAAK;IAClD,IAAI;MACF0B,sBAAsB,CAAC,IAAI,CAAC;MAC5BmC,YAAY,CAAC,6BAA6B,EAAE,MAAM,CAAC;MAEnDG,OAAO,CAACqB,GAAG,CAAC,kDAAkD,EAAE;QAC9DkD,iBAAiB,EAAEvI,cAAc,CAACuI,iBAAiB;QACnDX,kBAAkB,EAAE5H,cAAc,CAAC4H,kBAAkB;QACrD/F,OAAO,EAAE7B,cAAc,CAAC6B,OAAO;QAC/B8I,WAAW,EAAE3M;MACf,CAAC,CAAC;MAEF,MAAM4M,QAAQ,GAAG,MAAMrN,qBAAqB,CAACsN,WAAW,CAAC7M,UAAU,EAAEgC,cAAc,CAACuI,iBAAiB,CAAC;MAEtG,IAAIqC,QAAQ,CAACE,QAAQ,EAAE;QACrB;QACA,MAAMC,SAAS,GAAGZ,MAAM,CAAC/I,IAAI,CAACwJ,QAAQ,CAACE,QAAQ,EAAE,QAAQ,CAAC;QAC1D,IAAIC,SAAS,EAAE;UACblH,YAAY,CAAC,6CAA6C,EAAE,SAAS,CAAC;QACxE,CAAC,MAAM;UACL;UACA,MAAMmH,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;UACxCF,IAAI,CAACG,IAAI,GAAGP,QAAQ,CAACE,QAAQ;UAC7BE,IAAI,CAACI,QAAQ,GAAG,kBAAkBpL,cAAc,CAAC4H,kBAAkB,MAAM;UACzEqD,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;UAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;UACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;UAC/BnH,YAAY,CAAC,uCAAuC,EAAE,SAAS,CAAC;QAClE;MACF,CAAC,MAAM,IAAI+G,QAAQ,CAACa,WAAW,EAAE;QAC/B;QACA,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACC,IAAI,CAAChB,QAAQ,CAACa,WAAW,CAAC,CAAC,EAAE;UAAEI,IAAI,EAAE;QAAkB,CAAC,CAAC;QAChF,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACN,IAAI,CAAC;QACrC,MAAMV,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACxCF,IAAI,CAACG,IAAI,GAAGW,GAAG;QACfd,IAAI,CAACI,QAAQ,GAAG,kBAAkBpL,cAAc,CAAC4H,kBAAkB,MAAM;QACzEqD,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;QAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;QACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;QAC/Be,GAAG,CAACE,eAAe,CAACH,GAAG,CAAC;QACxBjI,YAAY,CAAC,4BAA4B,EAAE,SAAS,CAAC;MACvD,CAAC,MAAM;QACLA,YAAY,CAAC,sCAAsC,EAAE,OAAO,CAAC;MAC/D;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CC,YAAY,CAAC,oCAAoC,IAAID,KAAK,CAACvC,OAAO,IAAI,oBAAoB,CAAC,EAAE,OAAO,CAAC;IACvG,CAAC,SAAS;MACRK,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;EAED,MAAMwK,0BAA0B,GAAG,MAAOlM,cAAc,IAAK;IAC3D,IAAImK,MAAM,CAACC,OAAO,CAAC,mDAAmDpK,cAAc,CAAC4H,kBAAkB,GAAG,CAAC,EAAE;MAC3G,IAAI;QACFlG,sBAAsB,CAAC,IAAI,CAAC;QAC5B,MAAMnE,qBAAqB,CAAC4O,oBAAoB,CAACnO,UAAU,EAAEgC,cAAc,CAACuI,iBAAiB,CAAC;QAC9F1E,YAAY,CAAC,uCAAuC,EAAE,SAAS,CAAC;QAChE,MAAMJ,kBAAkB,CAAC,CAAC;QAC1BJ,mBAAmB,CAAC,CAAC;MACvB,CAAC,CAAC,OAAOO,KAAK,EAAE;QACdC,YAAY,CAAC,kDAAkD,IAAID,KAAK,CAACvC,OAAO,IAAI,oBAAoB,CAAC,EAAE,OAAO,CAAC;MACrH,CAAC,SAAS;QACRK,sBAAsB,CAAC,KAAK,CAAC;MAC/B;IACF;EACF,CAAC;;EAED;EACA,MAAM0K,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAItL,aAAa,CAACwE,MAAM,KAAK,CAAC,EAAE;MAC9BzB,YAAY,CAAC,8BAA8B,EAAE,SAAS,CAAC;MACvD;IACF;IAEA,IAAIsG,MAAM,CAACC,OAAO,CAAC,iCAAiCtJ,aAAa,CAACwE,MAAM,kBAAkB,CAAC,EAAE;MAC3F,IAAI;QACF5D,sBAAsB,CAAC,IAAI,CAAC;QAC5B,KAAK,MAAM6C,EAAE,IAAIzD,aAAa,EAAE;UAC9B,MAAMvD,qBAAqB,CAAC4O,oBAAoB,CAACnO,UAAU,EAAEuG,EAAE,CAAC;QAClE;QACAV,YAAY,CAAC,GAAG/C,aAAa,CAACwE,MAAM,wCAAwC,EAAE,SAAS,CAAC;QACxFvE,gBAAgB,CAAC,EAAE,CAAC;QACpB,MAAM0C,kBAAkB,CAAC,CAAC;QAC1BJ,mBAAmB,CAAC,CAAC;MACvB,CAAC,CAAC,OAAOO,KAAK,EAAE;QACdC,YAAY,CAAC,gDAAgD,EAAE,OAAO,CAAC;MACzE,CAAC,SAAS;QACRnC,sBAAsB,CAAC,KAAK,CAAC;MAC/B;IACF;EACF,CAAC;EAED,MAAM2K,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAIvL,aAAa,CAACwE,MAAM,KAAK,CAAC,EAAE;MAC9BzB,YAAY,CAAC,8BAA8B,EAAE,SAAS,CAAC;MACvD;IACF;IAEA,IAAI;MACFnC,sBAAsB,CAAC,IAAI,CAAC;MAC5B;MACA,MAAM4K,aAAa,GAAG9N,cAAc,CAACoH,MAAM,CAACC,IAAI,IAC9C/E,aAAa,CAACiG,QAAQ,CAAClB,IAAI,CAAC0C,iBAAiB,CAC/C,CAAC;;MAED;MACA,MAAMgE,UAAU,GAAGC,WAAW,CAACF,aAAa,CAAC;MAC7CG,WAAW,CAACF,UAAU,EAAE,kBAAkB,IAAI7G,IAAI,CAAC,CAAC,CAACgH,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;MAEvF9I,YAAY,CAAC,GAAG/C,aAAa,CAACwE,MAAM,2BAA2B,EAAE,SAAS,CAAC;IAC7E,CAAC,CAAC,OAAO1B,KAAK,EAAE;MACdC,YAAY,CAAC,2BAA2B,EAAE,OAAO,CAAC;IACpD,CAAC,SAAS;MACRnC,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;;EAED;EACA,MAAM8K,WAAW,GAAI1I,IAAI,IAAK;IAC5B,MAAM8I,OAAO,GAAG,CAAC,SAAS,EAAE,oBAAoB,EAAE,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,WAAW,CAAC;IAC3H,MAAMC,IAAI,GAAG/I,IAAI,CAACwE,GAAG,CAACzC,IAAI,IAAI,CAC5BA,IAAI,CAAChE,OAAO,EACZgE,IAAI,CAAC+B,kBAAkB,EACvB,IAAIlC,IAAI,CAACG,IAAI,CAACC,mBAAmB,CAAC,CAACgH,kBAAkB,CAAC,CAAC,EACvDjH,IAAI,CAACnG,SAAS,EACdmG,IAAI,CAAC9F,SAAS,EACd8F,IAAI,CAAC7D,kBAAkB,EACvB6D,IAAI,CAAC3D,iBAAiB,EACtB2D,IAAI,CAACpD,gBAAgB,CACtB,CAAC;IAEF,OAAO,CAACmK,OAAO,EAAE,GAAGC,IAAI,CAAC,CAACvE,GAAG,CAACyE,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,CAACA,IAAI,CAAC,IAAI,CAAC;EAChE,CAAC;EAED,MAAMP,WAAW,GAAGA,CAACQ,OAAO,EAAEC,QAAQ,KAAK;IACzC,MAAMxB,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACsB,OAAO,CAAC,EAAE;MAAEpB,IAAI,EAAE;IAA0B,CAAC,CAAC;IACrE,MAAMb,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxC,IAAIF,IAAI,CAACI,QAAQ,KAAK+B,SAAS,EAAE;MAC/B,MAAMrB,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACN,IAAI,CAAC;MACrCV,IAAI,CAACoC,YAAY,CAAC,MAAM,EAAEtB,GAAG,CAAC;MAC9Bd,IAAI,CAACoC,YAAY,CAAC,UAAU,EAAEF,QAAQ,CAAC;MACvClC,IAAI,CAACqC,KAAK,CAACC,UAAU,GAAG,QAAQ;MAChCrC,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;MACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;IACjC;EACF,CAAC;EAED,MAAMuC,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMhB,UAAU,GAAGC,WAAW,CAACtN,sBAAsB,CAAC;IACtDuN,WAAW,CAACF,UAAU,EAAE,wBAAwB,IAAI7G,IAAI,CAAC,CAAC,CAACgH,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IAC7F9I,YAAY,CAAC,yBAAyB,EAAE,SAAS,CAAC;EACpD,CAAC;;EAED;EACA7K,mBAAmB,CAACmF,GAAG,EAAE,OAAO;IAC9BqP,kBAAkB,EAAGC,MAAM,IAAK;MAC9B,IAAIA,MAAM,KAAK,oBAAoB,EAAE;QACnC1E,gBAAgB,CAAC,CAAC;MACpB,CAAC,MAAM,IAAI0E,MAAM,KAAK,0BAA0B,EAAE;QAChDlP,YAAY,CAAC,CAAC,CAAC;MACjB;IACF,CAAC;IACDmP,0BAA0B,EAAG9I,IAAI,IAAK;MACpCmE,gBAAgB,CAACnE,IAAI,CAAC;IACxB,CAAC;IACD+I,wBAAwB,EAAG/I,IAAI,IAAK;MAClC;MACA,MAAM5E,cAAc,GAAGxB,cAAc,CAAC8L,IAAI,CAACzE,IAAI,IAAIA,IAAI,CAAChE,OAAO,KAAK+C,IAAI,CAAC/C,OAAO,CAAC;MACjF,IAAI7B,cAAc,EAAE;QAClBa,eAAe,CAACb,cAAc,CAAC;QAC/BW,aAAa,CAAC,MAAM,CAAC;QACrBF,aAAa,CAAC,IAAI,CAAC;MACrB,CAAC,MAAM;QACLoD,YAAY,CAAC,4CAA4C,EAAE,OAAO,CAAC;MACrE;IACF,CAAC;IACD+J,kBAAkB,EAAGhJ,IAAI,IAAK;MAC5B;MACAZ,OAAO,CAACqB,GAAG,CAAC,mDAAmDT,IAAI,CAAC/C,OAAO,EAAE,CAAC;MAC9EmC,OAAO,CAACqB,GAAG,CAAC,4CAA4C,EAAE7G,cAAc,CAAC8J,GAAG,CAACiC,CAAC,KAAK;QAAEhG,EAAE,EAAEgG,CAAC,CAAChC,iBAAiB;QAAE3D,IAAI,EAAE2F,CAAC,CAAC1I;MAAQ,CAAC,CAAC,CAAC,CAAC;MAElI,MAAM7B,cAAc,GAAGxB,cAAc,CAAC8L,IAAI,CAACzE,IAAI,IAAIA,IAAI,CAAChE,OAAO,KAAK+C,IAAI,CAAC/C,OAAO,CAAC;MACjF,IAAI7B,cAAc,EAAE;QAClBgE,OAAO,CAACqB,GAAG,CAAC,wCAAwC,EAAErF,cAAc,CAAC;QACrE0K,iBAAiB,CAAC1K,cAAc,CAAC;MACnC,CAAC,MAAM;QACLgE,OAAO,CAACqB,GAAG,CAAC,yDAAyDT,IAAI,CAAC/C,OAAO,EAAE,CAAC;QACpFgC,YAAY,CAAC,4CAA4C,EAAE,OAAO,CAAC;MACrE;IACF;EACF,CAAC,CAAC,CAAC;;EAEH;EACA,MAAMgK,mBAAmB,GAAIC,KAAK,IAAK;IACrC,MAAMC,UAAU,GAAG,CAAC9N,WAAW,GAAG,CAAC,IAAIE,YAAY;IACnD,MAAM6N,QAAQ,GAAGD,UAAU,GAAG5N,YAAY;IAC1C,OAAO2N,KAAK,CAACG,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;EAC1C,CAAC;EAED,MAAME,aAAa,GAAIJ,KAAK,IAAKvI,IAAI,CAAC4I,IAAI,CAACL,KAAK,CAACxI,MAAM,GAAGnF,YAAY,CAAC;;EAIvE;EACA,MAAMiO,eAAe,GAAGA,CAAA,kBACtB1Q,OAAA,CAACtE,KAAK;IAACiV,EAAE,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,EAAE,EAAE,CAAC;MAAEC,OAAO,EAAE;IAAU,CAAE;IAAAC,QAAA,eAC7C/Q,OAAA,CAAC1C,KAAK;MAAC0T,SAAS,EAAC,KAAK;MAACC,OAAO,EAAE,CAAE;MAACC,UAAU,EAAC,QAAQ;MAACC,cAAc,EAAC,eAAe;MAACC,QAAQ,EAAC,MAAM;MAAAL,QAAA,gBAEnG/Q,OAAA,CAAC1C,KAAK;QAAC0T,SAAS,EAAC,KAAK;QAACE,UAAU,EAAC,QAAQ;QAACD,OAAO,EAAE,CAAE;QAAAF,QAAA,gBACpD/Q,OAAA,CAACV,SAAS;UAAC+R,KAAK,EAAC,SAAS;UAACC,QAAQ,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9C1R,OAAA,CAACzE,GAAG;UAAAwV,QAAA,gBACF/Q,OAAA,CAACxE,UAAU;YAACmW,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAACjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAE,CAAE;YAAAd,QAAA,EAC9D/L,UAAU,CAACE;UAAU;YAAAqM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACb1R,OAAA,CAACxE,UAAU;YAACmW,OAAO,EAAC,SAAS;YAACN,KAAK,EAAC,gBAAgB;YAAAN,QAAA,EAAC;UAErD;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAER1R,OAAA,CAAC1C,KAAK;QAAC0T,SAAS,EAAC,KAAK;QAACE,UAAU,EAAC,QAAQ;QAACD,OAAO,EAAE,CAAE;QAAAF,QAAA,gBACpD/Q,OAAA,CAAC5B,SAAS;UAACiT,KAAK,EAAC,SAAS;UAACC,QAAQ,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9C1R,OAAA,CAACzE,GAAG;UAAAwV,QAAA,gBACF/Q,OAAA,CAACxE,UAAU;YAACmW,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAACjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAE,CAAE;YAAAd,QAAA,EAC9D/L,UAAU,CAACG;UAAe;YAAAoM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACb1R,OAAA,CAACxE,UAAU;YAACmW,OAAO,EAAC,SAAS;YAACN,KAAK,EAAC,gBAAgB;YAAAN,QAAA,EAAC;UAErD;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAER1R,OAAA,CAAC1C,KAAK;QAAC0T,SAAS,EAAC,KAAK;QAACE,UAAU,EAAC,QAAQ;QAACD,OAAO,EAAE,CAAE;QAAAF,QAAA,gBACpD/Q,OAAA,CAACR,SAAS;UAAC6R,KAAK,EAAC,MAAM;UAACC,QAAQ,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3C1R,OAAA,CAACzE,GAAG;UAAAwV,QAAA,gBACF/Q,OAAA,CAACxE,UAAU;YAACmW,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAACjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAE,CAAE;YAAAd,QAAA,EAC9D/L,UAAU,CAACwD;UAAiB;YAAA+I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACb1R,OAAA,CAACxE,UAAU;YAACmW,OAAO,EAAC,SAAS;YAACN,KAAK,EAAC,gBAAgB;YAAAN,QAAA,EAAC;UAErD;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAER1R,OAAA,CAAC1C,KAAK;QAAC0T,SAAS,EAAC,KAAK;QAACE,UAAU,EAAC,QAAQ;QAACD,OAAO,EAAE,CAAE;QAAAF,QAAA,gBACpD/Q,OAAA,CAACzE,GAAG;UAACoV,EAAE,EAAE;YACPmB,KAAK,EAAE,EAAE;YACTC,MAAM,EAAE,EAAE;YACVC,YAAY,EAAE,KAAK;YACnBlB,OAAO,EAAE9L,UAAU,CAACK,wBAAwB,IAAI,EAAE,GAAG,cAAc,GAC1DL,UAAU,CAACK,wBAAwB,IAAI,EAAE,GAAG,cAAc,GAAG,YAAY;YAClF4M,OAAO,EAAE,MAAM;YACff,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAJ,QAAA,eACA/Q,OAAA,CAACxE,UAAU;YAACmW,OAAO,EAAC,SAAS;YAACC,UAAU,EAAC,MAAM;YAACP,KAAK,EAAC,OAAO;YAAAN,QAAA,GAC1D/L,UAAU,CAACK,wBAAwB,EAAC,GACvC;UAAA;YAAAkM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACN1R,OAAA,CAACzE,GAAG;UAAAwV,QAAA,gBACF/Q,OAAA,CAACxE,UAAU;YAACmW,OAAO,EAAC,OAAO;YAACC,UAAU,EAAC,QAAQ;YAACjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAE,CAAE;YAAAd,QAAA,EAAC;UAEvE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb1R,OAAA,CAACxE,UAAU;YAACmW,OAAO,EAAC,SAAS;YAACN,KAAK,EAAC,gBAAgB;YAAAN,QAAA,GACjD/L,UAAU,CAACM,kBAAkB,EAAC,OACjC;UAAA;YAAAiM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACR;;EAED;EACA,MAAMQ,sBAAsB,GAAGA,CAAA,kBAC7BlS,OAAA,CAACtE,KAAK;IAACiV,EAAE,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAE,QAAA,gBACzB/Q,OAAA,CAACrE,IAAI;MAACwW,SAAS;MAAClB,OAAO,EAAE,CAAE;MAACC,UAAU,EAAC,QAAQ;MAAAH,QAAA,gBAC7C/Q,OAAA,CAACrE,IAAI;QAACyW,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAvB,QAAA,eACvB/Q,OAAA,CAAC9D,SAAS;UACRqW,SAAS;UACTC,WAAW,EAAC,0CAA0C;UACtD5G,KAAK,EAAExK,UAAW;UAClBqR,QAAQ,EAAGC,CAAC,IAAKrR,aAAa,CAACqR,CAAC,CAACC,MAAM,CAAC/G,KAAK,CAAE;UAC/CgH,UAAU,EAAE;YACVC,cAAc,eACZ7S,OAAA,CAAC5C,cAAc;cAAC0V,QAAQ,EAAC,OAAO;cAAA/B,QAAA,eAC9B/Q,OAAA,CAAChC,UAAU;gBAAAuT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CACjB;YACDqB,YAAY,EAAE3R,UAAU,iBACtBpB,OAAA,CAAC5C,cAAc;cAAC0V,QAAQ,EAAC,KAAK;cAAA/B,QAAA,eAC5B/Q,OAAA,CAAChD,UAAU;gBAACgW,OAAO,EAAEA,CAAA,KAAM3R,aAAa,CAAC,EAAE,CAAE;gBAAC4R,IAAI,EAAC,OAAO;gBAAAlC,QAAA,eACxD/Q,OAAA,CAAC9B,SAAS;kBAAAqT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAEpB;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEP1R,OAAA,CAACrE,IAAI;QAACyW,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAvB,QAAA,eACvB/Q,OAAA,CAACvE,MAAM;UACL8W,SAAS;UACTZ,OAAO,EAAC,UAAU;UAClBqB,OAAO,EAAEA,CAAA,KAAMrR,sBAAsB,CAAC,CAACD,mBAAmB,CAAE;UAC5D2P,KAAK,EAAE6B,MAAM,CAACC,MAAM,CAACvR,OAAO,CAAC,CAAC8H,IAAI,CAAC0J,CAAC,IAAIA,CAAC,CAAC,GAAG,SAAS,GAAG,SAAU;UAAArC,QAAA,GACpE,SACQ,EAACmC,MAAM,CAACC,MAAM,CAACvR,OAAO,CAAC,CAACsG,MAAM,CAACkL,CAAC,IAAIA,CAAC,CAAC,CAACxL,MAAM,GAAG,CAAC,IAAI,IAAIsL,MAAM,CAACC,MAAM,CAACvR,OAAO,CAAC,CAACsG,MAAM,CAACkL,CAAC,IAAIA,CAAC,CAAC,CAACxL,MAAM,GAAG;QAAA;UAAA2J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEP1R,OAAA,CAACrE,IAAI;QAACyW,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAvB,QAAA,eACvB/Q,OAAA,CAACvE,MAAM;UACL8W,SAAS;UACTZ,OAAO,EAAC,UAAU;UAClBqB,OAAO,EAAE3I,cAAe;UACxBgH,KAAK,EAAE/N,QAAQ,GAAG,WAAW,GAAG,SAAU;UAC1C+P,QAAQ,EAAEzS,SAAS,KAAK,CAAE;UAAAmQ,QAAA,EAEzBzN,QAAQ,GAAG,gBAAgB,GAAG;QAAoB;UAAAiO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEP1R,OAAA,CAACrE,IAAI;QAACyW,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAvB,QAAA,eACvB/Q,OAAA,CAACvE,MAAM;UACL8W,SAAS;UACTZ,OAAO,EAAC,UAAU;UAClBqB,OAAO,EAAEnD,eAAgB;UACzBwD,QAAQ,EAAEzS,SAAS,KAAK,CAAC,IAAIY,sBAAsB,CAACoG,MAAM,KAAK,CAAE;UAAAmJ,QAAA,EAEhEnQ,SAAS,KAAK,CAAC,GAAG,8BAA8B,GAAG;QAAwB;UAAA2Q,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEP1R,OAAA,CAACrE,IAAI;QAACyW,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAvB,QAAA,eACvB/Q,OAAA,CAACvE,MAAM;UACL8W,SAAS;UACTZ,OAAO,EAAC,WAAW;UACnBqB,OAAO,EAAE3H,gBAAiB;UAAA0F,QAAA,EAC3B;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGP1R,OAAA,CAACtC,QAAQ;MAAC4V,EAAE,EAAE5R,mBAAoB;MAAAqP,QAAA,gBAChC/Q,OAAA,CAAC3C,OAAO;QAACsT,EAAE,EAAE;UAAE4C,EAAE,EAAE;QAAE;MAAE;QAAAhC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1B1R,OAAA,CAACxE,UAAU;QAACmW,OAAO,EAAC,OAAO;QAACN,KAAK,EAAC,gBAAgB;QAACV,EAAE,EAAE;UAAEE,EAAE,EAAE;QAAE,CAAE;QAAAE,QAAA,EAC9DnQ,SAAS,KAAK,CAAC,GAAG,iBAAiB,GAAG;MAA2B;QAAA2Q,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC,eAEb1R,OAAA,CAACrE,IAAI;QAACwW,SAAS;QAAClB,OAAO,EAAE,CAAE;QAAAF,QAAA,GAExBnQ,SAAS,KAAK,CAAC,iBACdZ,OAAA,CAAAE,SAAA;UAAA6Q,QAAA,gBACE/Q,OAAA,CAACrE,IAAI;YAACyW,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACvB/Q,OAAA,CAAC7D,WAAW;cAACoW,SAAS;cAACU,IAAI,EAAC,OAAO;cAAAlC,QAAA,gBACjC/Q,OAAA,CAAC5D,UAAU;gBAAA2U,QAAA,EAAC;cAAmB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC5C1R,OAAA,CAAC3D,MAAM;gBACLuP,KAAK,EAAEhK,OAAO,CAACE,KAAM;gBACrB2Q,QAAQ,EAAGC,CAAC,IAAK7Q,UAAU,CAAC;kBAAC,GAAGD,OAAO;kBAAEE,KAAK,EAAE4Q,CAAC,CAACC,MAAM,CAAC/G;gBAAK,CAAC,CAAE;gBAAAmF,QAAA,gBAEjE/Q,OAAA,CAAC1D,QAAQ;kBAACsP,KAAK,EAAC,EAAE;kBAAAmF,QAAA,EAAC;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACnC1R,OAAA,CAAC1D,QAAQ;kBAACsP,KAAK,EAAC,YAAY;kBAAAmF,QAAA,EAAC;gBAAU;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAClD1R,OAAA,CAAC1D,QAAQ;kBAACsP,KAAK,EAAC,gBAAgB;kBAAAmF,QAAA,EAAC;gBAAc;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAC1D1R,OAAA,CAAC1D,QAAQ;kBAACsP,KAAK,EAAC,UAAU;kBAAAmF,QAAA,EAAC;gBAAQ;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEP1R,OAAA,CAACrE,IAAI;YAACyW,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACvB/Q,OAAA,CAAC7D,WAAW;cAACoW,SAAS;cAACU,IAAI,EAAC,OAAO;cAAAlC,QAAA,gBACjC/Q,OAAA,CAAC5D,UAAU;gBAAA2U,QAAA,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAClC1R,OAAA,CAAC3D,MAAM;gBACLuP,KAAK,EAAEhK,OAAO,CAACG,SAAU;gBACzB0Q,QAAQ,EAAGC,CAAC,IAAK7Q,UAAU,CAAC;kBAAC,GAAGD,OAAO;kBAAEG,SAAS,EAAE2Q,CAAC,CAACC,MAAM,CAAC/G;gBAAK,CAAC,CAAE;gBAAAmF,QAAA,gBAErE/Q,OAAA,CAAC1D,QAAQ;kBAACsP,KAAK,EAAC,EAAE;kBAAAmF,QAAA,EAAC;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,EAClC,CAAC,GAAG,IAAI8B,GAAG,CAACxS,IAAI,CAAC4J,GAAG,CAACiC,CAAC,IAAIA,CAAC,CAAC9K,SAAS,CAAC,CAAC,CAAC,CAACmG,MAAM,CAACuL,OAAO,CAAC,CAAC7I,GAAG,CAAC8I,GAAG,iBAC/D1T,OAAA,CAAC1D,QAAQ;kBAAWsP,KAAK,EAAE8H,GAAI;kBAAA3C,QAAA,EAAE2C;gBAAG,GAArBA,GAAG;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA6B,CAChD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEP1R,OAAA,CAACrE,IAAI;YAACyW,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACvB/Q,OAAA,CAAC7D,WAAW;cAACoW,SAAS;cAACU,IAAI,EAAC,OAAO;cAAAlC,QAAA,gBACjC/Q,OAAA,CAAC5D,UAAU;gBAAA2U,QAAA,EAAC;cAAoB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC7C1R,OAAA,CAAC3D,MAAM;gBACLuP,KAAK,EAAEhK,OAAO,CAACU,cAAe;gBAC9BmQ,QAAQ,EAAGC,CAAC,IAAK7Q,UAAU,CAAC;kBAAC,GAAGD,OAAO;kBAAEU,cAAc,EAAEoQ,CAAC,CAACC,MAAM,CAAC/G;gBAAK,CAAC,CAAE;gBAAAmF,QAAA,gBAE1E/Q,OAAA,CAAC1D,QAAQ;kBAACsP,KAAK,EAAC,EAAE;kBAAAmF,QAAA,EAAC;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACnC1R,OAAA,CAAC1D,QAAQ;kBAACsP,KAAK,EAAC,aAAa;kBAAAmF,QAAA,EAAC;gBAAW;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACpD1R,OAAA,CAAC1D,QAAQ;kBAACsP,KAAK,EAAC,iBAAiB;kBAAAmF,QAAA,EAAC;gBAAe;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA,eACP,CACH,EAGA9Q,SAAS,KAAK,CAAC,iBACdZ,OAAA,CAAAE,SAAA;UAAA6Q,QAAA,gBACE/Q,OAAA,CAACrE,IAAI;YAACyW,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACvB/Q,OAAA,CAAC7D,WAAW;cAACoW,SAAS;cAACU,IAAI,EAAC,OAAO;cAAAlC,QAAA,gBACjC/Q,OAAA,CAAC5D,UAAU;gBAAA2U,QAAA,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAClC1R,OAAA,CAAC3D,MAAM;gBACLuP,KAAK,EAAEhK,OAAO,CAACI,SAAU;gBACzByQ,QAAQ,EAAGC,CAAC,IAAK7Q,UAAU,CAAC;kBAAC,GAAGD,OAAO;kBAAEI,SAAS,EAAE0Q,CAAC,CAACC,MAAM,CAAC/G;gBAAK,CAAC,CAAE;gBAAAmF,QAAA,gBAErE/Q,OAAA,CAAC1D,QAAQ;kBAACsP,KAAK,EAAC,EAAE;kBAAAmF,QAAA,EAAC;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,EAClC,CAAC,GAAG,IAAI8B,GAAG,CAAC1S,cAAc,CAAC8J,GAAG,CAACiC,CAAC,IAAIA,CAAC,CAAC7K,SAAS,CAAC,CAAC,CAAC,CAACkG,MAAM,CAACuL,OAAO,CAAC,CAAC7I,GAAG,CAAC+I,EAAE,iBACxE3T,OAAA,CAAC1D,QAAQ;kBAAUsP,KAAK,EAAE+H,EAAG;kBAAA5C,QAAA,EAAE4C;gBAAE,GAAlBA,EAAE;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA2B,CAC7C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEP1R,OAAA,CAACrE,IAAI;YAACyW,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACvB/Q,OAAA,CAAC7D,WAAW;cAACoW,SAAS;cAACU,IAAI,EAAC,OAAO;cAAAlC,QAAA,gBACjC/Q,OAAA,CAAC5D,UAAU;gBAAA2U,QAAA,EAAC;cAAc;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACvC1R,OAAA,CAAC3D,MAAM;gBACLuP,KAAK,EAAEhK,OAAO,CAACQ,aAAc;gBAC7BqQ,QAAQ,EAAGC,CAAC,IAAK7Q,UAAU,CAAC;kBAAC,GAAGD,OAAO;kBAAEQ,aAAa,EAAEsQ,CAAC,CAACC,MAAM,CAAC/G;gBAAK,CAAC,CAAE;gBAAAmF,QAAA,gBAEzE/Q,OAAA,CAAC1D,QAAQ;kBAACsP,KAAK,EAAC,EAAE;kBAAAmF,QAAA,EAAC;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACnC1R,OAAA,CAAC1D,QAAQ;kBAACsP,KAAK,EAAC,UAAU;kBAAAmF,QAAA,EAAC;gBAAQ;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAC9C1R,OAAA,CAAC1D,QAAQ;kBAACsP,KAAK,EAAC,cAAc;kBAAAmF,QAAA,EAAC;gBAAY;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACtD1R,OAAA,CAAC1D,QAAQ;kBAACsP,KAAK,EAAC,eAAe;kBAAAmF,QAAA,EAAC;gBAAa;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEP1R,OAAA,CAACrE,IAAI;YAACyW,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACvB/Q,OAAA,CAAC9D,SAAS;cACRqW,SAAS;cACTU,IAAI,EAAC,OAAO;cACZW,KAAK,EAAC,0BAAqB;cAC3BzF,IAAI,EAAC,QAAQ;cACbvC,KAAK,EAAEhK,OAAO,CAACO,gBAAiB;cAChCsQ,QAAQ,EAAGC,CAAC,IAAK7Q,UAAU,CAAC;gBAAC,GAAGD,OAAO;gBAAEO,gBAAgB,EAAEuQ,CAAC,CAACC,MAAM,CAAC/G;cAAK,CAAC,CAAE;cAC5E4G,WAAW,EAAC;YAAS;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEP1R,OAAA,CAACrE,IAAI;YAACyW,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACvB/Q,OAAA,CAAC9D,SAAS;cACRqW,SAAS;cACTU,IAAI,EAAC,OAAO;cACZW,KAAK,EAAC,aAAa;cACnBzF,IAAI,EAAC,MAAM;cACXvC,KAAK,EAAEhK,OAAO,CAACK,UAAW;cAC1BwQ,QAAQ,EAAGC,CAAC,IAAK7Q,UAAU,CAAC;gBAAC,GAAGD,OAAO;gBAAEK,UAAU,EAAEyQ,CAAC,CAACC,MAAM,CAAC/G;cAAK,CAAC,CAAE;cACtEiI,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEP1R,OAAA,CAACrE,IAAI;YAACyW,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACvB/Q,OAAA,CAAC9D,SAAS;cACRqW,SAAS;cACTU,IAAI,EAAC,OAAO;cACZW,KAAK,EAAC,WAAW;cACjBzF,IAAI,EAAC,MAAM;cACXvC,KAAK,EAAEhK,OAAO,CAACM,QAAS;cACxBuQ,QAAQ,EAAGC,CAAC,IAAK7Q,UAAU,CAAC;gBAAC,GAAGD,OAAO;gBAAEM,QAAQ,EAAEwQ,CAAC,CAACC,MAAM,CAAC/G;cAAK,CAAC,CAAE;cACpEiI,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,eACP,CACH,eAED1R,OAAA,CAACrE,IAAI;UAACyW,IAAI;UAACC,EAAE,EAAE,EAAG;UAAAtB,QAAA,eAChB/Q,OAAA,CAAC1C,KAAK;YAAC0T,SAAS,EAAC,KAAK;YAACC,OAAO,EAAE,CAAE;YAACE,cAAc,EAAC,UAAU;YAAAJ,QAAA,eAC1D/Q,OAAA,CAACvE,MAAM;cACLkW,OAAO,EAAC,UAAU;cAClBsB,IAAI,EAAC,OAAO;cACZD,OAAO,EAAEA,CAAA,KAAMnR,UAAU,CAAC;gBACxBC,KAAK,EAAE,EAAE;gBAAEC,SAAS,EAAE,EAAE;gBAAEC,SAAS,EAAE,EAAE;gBAAEC,UAAU,EAAE,EAAE;gBACvDC,QAAQ,EAAE,EAAE;gBAAEC,gBAAgB,EAAE,EAAE;gBAAEC,aAAa,EAAE,EAAE;gBAAEC,SAAS,EAAE,EAAE;gBACpEC,cAAc,EAAE;cAClB,CAAC,CAAE;cAAAyO,QAAA,EACJ;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGVpO,QAAQ,IAAIF,aAAa,CAACwE,MAAM,GAAG,CAAC,iBACnC5H,OAAA,CAAAE,SAAA;MAAA6Q,QAAA,gBACE/Q,OAAA,CAAC3C,OAAO;QAACsT,EAAE,EAAE;UAAE4C,EAAE,EAAE;QAAE;MAAE;QAAAhC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1B1R,OAAA,CAAC1C,KAAK;QAAC0T,SAAS,EAAC,KAAK;QAACC,OAAO,EAAE,CAAE;QAACC,UAAU,EAAC,QAAQ;QAAAH,QAAA,gBACpD/Q,OAAA,CAACxE,UAAU;UAACmW,OAAO,EAAC,OAAO;UAAAZ,QAAA,GACxB3N,aAAa,CAACwE,MAAM,EAAC,uBACxB;QAAA;UAAA2J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb1R,OAAA,CAACvE,MAAM;UACLwX,IAAI,EAAC,OAAO;UACZtB,OAAO,EAAC,UAAU;UAClBqB,OAAO,EAAEtI,cAAe;UAAAqG,QAAA,EACzB;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT1R,OAAA,CAACvE,MAAM;UACLwX,IAAI,EAAC,OAAO;UACZtB,OAAO,EAAC,UAAU;UAClBqB,OAAO,EAAElI,cAAe;UAAAiG,QAAA,EACzB;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT1R,OAAA,CAACvE,MAAM;UACLwX,IAAI,EAAC,OAAO;UACZtB,OAAO,EAAC,UAAU;UAClBqB,OAAO,EAAErE,gBAAiB;UAAAoC,QAAA,EAC3B;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT1R,OAAA,CAACvE,MAAM;UACLwX,IAAI,EAAC,OAAO;UACZtB,OAAO,EAAC,UAAU;UAClBN,KAAK,EAAC,OAAO;UACb2B,OAAO,EAAEtE,gBAAiB;UAAAqC,QAAA,EAC3B;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA,eACR,CACH;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CACR;;EAED;EACA,MAAMqC,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,YAAY,GAAG7D,mBAAmB,CAAC7O,YAAY,CAAC;IAEtD,IAAIA,YAAY,CAACsG,MAAM,KAAK,CAAC,EAAE;MAC7B,oBACE5H,OAAA,CAACvD,KAAK;QAACmH,QAAQ,EAAC,MAAM;QAAAmN,QAAA,EACnB3P,UAAU,IAAIQ,OAAO,CAACE,KAAK,IAAIF,OAAO,CAACG,SAAS,GAC7C,4CAA4C,GAC5C;MAAyB;QAAAwP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC;IAEZ;IAEA,oBACE1R,OAAA,CAAAE,SAAA;MAAA6Q,QAAA,gBACE/Q,OAAA,CAACnD,cAAc;QAACoX,SAAS,EAAEvY,KAAM;QAAAqV,QAAA,eAC/B/Q,OAAA,CAACtD,KAAK;UAACuW,IAAI,EAAC,OAAO;UAAAlC,QAAA,gBACjB/Q,OAAA,CAAClD,SAAS;YAAAiU,QAAA,eACR/Q,OAAA,CAACjD,QAAQ;cAAAgU,QAAA,gBACP/Q,OAAA,CAACpD,SAAS;gBAAAmU,QAAA,eACR/Q,OAAA,CAAC1C,KAAK;kBAAC0T,SAAS,EAAC,KAAK;kBAACE,UAAU,EAAC,QAAQ;kBAACD,OAAO,EAAE,CAAE;kBAAAF,QAAA,gBACpD/Q,OAAA,CAACxE,UAAU;oBAACmW,OAAO,EAAC,OAAO;oBAACC,UAAU,EAAC,MAAM;oBAAAb,QAAA,EAAC;kBAAO;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAClE1R,OAAA,CAAChD,UAAU;oBAACiW,IAAI,EAAC,OAAO;oBAACD,OAAO,EAAEA,CAAA,KAAM;sBACtCrQ,SAAS,CAAC,SAAS,CAAC;sBACpBE,YAAY,CAACD,SAAS,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK,CAAC;oBACpD,CAAE;oBAAAmO,QAAA,EACCrO,MAAM,KAAK,SAAS,GAAIE,SAAS,KAAK,KAAK,gBAAG5C,OAAA,CAACtB,cAAc;sBAAA6S,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAG1R,OAAA,CAACxB,cAAc;sBAAA+S,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAI1R,OAAA,CAACxB,cAAc;sBAAA+S,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACZ1R,OAAA,CAACpD,SAAS;gBAAAmU,QAAA,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChC1R,OAAA,CAACpD,SAAS;gBAAAmU,QAAA,EAAC;cAAO;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9B1R,OAAA,CAACpD,SAAS;gBAAAmU,QAAA,EAAC;cAAQ;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC/B1R,OAAA,CAACpD,SAAS;gBAAAmU,QAAA,EAAC;cAAM;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC7B1R,OAAA,CAACpD,SAAS;gBAAAmU,QAAA,EAAC;cAAK;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC5B1R,OAAA,CAACpD,SAAS;gBAAAmU,QAAA,EAAC;cAAK;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC5B1R,OAAA,CAACpD,SAAS;gBAAAmU,QAAA,EAAC;cAAY;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACnC1R,OAAA,CAACpD,SAAS;gBAAAmU,QAAA,EAAC;cAAW;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAClC1R,OAAA,CAACpD,SAAS;gBAAAmU,QAAA,EAAC;cAAM;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZ1R,OAAA,CAACrD,SAAS;YAAAoU,QAAA,EACPiD,YAAY,CAACpJ,GAAG,CAAE1D,IAAI,IAAK;cAC1B,MAAMgN,aAAa,GAAGnJ,iBAAiB,CAAC7D,IAAI,CAAC/C,OAAO,CAAC;cACrD,MAAMgQ,cAAc,GAAGlN,oBAAoB,CAACC,IAAI,CAAC;cACjD,MAAMkN,eAAe,GAAG,CAACD,cAAc,GAAGlJ,gCAAgC,CAAC/D,IAAI,CAAC,GAAG,EAAE;cAErF,oBACElH,OAAA,CAACjD,QAAQ;gBAAAgU,QAAA,gBACP/Q,OAAA,CAACpD,SAAS;kBAAAmU,QAAA,eACR/Q,OAAA,CAACxE,UAAU;oBAACmW,OAAO,EAAC,OAAO;oBAACC,UAAU,EAAC,QAAQ;oBAAAb,QAAA,EAC5C7J,IAAI,CAAC/C;kBAAO;oBAAAoN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACZ1R,OAAA,CAACpD,SAAS;kBAAAmU,QAAA,EAAE7J,IAAI,CAACnF;gBAAS;kBAAAwP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACvC1R,OAAA,CAACpD,SAAS;kBAAAmU,QAAA,EAAE7J,IAAI,CAACsC;gBAAO;kBAAA+H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrC1R,OAAA,CAACpD,SAAS;kBAAAmU,QAAA,EAAE7J,IAAI,CAACoC;gBAAmB;kBAAAiI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACjD1R,OAAA,CAACpD,SAAS;kBAAAmU,QAAA,EAAE7J,IAAI,CAACqC;gBAAiB;kBAAAgI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC/C1R,OAAA,CAACpD,SAAS;kBAAAmU,QAAA,GAAE7J,IAAI,CAACqE,eAAe,IAAIrE,IAAI,CAACsE,aAAa,EAAC,IAAE;gBAAA;kBAAA+F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACrE1R,OAAA,CAACpD,SAAS;kBAAAmU,QAAA,eACR/Q,OAAA,CAACzC,IAAI;oBACH0V,IAAI,EAAC,OAAO;oBACZW,KAAK,EAAE1M,IAAI,CAACE,mBAAoB;oBAChCiK,KAAK,EAAEnK,IAAI,CAACE,mBAAmB,KAAK,YAAY,GAAG,SAAS,GAAG;kBAAU;oBAAAmK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1E;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZ1R,OAAA,CAACpD,SAAS;kBAAAmU,QAAA,EACP,CAAC,MAAM;oBACN,MAAMxJ,YAAY,GAAGL,IAAI,CAACK,YAAY,IAAI,CAAC;oBAC3C,MAAM8M,iBAAiB,GAAG9M,YAAY,KAAK,CAAC,GAAG,eAAe,GACrCA,YAAY,KAAK,CAAC,GAAG,eAAe,GACpCA,YAAY,KAAK,CAAC,GAAG,aAAa,GAClCA,YAAY,KAAK,CAAC,GAAG,UAAU,GAC/B,aAAa;oBACtC,MAAM+M,MAAM,GAAG/M,YAAY,KAAK,CAAC,GAAG,SAAS,GAC/BA,YAAY,KAAK,CAAC,GAAG,OAAO,GAAG,SAAS;oBAEtD,oBACEvH,OAAA,CAACxC,OAAO;sBAAC+W,KAAK,EAAE,aAAarN,IAAI,CAACO,qBAAqB,IAAI,eAAe,cAAcP,IAAI,CAACQ,mBAAmB,IAAI,eAAe,EAAG;sBAAAqJ,QAAA,eACpI/Q,OAAA,CAACzC,IAAI;wBACH0V,IAAI,EAAC,OAAO;wBACZW,KAAK,EAAES,iBAAkB;wBACzBhD,KAAK,EAAEiD,MAAO;wBACdE,IAAI,EAAEjN,YAAY,KAAK,CAAC,gBAAGvH,OAAA,CAAC5B,SAAS;0BAAAmT,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,gBAAG1R,OAAA,CAAC1B,WAAW;0BAAAiT,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAE;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5D;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACK,CAAC;kBAEd,CAAC,EAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC,eACZ1R,OAAA,CAACpD,SAAS;kBAAAmU,QAAA,EACPmD,aAAa,gBACZlU,OAAA,CAACzC,IAAI;oBACH0V,IAAI,EAAC,OAAO;oBACZuB,IAAI,eAAExU,OAAA,CAAC5B,SAAS;sBAAAmT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACpBkC,KAAK,EAAC,aAAa;oBACnBvC,KAAK,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB,CAAC,gBAEF1R,OAAA,CAACzC,IAAI;oBACH0V,IAAI,EAAC,OAAO;oBACZuB,IAAI,eAAExU,OAAA,CAAC1B,WAAW;sBAAAiT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACtBkC,KAAK,EAAC,iBAAiB;oBACvBvC,KAAK,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB;gBACF;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,eACZ1R,OAAA,CAACpD,SAAS;kBAAAmU,QAAA,EACPmD,aAAa,gBACZlU,OAAA,CAACxC,OAAO;oBAAC+W,KAAK,EAAC,yBAAsB;oBAAAxD,QAAA,eACnC/Q,OAAA,CAACzC,IAAI;sBACHiX,IAAI,eAAExU,OAAA,CAAC5B,SAAS;wBAAAmT,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBACpBkC,KAAK,EAAC,aAAa;sBACnBvC,KAAK,EAAC,SAAS;sBACf4B,IAAI,EAAC;oBAAO;sBAAA1B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACb;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACK,CAAC,GACRyC,cAAc,gBAChBnU,OAAA,CAACxC,OAAO;oBAAC+W,KAAK,EAAC,qCAAqC;oBAAAxD,QAAA,eAClD/Q,OAAA,CAAChD,UAAU;sBACTiW,IAAI,EAAC,OAAO;sBACZD,OAAO,EAAEA,CAAA,KAAM3H,gBAAgB,CAACnE,IAAI,CAAE;sBACtCmK,KAAK,EAAC,SAAS;sBAAAN,QAAA,eAEf/Q,OAAA,CAAClC,OAAO;wBAAAyT,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,gBAEV1R,OAAA,CAACxC,OAAO;oBAAC+W,KAAK,EAAEH,eAAgB;oBAAArD,QAAA,eAC9B/Q,OAAA;sBAAA+Q,QAAA,eACE/Q,OAAA,CAAChD,UAAU;wBACTiW,IAAI,EAAC,OAAO;wBACZI,QAAQ;wBACRL,OAAO,EAAEA,CAAA,KAAM7M,YAAY,CAACiO,eAAe,EAAE,SAAS,CAAE;wBAAArD,QAAA,eAExD/Q,OAAA,CAACpB,SAAS;0BAAA2S,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA;gBACV;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC;cAAA,GA3FCxK,IAAI,CAAC/C,OAAO;gBAAAoN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA4FjB,CAAC;YAEf,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,EAEhBlB,aAAa,CAAClP,YAAY,CAAC,GAAG,CAAC,iBAC9BtB,OAAA,CAACzE,GAAG;QAACoV,EAAE,EAAE;UAAEsB,OAAO,EAAE,MAAM;UAAEd,cAAc,EAAE,QAAQ;UAAEsD,EAAE,EAAE;QAAE,CAAE;QAAA1D,QAAA,eAC5D/Q,OAAA,CAAC7C,UAAU;UACTuX,KAAK,EAAElE,aAAa,CAAClP,YAAY,CAAE;UACnCqT,IAAI,EAAEpS,WAAY;UAClBkQ,QAAQ,EAAEA,CAACtH,KAAK,EAAES,KAAK,KAAKpJ,cAAc,CAACoJ,KAAK,CAAE;UAClDyF,KAAK,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA,eACD,CAAC;EAEP,CAAC;;EAED;EACA,MAAMkD,yBAAyB,GAAGA,CAAA,KAAM;IACtC,MAAMZ,YAAY,GAAG7D,mBAAmB,CAAC3O,sBAAsB,CAAC;IAEhE,IAAIA,sBAAsB,CAACoG,MAAM,KAAK,CAAC,EAAE;MACvC,oBACE5H,OAAA,CAACvD,KAAK;QAACmH,QAAQ,EAAC,MAAM;QAAAmN,QAAA,EACnB3P,UAAU,IAAIQ,OAAO,CAACI,SAAS,GAC5B,uDAAuD,GACvD;MAAoC;QAAAuP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAEZ;IAEA,oBACE1R,OAAA,CAAAE,SAAA;MAAA6Q,QAAA,gBACE/Q,OAAA,CAACnD,cAAc;QAACoX,SAAS,EAAEvY,KAAM;QAAAqV,QAAA,eAC/B/Q,OAAA,CAACtD,KAAK;UAACuW,IAAI,EAAC,OAAO;UAAAlC,QAAA,gBACjB/Q,OAAA,CAAClD,SAAS;YAAAiU,QAAA,eACR/Q,OAAA,CAACjD,QAAQ;cAAAgU,QAAA,GACNzN,QAAQ,iBACPtD,OAAA,CAACpD,SAAS;gBAACiY,OAAO,EAAC,UAAU;gBAAA9D,QAAA,eAC3B/Q,OAAA,CAAChD,UAAU;kBACTiW,IAAI,EAAC,OAAO;kBACZD,OAAO,EAAE5P,aAAa,CAACwE,MAAM,KAAKpG,sBAAsB,CAACoG,MAAM,GAAGkD,cAAc,GAAGJ,cAAe;kBAAAqG,QAAA,EAEjG3N,aAAa,CAACwE,MAAM,KAAKpG,sBAAsB,CAACoG,MAAM,gBAAG5H,OAAA,CAAC9B,SAAS;oBAAAqT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAG1R,OAAA,CAAC5B,SAAS;oBAAAmT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7E;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACZ,eACD1R,OAAA,CAACpD,SAAS;gBAAAmU,QAAA,eACR/Q,OAAA,CAAC1C,KAAK;kBAAC0T,SAAS,EAAC,KAAK;kBAACE,UAAU,EAAC,QAAQ;kBAACD,OAAO,EAAE,CAAE;kBAAAF,QAAA,gBACpD/Q,OAAA,CAACxE,UAAU;oBAACmW,OAAO,EAAC,OAAO;oBAACC,UAAU,EAAC,MAAM;oBAAAb,QAAA,EAAC;kBAAc;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACzE1R,OAAA,CAAChD,UAAU;oBAACiW,IAAI,EAAC,OAAO;oBAACD,OAAO,EAAEA,CAAA,KAAM;sBACtCrQ,SAAS,CAAC,oBAAoB,CAAC;sBAC/BE,YAAY,CAACD,SAAS,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK,CAAC;oBACpD,CAAE;oBAAAmO,QAAA,EACCrO,MAAM,KAAK,oBAAoB,GAAIE,SAAS,KAAK,KAAK,gBAAG5C,OAAA,CAACtB,cAAc;sBAAA6S,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAG1R,OAAA,CAACxB,cAAc;sBAAA+S,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAI1R,OAAA,CAACxB,cAAc;sBAAA+S,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7G,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACZ1R,OAAA,CAACpD,SAAS;gBAAAmU,QAAA,EAAC;cAAO;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9B1R,OAAA,CAACpD,SAAS;gBAAAmU,QAAA,eACR/Q,OAAA,CAAC1C,KAAK;kBAAC0T,SAAS,EAAC,KAAK;kBAACE,UAAU,EAAC,QAAQ;kBAACD,OAAO,EAAE,CAAE;kBAAAF,QAAA,gBACpD/Q,OAAA,CAACxE,UAAU;oBAACmW,OAAO,EAAC,OAAO;oBAACC,UAAU,EAAC,MAAM;oBAAAb,QAAA,EAAC;kBAAI;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC/D1R,OAAA,CAAChD,UAAU;oBAACiW,IAAI,EAAC,OAAO;oBAACD,OAAO,EAAEA,CAAA,KAAM;sBACtCrQ,SAAS,CAAC,qBAAqB,CAAC;sBAChCE,YAAY,CAACD,SAAS,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK,CAAC;oBACpD,CAAE;oBAAAmO,QAAA,EACCrO,MAAM,KAAK,qBAAqB,GAAIE,SAAS,KAAK,KAAK,gBAAG5C,OAAA,CAACtB,cAAc;sBAAA6S,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAG1R,OAAA,CAACxB,cAAc;sBAAA+S,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAI1R,OAAA,CAACxB,cAAc;sBAAA+S,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9G,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACZ1R,OAAA,CAACpD,SAAS;gBAAAmU,QAAA,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChC1R,OAAA,CAACpD,SAAS;gBAAAmU,QAAA,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChC1R,OAAA,CAACpD,SAAS;gBAAAmU,QAAA,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChC1R,OAAA,CAACpD,SAAS;gBAAAmU,QAAA,EAAC;cAAU;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACjC1R,OAAA,CAACpD,SAAS;gBAAAmU,QAAA,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChC1R,OAAA,CAACpD,SAAS;gBAAAmU,QAAA,EAAC;cAAM;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZ1R,OAAA,CAACrD,SAAS;YAAAoU,QAAA,EACPiD,YAAY,CAACpJ,GAAG,CAAEzC,IAAI,iBACrBnI,OAAA,CAACjD,QAAQ;cAEP+X,QAAQ,EAAE1R,aAAa,CAACiG,QAAQ,CAAClB,IAAI,CAAC0C,iBAAiB,CAAE;cACzDkK,KAAK;cAAAhE,QAAA,GAEJzN,QAAQ,iBACPtD,OAAA,CAACpD,SAAS;gBAACiY,OAAO,EAAC,UAAU;gBAAA9D,QAAA,eAC3B/Q,OAAA,CAAChD,UAAU;kBACTiW,IAAI,EAAC,OAAO;kBACZD,OAAO,EAAEA,CAAA,KAAM1I,mBAAmB,CAACnC,IAAI,CAAC0C,iBAAiB,CAAE;kBAC3DwG,KAAK,EAAEjO,aAAa,CAACiG,QAAQ,CAAClB,IAAI,CAAC0C,iBAAiB,CAAC,GAAG,SAAS,GAAG,SAAU;kBAAAkG,QAAA,EAE7E3N,aAAa,CAACiG,QAAQ,CAAClB,IAAI,CAAC0C,iBAAiB,CAAC,gBAAG7K,OAAA,CAAC5B,SAAS;oBAAAmT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAG1R,OAAA,CAAClC,OAAO;oBAAAyT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACZ,eACD1R,OAAA,CAACpD,SAAS;gBAAAmU,QAAA,eACR/Q,OAAA,CAACxE,UAAU;kBAACmW,OAAO,EAAC,OAAO;kBAACC,UAAU,EAAC,QAAQ;kBAAAb,QAAA,EAC5C5I,IAAI,CAAC+B;gBAAkB;kBAAAqH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACZ1R,OAAA,CAACpD,SAAS;gBAAAmU,QAAA,eACR/Q,OAAA,CAACzC,IAAI;kBAAC0V,IAAI,EAAC,OAAO;kBAACW,KAAK,EAAEzL,IAAI,CAAChE,OAAQ;kBAACwN,OAAO,EAAC;gBAAU;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,eACZ1R,OAAA,CAACpD,SAAS;gBAAAmU,QAAA,EAAE,IAAI/I,IAAI,CAACG,IAAI,CAACC,mBAAmB,CAAC,CAACgH,kBAAkB,CAAC;cAAC;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAChF1R,OAAA,CAACpD,SAAS;gBAAAmU,QAAA,eACR/Q,OAAA,CAACxE,UAAU;kBAACmW,OAAO,EAAC,OAAO;kBAAAZ,QAAA,EAAE5I,IAAI,CAACnG,SAAS,IAAImG,IAAI,CAAC/D;gBAAY;kBAAAmN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CAAC,eACZ1R,OAAA,CAACpD,SAAS;gBAAAmU,QAAA,eACR/Q,OAAA,CAACxE,UAAU;kBAACmW,OAAO,EAAC,OAAO;kBAAAZ,QAAA,EACxB5I,IAAI,CAAC9D,YAAY,GAChB,CAAC,MAAM;oBACL,MAAMhC,SAAS,GAAGnB,SAAS,CAAC0L,IAAI,CAACoI,CAAC,IAAIA,CAAC,CAAC3Q,YAAY,KAAK8D,IAAI,CAAC9D,YAAY,CAAC;oBAC3E,OAAOhC,SAAS,GAAG,GAAGA,SAAS,CAAC4S,IAAI,MAAM5S,SAAS,CAAC6S,KAAK,EAAE,GAAG,uBAAuB;kBACvF,CAAC,EAAE,CAAC,GACD/M,IAAI,CAACgN,oBAAoB,IAAI;gBAAM;kBAAA5D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAE9B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACZ1R,OAAA,CAACpD,SAAS;gBAAAmU,QAAA,eACR/Q,OAAA,CAACxE,UAAU;kBAACmW,OAAO,EAAC,OAAO;kBAAAZ,QAAA,GAAE5I,IAAI,CAAC7D,kBAAkB,EAAC,IAAE;gBAAA;kBAAAiN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CAAC,eACZ1R,OAAA,CAACpD,SAAS;gBAAAmU,QAAA,eACR/Q,OAAA,CAACzC,IAAI;kBACH0V,IAAI,EAAC,OAAO;kBACZW,KAAK,EAAE,GAAGzL,IAAI,CAAC3D,iBAAiB,KAAM;kBACtC6M,KAAK,EAAEjH,UAAU,CAACjC,IAAI,CAAC3D,iBAAiB,CAAC,IAAI,GAAG,GAAG,SAAS,GAAG,SAAU;kBACzEgQ,IAAI,EAAEpK,UAAU,CAACjC,IAAI,CAAC3D,iBAAiB,CAAC,IAAI,GAAG,gBAAGxE,OAAA,CAAC5B,SAAS;oBAAAmT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAG1R,OAAA,CAAC1B,WAAW;oBAAAiT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZ1R,OAAA,CAACpD,SAAS;gBAAAmU,QAAA,eACR/Q,OAAA,CAACzC,IAAI;kBACH0V,IAAI,EAAC,OAAO;kBACZW,KAAK,EAAEzL,IAAI,CAACpD,gBAAgB,IAAI,UAAW;kBAC3CsM,KAAK,EAAElJ,IAAI,CAACpD,gBAAgB,KAAK,UAAU,GAAG,SAAS,GAAGoD,IAAI,CAACpD,gBAAgB,KAAK,cAAc,GAAG,OAAO,GAAG;gBAAU;kBAAAwM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1H;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZ1R,OAAA,CAACpD,SAAS;gBAAAmU,QAAA,eACR/Q,OAAA,CAAC1C,KAAK;kBAAC0T,SAAS,EAAC,KAAK;kBAACC,OAAO,EAAE,GAAI;kBAAAF,QAAA,gBAClC/Q,OAAA,CAACxC,OAAO;oBAAC+W,KAAK,EAAC,qBAAqB;oBAAAxD,QAAA,eAClC/Q,OAAA,CAAChD,UAAU;sBACTiW,IAAI,EAAC,OAAO;sBACZD,OAAO,EAAEA,CAAA,KAAM;wBACb7P,eAAe,CAACgF,IAAI,CAAC;wBACrBlF,aAAa,CAAC,MAAM,CAAC;wBACrBF,aAAa,CAAC,IAAI,CAAC;sBACrB,CAAE;sBAAAgO,QAAA,eAEF/Q,OAAA,CAAClB,QAAQ;wBAAAyS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACV1R,OAAA,CAACxC,OAAO;oBAAC+W,KAAK,EAAC,YAAY;oBAAAxD,QAAA,eACzB/Q,OAAA,CAAChD,UAAU;sBACTiW,IAAI,EAAC,OAAO;sBACZD,OAAO,EAAEA,CAAA,KAAMhG,iBAAiB,CAAC7E,IAAI,CAAE;sBACvCkL,QAAQ,EAAEtP,mBAAoB;sBAAAgN,QAAA,eAE9B/Q,OAAA,CAAChB,OAAO;wBAAAuS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACV1R,OAAA,CAACxC,OAAO;oBAAC+W,KAAK,EAAC,SAAS;oBAAAxD,QAAA,eACtB/Q,OAAA,CAAChD,UAAU;sBACTiW,IAAI,EAAC,OAAO;sBACZ5B,KAAK,EAAC,OAAO;sBACb2B,OAAO,EAAEA,CAAA,KAAMxE,0BAA0B,CAACrG,IAAI,CAAE;sBAChDkL,QAAQ,EAAEtP,mBAAoB;sBAAAgN,QAAA,eAE9B/Q,OAAA,CAACd,UAAU;wBAAAqS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA,GA1FPvJ,IAAI,CAAC0C,iBAAiB;cAAA0G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA2FnB,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,EAEhBlB,aAAa,CAAChP,sBAAsB,CAAC,GAAG,CAAC,iBACxCxB,OAAA,CAACzE,GAAG;QAACoV,EAAE,EAAE;UAAEsB,OAAO,EAAE,MAAM;UAAEd,cAAc,EAAE,QAAQ;UAAEsD,EAAE,EAAE;QAAE,CAAE;QAAA1D,QAAA,eAC5D/Q,OAAA,CAAC7C,UAAU;UACTuX,KAAK,EAAElE,aAAa,CAAChP,sBAAsB,CAAE;UAC7CmT,IAAI,EAAEpS,WAAY;UAClBkQ,QAAQ,EAAEA,CAACtH,KAAK,EAAES,KAAK,KAAKpJ,cAAc,CAACoJ,KAAK,CAAE;UAClDyF,KAAK,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA,eACD,CAAC;EAEP,CAAC;;EAED;EACA,MAAM0D,0BAA0B,GAAGA,CAAA,KAAM;IACvC,IAAIpS,UAAU,KAAK,QAAQ,IAAIA,UAAU,KAAK,MAAM,EAAE,OAAO,IAAI;IAEjE,oBACEhD,OAAA,CAAClE,MAAM;MAAC4H,IAAI,EAAEZ,UAAW;MAACuS,OAAO,EAAE5J,WAAY;MAAC6J,QAAQ,EAAC,IAAI;MAAC/C,SAAS;MAAAxB,QAAA,gBACrE/Q,OAAA,CAACjE,WAAW;QAAAgV,QAAA,EACT/N,UAAU,KAAK,QAAQ,GAAG,sBAAsB,GAAG;MAAyB;QAAAuO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE,CAAC,eACd1R,OAAA,CAAChE,aAAa;QAAA+U,QAAA,eACZ/Q,OAAA,CAACrE,IAAI;UAACwW,SAAS;UAAClB,OAAO,EAAE,CAAE;UAACN,EAAE,EAAE;YAAE8D,EAAE,EAAE;UAAE,CAAE;UAAA1D,QAAA,gBACxC/Q,OAAA,CAACrE,IAAI;YAACyW,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACvB/Q,OAAA,CAACzD,YAAY;cACXgZ,OAAO,EAAEvU,IAAI,CAACkH,MAAM,CAAChB,IAAI,IAAI;gBAC3B;gBACA,MAAMsO,UAAU,GAAGtO,IAAI,CAAC/C,OAAO,KAAKF,QAAQ,CAACE,OAAO;gBACpD,MAAMsR,cAAc,GAAG,CAAC3U,cAAc,CAAC4I,IAAI,CAACvB,IAAI,IAAIA,IAAI,CAAChE,OAAO,KAAK+C,IAAI,CAAC/C,OAAO,CAAC;gBAClF,MAAMuR,cAAc,GAAGzO,oBAAoB,CAACC,IAAI,CAAC;gBAEjD,OAAOsO,UAAU,IAAKC,cAAc,IAAIC,cAAe;cACzD,CAAC,CAAE;cACHC,cAAc,EAAG5F,MAAM,IAAK,GAAGA,MAAM,CAAC5L,OAAO,MAAM4L,MAAM,CAAChO,SAAS,EAAG;cACtE6J,KAAK,EAAE5K,IAAI,CAAC4L,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC1I,OAAO,KAAKF,QAAQ,CAACE,OAAO,CAAC,IAAI,IAAK;cAC9DsO,QAAQ,EAAEA,CAACtH,KAAK,EAAEC,QAAQ,KAAK;gBAC7B,IAAIA,QAAQ,EAAE;kBACZS,gBAAgB,CAACT,QAAQ,CAAC;gBAC5B,CAAC,MAAM;kBACLlH,WAAW,CAACsG,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAErG,OAAO,EAAE,EAAE;oBAAEG,kBAAkB,EAAE;kBAAG,CAAC,CAAC,CAAC;gBACzE;cACF,CAAE;cACFsR,WAAW,EAAGC,MAAM,iBAClB7V,OAAA,CAAC9D,SAAS;gBAAA,GACJ2Z,MAAM;gBACVjC,KAAK,EAAC,QAAQ;gBACdpB,WAAW,EAAC,0BAA0B;gBACtCsD,QAAQ;gBACRC,UAAU,EAAC;cAA6E;gBAAAxE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzF,CACD;cACFsE,YAAY,EAAEA,CAACC,KAAK,EAAElG,MAAM,KAAK;gBAC/B,MAAMxI,YAAY,GAAGwI,MAAM,CAACxI,YAAY,IAAI,CAAC;gBAC7C,MAAMD,WAAW,GAAGC,YAAY,KAAK,CAAC;gBAEtC,oBACEvH,OAAA,CAACzE,GAAG;kBAAC0Y,SAAS,EAAC,IAAI;kBAAA,GAAKgC,KAAK;kBAAAlF,QAAA,eAC3B/Q,OAAA,CAACzE,GAAG;oBAACoV,EAAE,EAAE;sBAAEmB,KAAK,EAAE;oBAAO,CAAE;oBAAAf,QAAA,eACzB/Q,OAAA,CAAC1C,KAAK;sBAAC0T,SAAS,EAAC,KAAK;sBAACG,cAAc,EAAC,eAAe;sBAACD,UAAU,EAAC,QAAQ;sBAAAH,QAAA,gBACvE/Q,OAAA,CAACzE,GAAG;wBAAAwV,QAAA,gBACF/Q,OAAA,CAACxE,UAAU;0BAACmW,OAAO,EAAC,OAAO;0BAACC,UAAU,EAAC,QAAQ;0BAAAb,QAAA,EAC5ChB,MAAM,CAAC5L;wBAAO;0BAAAoN,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eACb1R,OAAA,CAACxE,UAAU;0BAACmW,OAAO,EAAC,SAAS;0BAACN,KAAK,EAAC,gBAAgB;0BAAAN,QAAA,GACjDhB,MAAM,CAAChO,SAAS,EAAC,KAAG,EAACgO,MAAM,CAACzG,mBAAmB,EAAC,UAAG,EAACyG,MAAM,CAACxG,iBAAiB;wBAAA;0BAAAgI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC,eACN1R,OAAA,CAAC1C,KAAK;wBAAC0T,SAAS,EAAC,KAAK;wBAACC,OAAO,EAAE,CAAE;wBAAAF,QAAA,gBAChC/Q,OAAA,CAACzC,IAAI;0BACH0V,IAAI,EAAC,OAAO;0BACZW,KAAK,EAAE7D,MAAM,CAAC3I,mBAAoB;0BAClCiK,KAAK,EAAEtB,MAAM,CAAC3I,mBAAmB,KAAK,YAAY,GAAG,SAAS,GAAG;wBAAU;0BAAAmK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC5E,CAAC,eACF1R,OAAA,CAACzC,IAAI;0BACH0V,IAAI,EAAC,OAAO;0BACZW,KAAK,EAAEtM,WAAW,GAAG,WAAW,GAAG,cAAe;0BAClD+J,KAAK,EAAE/J,WAAW,GAAG,SAAS,GAAG,SAAU;0BAC3CkN,IAAI,EAAElN,WAAW,gBAAGtH,OAAA,CAAC5B,SAAS;4BAAAmT,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,gBAAG1R,OAAA,CAAC1B,WAAW;4BAAAiT,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAE;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAEV;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEP1R,OAAA,CAACrE,IAAI;YAACyW,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACvB/Q,OAAA,CAAC9D,SAAS;cACRqW,SAAS;cACTqB,KAAK,EAAC,aAAa;cACnBhI,KAAK,EAAE3H,QAAQ,CAACG,YAAa;cAC7BqO,QAAQ,EAAGC,CAAC,IAAKhH,gBAAgB,CAAC,cAAc,EAAEgH,CAAC,CAACC,MAAM,CAAC/G,KAAK,CAAE;cAClEkK,QAAQ;YAAA;cAAAvE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEP1R,OAAA,CAACrE,IAAI;YAACyW,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACvB/Q,OAAA,CAAC7D,WAAW;cAACoW,SAAS;cAACuD,QAAQ;cAAA/E,QAAA,gBAC7B/Q,OAAA,CAAC5D,UAAU;gBAAA2U,QAAA,EAAC;cAAW;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACpC1R,OAAA,CAAC3D,MAAM;gBACLuP,KAAK,EAAE3H,QAAQ,CAACI,YAAa;gBAC7BoO,QAAQ,EAAGC,CAAC,IAAKhH,gBAAgB,CAAC,cAAc,EAAEgH,CAAC,CAACC,MAAM,CAAC/G,KAAK,CAAE;gBAClEgI,KAAK,EAAC,aAAa;gBAAA7C,QAAA,EAElB7P,SAAS,CAAC0J,GAAG,CAAEvI,SAAS,iBACvBrC,OAAA,CAAC1D,QAAQ;kBAA8BsP,KAAK,EAAEvJ,SAAS,CAACgC,YAAa;kBAAA0M,QAAA,GAClE1O,SAAS,CAAC4S,IAAI,EAAC,KAAG,EAAC5S,SAAS,CAAC6S,KAAK,EAAC,GAAC,EAAC7S,SAAS,CAAC6T,OAAO,EAAC,SAAO,EAAC7T,SAAS,CAAC8T,YAAY,EAAC,GACzF;gBAAA,GAFe9T,SAAS,CAACgC,YAAY;kBAAAkN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAE3B,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEP1R,OAAA,CAACrE,IAAI;YAACyW,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACvB/Q,OAAA,CAAC9D,SAAS;cACRqW,SAAS;cACTqB,KAAK,EAAC,0BAA0B;cAChCzF,IAAI,EAAC,QAAQ;cACbvC,KAAK,EAAE3H,QAAQ,CAACK,kBAAmB;cACnCmO,QAAQ,EAAGC,CAAC,IAAKhH,gBAAgB,CAAC,oBAAoB,EAAEgH,CAAC,CAACC,MAAM,CAAC/G,KAAK,CAAE;cACxEkK,QAAQ;YAAA;cAAAvE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEP1R,OAAA,CAACrE,IAAI;YAACyW,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACvB/Q,OAAA,CAAC7D,WAAW;cAACoW,SAAS;cAAAxB,QAAA,gBACpB/Q,OAAA,CAAC5D,UAAU;gBAAA2U,QAAA,EAAC;cAAU;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACnC1R,OAAA,CAAC3D,MAAM;gBACLuP,KAAK,EAAE3H,QAAQ,CAACM,iBAAkB;gBAClCkO,QAAQ,EAAGC,CAAC,IAAKhH,gBAAgB,CAAC,mBAAmB,EAAEgH,CAAC,CAACC,MAAM,CAAC/G,KAAK,CAAE;gBACvEgI,KAAK,EAAC,eAAY;gBAAA7C,QAAA,gBAElB/Q,OAAA,CAAC1D,QAAQ;kBAACsP,KAAK,EAAC,IAAI;kBAAAmF,QAAA,EAAC;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAClC1R,OAAA,CAAC1D,QAAQ;kBAACsP,KAAK,EAAC,KAAK;kBAAAmF,QAAA,EAAC;gBAAG;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEP1R,OAAA,CAACrE,IAAI;YAACyW,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACvB/Q,OAAA,CAAC9D,SAAS;cACRqW,SAAS;cACTqB,KAAK,EAAC,wBAAmB;cACzBzF,IAAI,EAAC,QAAQ;cACbvC,KAAK,EAAE3H,QAAQ,CAACO,iBAAkB;cAClCiO,QAAQ,EAAGC,CAAC,IAAKhH,gBAAgB,CAAC,mBAAmB,EAAEgH,CAAC,CAACC,MAAM,CAAC/G,KAAK,CAAE;cACvEkK,QAAQ;cACRC,UAAU,EAAC;YAAmC;cAAAxE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEP1R,OAAA,CAACrE,IAAI;YAACyW,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACvB/Q,OAAA,CAAC7D,WAAW;cAACoW,SAAS;cAAAxB,QAAA,gBACpB/Q,OAAA,CAAC5D,UAAU;gBAAA2U,QAAA,EAAC;cAAU;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACnC1R,OAAA,CAAC3D,MAAM;gBACLuP,KAAK,EAAE3H,QAAQ,CAACQ,iBAAkB;gBAClCgO,QAAQ,EAAGC,CAAC,IAAKhH,gBAAgB,CAAC,mBAAmB,EAAEgH,CAAC,CAACC,MAAM,CAAC/G,KAAK,CAAE;gBACvEgI,KAAK,EAAC,YAAY;gBAAA7C,QAAA,gBAElB/Q,OAAA,CAAC1D,QAAQ;kBAACsP,KAAK,EAAC,IAAI;kBAAAmF,QAAA,EAAC;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAClC1R,OAAA,CAAC1D,QAAQ;kBAACsP,KAAK,EAAC,KAAK;kBAAAmF,QAAA,EAAC;gBAAG;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAGP1R,OAAA,CAACrE,IAAI;YAACyW,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAtB,QAAA,eAChB/Q,OAAA,CAAC3C,OAAO;cAACsT,EAAE,EAAE;gBAAE4C,EAAE,EAAE;cAAE,CAAE;cAAAxC,QAAA,eACrB/Q,OAAA,CAACxE,UAAU;gBAACmW,OAAO,EAAC,OAAO;gBAACN,KAAK,EAAC,gBAAgB;gBAAAN,QAAA,EAAC;cAEnD;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAENzN,QAAQ,CAACE,OAAO,IAAI,CAAC,MAAM;YAC1B,MAAM+C,IAAI,GAAGlG,IAAI,CAAC4L,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC1I,OAAO,KAAKF,QAAQ,CAACE,OAAO,CAAC;YAC3D,IAAI,CAAC+C,IAAI,EAAE,OAAO,IAAI;YAEtB,MAAMK,YAAY,GAAGL,IAAI,CAACK,YAAY,IAAI,CAAC;YAC3C,MAAMD,WAAW,GAAGC,YAAY,KAAK,CAAC;YAEtC,oBACEvH,OAAA,CAACrE,IAAI;cAACyW,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAtB,QAAA,eAChB/Q,OAAA,CAACtE,KAAK;gBAACiV,EAAE,EAAE;kBAAEC,CAAC,EAAE,CAAC;kBAAEE,OAAO,EAAExJ,WAAW,GAAG,eAAe,GAAG;gBAAgB,CAAE;gBAAAyJ,QAAA,eAC5E/Q,OAAA,CAAC1C,KAAK;kBAAC0T,SAAS,EAAC,KAAK;kBAACE,UAAU,EAAC,QAAQ;kBAACD,OAAO,EAAE,CAAE;kBAAAF,QAAA,GACnDzJ,WAAW,gBAAGtH,OAAA,CAAC5B,SAAS;oBAACiT,KAAK,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAG1R,OAAA,CAAC1B,WAAW;oBAAC+S,KAAK,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9E1R,OAAA,CAACzE,GAAG;oBAAAwV,QAAA,gBACF/Q,OAAA,CAACxE,UAAU;sBAACmW,OAAO,EAAC,OAAO;sBAACC,UAAU,EAAC,MAAM;sBAAAb,QAAA,EAC1CzJ,WAAW,GAAG,8BAA8B,GAAG;oBAAkC;sBAAAiK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxE,CAAC,eACb1R,OAAA,CAACxE,UAAU;sBAACmW,OAAO,EAAC,SAAS;sBAAAZ,QAAA,GAAC,SACrB,EAACxJ,YAAY,KAAK,CAAC,GAAG,eAAe,GACpCA,YAAY,KAAK,CAAC,GAAG,yBAAyB,GAC9CA,YAAY,KAAK,CAAC,GAAG,uBAAuB,GAC5CA,YAAY,KAAK,CAAC,GAAG,yBAAyB,GAC9C,mBAAmB;oBAAA;sBAAAgK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC,EACZ,CAACpK,WAAW,iBACXtH,OAAA,CAACzE,GAAG;sBAACoV,EAAE,EAAE;wBAAE8D,EAAE,EAAE;sBAAE,CAAE;sBAAA1D,QAAA,gBACjB/Q,OAAA,CAACxE,UAAU;wBAACmW,OAAO,EAAC,SAAS;wBAACM,OAAO,EAAC,OAAO;wBAACtB,EAAE,EAAE;0BAAEE,EAAE,EAAE;wBAAE,CAAE;wBAAAE,QAAA,EAAC;sBAE7D;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACb1R,OAAA,CAACvE,MAAM;wBACLwX,IAAI,EAAC,QAAQ;wBACbtB,OAAO,EAAC,WAAW;wBACnBN,KAAK,EAAC,SAAS;wBACf+E,SAAS,eAAEpW,OAAA,CAACN,QAAQ;0BAAA6R,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAE;wBACxBsB,OAAO,EAAEA,CAAA,KAAM1G,kCAAkC,CAACpF,IAAI,CAAE;wBACxDmM,QAAQ,EAAEtP,mBAAoB;wBAC9B4M,EAAE,EAAE;0BACF8D,EAAE,EAAE,CAAC;0BACL7C,UAAU,EAAE,MAAM;0BAClByE,aAAa,EAAE,MAAM;0BACrBC,SAAS,EAAE,CAAC;0BACZ,SAAS,EAAE;4BACTA,SAAS,EAAE,CAAC;4BACZC,SAAS,EAAE;0BACb,CAAC;0BACDC,SAAS,EAAE,mBAAmB;0BAC9B,kBAAkB,EAAE;4BAClB,IAAI,EAAE;8BACJF,SAAS,EAAE;4BACb,CAAC;4BACD,KAAK,EAAE;8BACLA,SAAS,EAAE;4BACb,CAAC;4BACD,MAAM,EAAE;8BACNA,SAAS,EAAE;4BACb;0BACF;wBACF,CAAE;wBAAAvF,QAAA,EACH;sBAED;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAEX,CAAC,EAAE,CAAC,eAGJ1R,OAAA,CAACrE,IAAI;YAACyW,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAtB,QAAA,eAChB/Q,OAAA,CAAC3C,OAAO;cAACsT,EAAE,EAAE;gBAAE4C,EAAE,EAAE;cAAE,CAAE;cAAAxC,QAAA,eACrB/Q,OAAA,CAACxE,UAAU;gBAACmW,OAAO,EAAC,OAAO;gBAACN,KAAK,EAAC,gBAAgB;gBAAAN,QAAA,EAAC;cAEnD;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEP1R,OAAA,CAACrE,IAAI;YAACyW,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACvB/Q,OAAA,CAAC9D,SAAS;cACRqW,SAAS;cACTqB,KAAK,EAAC,8BAA2B;cACjCzF,IAAI,EAAC,QAAQ;cACbvC,KAAK,EAAE3H,QAAQ,CAACU,oBAAqB;cACrC8N,QAAQ,EAAGC,CAAC,IAAKhH,gBAAgB,CAAC,sBAAsB,EAAEgH,CAAC,CAACC,MAAM,CAAC/G,KAAK,CAAE;cAC1EmK,UAAU,EAAC;YAA6B;cAAAxE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEP1R,OAAA,CAACrE,IAAI;YAACyW,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACvB/Q,OAAA,CAAC9D,SAAS;cACRqW,SAAS;cACTqB,KAAK,EAAC,gBAAa;cACnBzF,IAAI,EAAC,QAAQ;cACbvC,KAAK,EAAE3H,QAAQ,CAACW,OAAQ;cACxB6N,QAAQ,EAAGC,CAAC,IAAKhH,gBAAgB,CAAC,SAAS,EAAEgH,CAAC,CAACC,MAAM,CAAC/G,KAAK,CAAE;cAC7DmK,UAAU,EAAC;YAAkB;cAAAxE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEP1R,OAAA,CAACrE,IAAI;YAACyW,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACvB/Q,OAAA,CAAC9D,SAAS;cACRqW,SAAS;cACTqB,KAAK,EAAC,uBAAuB;cAC7BzF,IAAI,EAAC,QAAQ;cACbvC,KAAK,EAAE3H,QAAQ,CAACY,cAAe;cAC/B4N,QAAQ,EAAGC,CAAC,IAAKhH,gBAAgB,CAAC,gBAAgB,EAAEgH,CAAC,CAACC,MAAM,CAAC/G,KAAK,CAAE;cACpEmK,UAAU,EAAC;YAAgC;cAAAxE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEP1R,OAAA,CAACrE,IAAI;YAACyW,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACvB/Q,OAAA,CAAC9D,SAAS;cACRqW,SAAS;cACTqB,KAAK,EAAC,oBAAoB;cAC1BzF,IAAI,EAAC,QAAQ;cACbvC,KAAK,EAAE3H,QAAQ,CAACa,YAAa;cAC7B2N,QAAQ,EAAGC,CAAC,IAAKhH,gBAAgB,CAAC,cAAc,EAAEgH,CAAC,CAACC,MAAM,CAAC/G,KAAK,CAAE;cAClEmK,UAAU,EAAC;YAA2B;cAAAxE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEP1R,OAAA,CAACrE,IAAI;YAACyW,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACvB/Q,OAAA,CAAC7D,WAAW;cAACoW,SAAS;cAAAxB,QAAA,gBACpB/Q,OAAA,CAAC5D,UAAU;gBAAA2U,QAAA,EAAC;cAAgB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACzC1R,OAAA,CAAC3D,MAAM;gBACLuP,KAAK,EAAE3H,QAAQ,CAACc,gBAAiB;gBACjC0N,QAAQ,EAAGC,CAAC,IAAKhH,gBAAgB,CAAC,kBAAkB,EAAEgH,CAAC,CAACC,MAAM,CAAC/G,KAAK,CAAE;gBACtEgI,KAAK,EAAC,kBAAkB;gBAAA7C,QAAA,gBAExB/Q,OAAA,CAAC1D,QAAQ;kBAACsP,KAAK,EAAC,UAAU;kBAAAmF,QAAA,eACxB/Q,OAAA,CAAC1C,KAAK;oBAAC0T,SAAS,EAAC,KAAK;oBAACE,UAAU,EAAC,QAAQ;oBAACD,OAAO,EAAE,CAAE;oBAAAF,QAAA,gBACpD/Q,OAAA,CAAC5B,SAAS;sBAACiT,KAAK,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC7B1R,OAAA,CAACxE,UAAU;sBAAAuV,QAAA,EAAC;oBAAQ;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACX1R,OAAA,CAAC1D,QAAQ;kBAACsP,KAAK,EAAC,cAAc;kBAAAmF,QAAA,eAC5B/Q,OAAA,CAAC1C,KAAK;oBAAC0T,SAAS,EAAC,KAAK;oBAACE,UAAU,EAAC,QAAQ;oBAACD,OAAO,EAAE,CAAE;oBAAAF,QAAA,gBACpD/Q,OAAA,CAACZ,SAAS;sBAACiS,KAAK,EAAC;oBAAO;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC3B1R,OAAA,CAACxE,UAAU;sBAAAuV,QAAA,EAAC;oBAAY;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACX1R,OAAA,CAAC1D,QAAQ;kBAACsP,KAAK,EAAC,eAAe;kBAAAmF,QAAA,eAC7B/Q,OAAA,CAAC1C,KAAK;oBAAC0T,SAAS,EAAC,KAAK;oBAACE,UAAU,EAAC,QAAQ;oBAACD,OAAO,EAAE,CAAE;oBAAAF,QAAA,gBACpD/Q,OAAA,CAAC1B,WAAW;sBAAC+S,KAAK,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC/B1R,OAAA,CAACxE,UAAU;sBAAAuV,QAAA,EAAC;oBAAa;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEP1R,OAAA,CAACrE,IAAI;YAACyW,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAtB,QAAA,eAChB/Q,OAAA,CAAC9D,SAAS;cACRqW,SAAS;cACTqB,KAAK,EAAC,MAAM;cACZ6C,SAAS;cACTtH,IAAI,EAAE,CAAE;cACRvD,KAAK,EAAE3H,QAAQ,CAACS,IAAK;cACrB+N,QAAQ,EAAGC,CAAC,IAAKhH,gBAAgB,CAAC,MAAM,EAAEgH,CAAC,CAACC,MAAM,CAAC/G,KAAK,CAAE;cAC1D4G,WAAW,EAAC;YAAkF;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/F;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChB1R,OAAA,CAAC/D,aAAa;QAAA8U,QAAA,gBACZ/Q,OAAA,CAACvE,MAAM;UAACuX,OAAO,EAAEvH,WAAY;UAAAsF,QAAA,EAAC;QAAO;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC9C1R,OAAA,CAACvE,MAAM;UACLuX,OAAO,EAAErG,0BAA2B;UACpCgF,OAAO,EAAC,WAAW;UACnB0B,QAAQ,EAAE3S,OAAO,IAAI,CAACuD,QAAQ,CAACE,OAAO,IAAI,CAACF,QAAQ,CAACG,YAAY,IAAI,CAACH,QAAQ,CAACI,YAAY,IAAI,CAACJ,QAAQ,CAACO,iBAAkB;UAAAuM,QAAA,EAEzH/N,UAAU,KAAK,QAAQ,GAAG,qBAAqB,GAAG;QAAiB;UAAAuO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEb,CAAC;;EAED;EACA,MAAMgF,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI1T,UAAU,KAAK,MAAM,IAAI,CAACE,YAAY,EAAE,OAAO,IAAI;IAEvD,oBACElD,OAAA,CAAClE,MAAM;MAAC4H,IAAI,EAAEZ,UAAW;MAACuS,OAAO,EAAE5J,WAAY;MAAC6J,QAAQ,EAAC,IAAI;MAAC/C,SAAS;MAAAxB,QAAA,gBACrE/Q,OAAA,CAACjE,WAAW;QAAAgV,QAAA,GAAC,4BACe,EAAC7N,YAAY,CAACgH,kBAAkB;MAAA;QAAAqH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC,eACd1R,OAAA,CAAChE,aAAa;QAAA+U,QAAA,eACZ/Q,OAAA,CAACrE,IAAI;UAACwW,SAAS;UAAClB,OAAO,EAAE,CAAE;UAACN,EAAE,EAAE;YAAE8D,EAAE,EAAE;UAAE,CAAE;UAAA1D,QAAA,gBACxC/Q,OAAA,CAACrE,IAAI;YAACyW,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACvB/Q,OAAA,CAACpE,IAAI;cAAC+V,OAAO,EAAC,UAAU;cAAAZ,QAAA,eACtB/Q,OAAA,CAACnE,WAAW;gBAAAkV,QAAA,gBACV/Q,OAAA,CAACxE,UAAU;kBAACmW,OAAO,EAAC,IAAI;kBAACgF,YAAY;kBAAA5F,QAAA,EAAC;gBAEtC;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb1R,OAAA,CAACxE,UAAU;kBAACmW,OAAO,EAAC,OAAO;kBAACN,KAAK,EAAC,gBAAgB;kBAAAN,QAAA,GAAC,WACxC,eAAA/Q,OAAA;oBAAA+Q,QAAA,EAAS7N,YAAY,CAACiB;kBAAO;oBAAAoN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,eACb1R,OAAA,CAACxE,UAAU;kBAACmW,OAAO,EAAC,OAAO;kBAACN,KAAK,EAAC,gBAAgB;kBAAAN,QAAA,GAAC,sBAC7B,eAAA/Q,OAAA;oBAAA+Q,QAAA,GAAS7N,YAAY,CAACoB,kBAAkB,EAAC,IAAE;kBAAA;oBAAAiN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEP1R,OAAA,CAACrE,IAAI;YAACyW,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvB,QAAA,eACvB/Q,OAAA,CAACpE,IAAI;cAAC+V,OAAO,EAAC,UAAU;cAAAZ,QAAA,eACtB/Q,OAAA,CAACnE,WAAW;gBAAAkV,QAAA,gBACV/Q,OAAA,CAACxE,UAAU;kBAACmW,OAAO,EAAC,IAAI;kBAACgF,YAAY;kBAAA5F,QAAA,EAAC;gBAEtC;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb1R,OAAA,CAACxE,UAAU;kBAACmW,OAAO,EAAC,OAAO;kBAACN,KAAK,EAAC,gBAAgB;kBAAAN,QAAA,GAAC,UACzC,eAAA/Q,OAAA;oBAAA+Q,QAAA,EAAS7N,YAAY,CAACgH;kBAAkB;oBAAAqH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,eACb1R,OAAA,CAACxE,UAAU;kBAACmW,OAAO,EAAC,OAAO;kBAACN,KAAK,EAAC,gBAAgB;kBAAAN,QAAA,GAAC,QAC3C,eAAA/Q,OAAA;oBAAA+Q,QAAA,EAAS,IAAI/I,IAAI,CAAC9E,YAAY,CAACkF,mBAAmB,CAAC,CAACgH,kBAAkB,CAAC;kBAAC;oBAAAmC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9E,CAAC,eACb1R,OAAA,CAACxE,UAAU;kBAACmW,OAAO,EAAC,OAAO;kBAACN,KAAK,EAAC,gBAAgB;kBAAAN,QAAA,GAAC,aACtC,eAAA/Q,OAAA;oBAAA+Q,QAAA,EAAS7N,YAAY,CAAClB,SAAS,IAAIkB,YAAY,CAACkB;kBAAY;oBAAAmN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEP1R,OAAA,CAACrE,IAAI;YAACyW,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAtB,QAAA,eAChB/Q,OAAA,CAACpE,IAAI;cAAC+V,OAAO,EAAC,UAAU;cAAAZ,QAAA,eACtB/Q,OAAA,CAACnE,WAAW;gBAAAkV,QAAA,gBACV/Q,OAAA,CAACxE,UAAU;kBAACmW,OAAO,EAAC,IAAI;kBAACgF,YAAY;kBAAA5F,QAAA,EAAC;gBAEtC;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb1R,OAAA,CAACrE,IAAI;kBAACwW,SAAS;kBAAClB,OAAO,EAAE,CAAE;kBAAAF,QAAA,gBACzB/Q,OAAA,CAACrE,IAAI;oBAACyW,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAtB,QAAA,gBACf/Q,OAAA,CAACxE,UAAU;sBAACmW,OAAO,EAAC,OAAO;sBAACN,KAAK,EAAC,gBAAgB;sBAAAN,QAAA,EAAC;oBAEnD;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACb1R,OAAA,CAACzC,IAAI;sBACH0V,IAAI,EAAC,OAAO;sBACZW,KAAK,EAAE1Q,YAAY,CAACqB,iBAAkB;sBACtC8M,KAAK,EAAEnO,YAAY,CAACqB,iBAAiB,KAAK,IAAI,GAAG,SAAS,GAAG;oBAAQ;sBAAAgN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACP1R,OAAA,CAACrE,IAAI;oBAACyW,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAtB,QAAA,gBACf/Q,OAAA,CAACxE,UAAU;sBAACmW,OAAO,EAAC,OAAO;sBAACN,KAAK,EAAC,gBAAgB;sBAAAN,QAAA,EAAC;oBAEnD;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACb1R,OAAA,CAACzC,IAAI;sBACH0V,IAAI,EAAC,OAAO;sBACZW,KAAK,EAAE,GAAG1Q,YAAY,CAACsB,iBAAiB,KAAM;sBAC9C6M,KAAK,EAAEjH,UAAU,CAAClH,YAAY,CAACsB,iBAAiB,CAAC,IAAI,GAAG,GAAG,SAAS,GAAG;oBAAU;sBAAA+M,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACP1R,OAAA,CAACrE,IAAI;oBAACyW,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAtB,QAAA,gBACf/Q,OAAA,CAACxE,UAAU;sBAACmW,OAAO,EAAC,OAAO;sBAACN,KAAK,EAAC,gBAAgB;sBAAAN,QAAA,EAAC;oBAEnD;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACb1R,OAAA,CAACzC,IAAI;sBACH0V,IAAI,EAAC,OAAO;sBACZW,KAAK,EAAE1Q,YAAY,CAACuB,iBAAkB;sBACtC4M,KAAK,EAAEnO,YAAY,CAACuB,iBAAiB,KAAK,IAAI,GAAG,SAAS,GAAG;oBAAQ;sBAAA8M,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAENxO,YAAY,CAACwB,IAAI,iBAChB1E,OAAA,CAACrE,IAAI;YAACyW,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAtB,QAAA,eAChB/Q,OAAA,CAACpE,IAAI;cAAC+V,OAAO,EAAC,UAAU;cAAAZ,QAAA,eACtB/Q,OAAA,CAACnE,WAAW;gBAAAkV,QAAA,gBACV/Q,OAAA,CAACxE,UAAU;kBAACmW,OAAO,EAAC,IAAI;kBAACgF,YAAY;kBAAA5F,QAAA,EAAC;gBAEtC;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb1R,OAAA,CAACxE,UAAU;kBAACmW,OAAO,EAAC,OAAO;kBAAAZ,QAAA,EACxB7N,YAAY,CAACwB;gBAAI;kBAAA6M,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChB1R,OAAA,CAAC/D,aAAa;QAAA8U,QAAA,gBACZ/Q,OAAA,CAACvE,MAAM;UAACuX,OAAO,EAAEvH,WAAY;UAAAsF,QAAA,EAAC;QAAM;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC7C1R,OAAA,CAACvE,MAAM;UACLuX,OAAO,EAAEA,CAAA,KAAMhG,iBAAiB,CAAC9J,YAAY,CAAE;UAC/CyO,OAAO,EAAC,WAAW;UACnB0B,QAAQ,EAAE3S,OAAQ;UAAAqQ,QAAA,EACnB;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEb,CAAC;EAID,oBACE1R,OAAA,CAACpC,SAAS;IAAC0X,QAAQ,EAAC,IAAI;IAAC3E,EAAE,EAAE;MAAEiG,EAAE,EAAE;IAAE,CAAE;IAAA7F,QAAA,GAEpCL,eAAe,CAAC,CAAC,EAGjB,CAAChQ,OAAO,IAAIqD,mBAAmB,kBAC9B/D,OAAA,CAACzE,GAAG;MAACoV,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAE,QAAA,gBACjB/Q,OAAA,CAACvC,cAAc;QAAA8T,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACjB7N,QAAQ,GAAG,CAAC,iBACX7D,OAAA,CAACxE,UAAU;QAACmW,OAAO,EAAC,SAAS;QAACN,KAAK,EAAC,gBAAgB;QAACV,EAAE,EAAE;UAAE8D,EAAE,EAAE;QAAE,CAAE;QAAA1D,QAAA,GAAC,iBACnD,EAAClN,QAAQ,EAAC,GAC3B;MAAA;QAAA0N,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CACb;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,eAGD1R,OAAA,CAACtE,KAAK;MAACiV,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAE,QAAA,eACnB/Q,OAAA,CAAC/C,IAAI;QACH2O,KAAK,EAAEhL,SAAU;QACjB6R,QAAQ,EAAEvH,eAAgB;QAC1B2L,cAAc,EAAC,SAAS;QACxBC,SAAS,EAAC,SAAS;QACnBnF,OAAO,EAAC,WAAW;QAAAZ,QAAA,gBAEnB/Q,OAAA,CAAC9C,GAAG;UACF0W,KAAK,eACH5T,OAAA,CAACzE,GAAG;YAAAwV,QAAA,gBACF/Q,OAAA,CAACxE,UAAU;cAACmW,OAAO,EAAC,OAAO;cAACC,UAAU,EAAC,MAAM;cAAAb,QAAA,EAAC;YAE9C;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb1R,OAAA,CAACxE,UAAU;cAACmW,OAAO,EAAC,SAAS;cAACN,KAAK,EAAC,gBAAgB;cAAAN,QAAA,GACjDzP,YAAY,CAACsG,MAAM,EAAC,cACvB;YAAA;cAAA2J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACF1R,OAAA,CAAC9C,GAAG;UACF0W,KAAK,eACH5T,OAAA,CAACzE,GAAG;YAAAwV,QAAA,gBACF/Q,OAAA,CAACxE,UAAU;cAACmW,OAAO,EAAC,OAAO;cAACC,UAAU,EAAC,MAAM;cAAAb,QAAA,EAAC;YAE9C;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb1R,OAAA,CAACxE,UAAU;cAACmW,OAAO,EAAC,SAAS;cAACN,KAAK,EAAC,gBAAgB;cAAAN,QAAA,GACjDvP,sBAAsB,CAACoG,MAAM,EAAC,iBACjC;YAAA;cAAA2J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAGPQ,sBAAsB,CAAC,CAAC,EAGxB,CAACxR,OAAO,IAAIE,SAAS,KAAK,CAAC,IAAImT,eAAe,CAAC,CAAC,EAChD,CAACrT,OAAO,IAAIE,SAAS,KAAK,CAAC,IAAIgU,yBAAyB,CAAC,CAAC,EAG1DQ,0BAA0B,CAAC,CAAC,EAC5BsB,gBAAgB,CAAC,CAAC,eAGnB1W,OAAA,CAACrC,QAAQ;MACP+F,IAAI,EAAEF,QAAQ,CAACE,IAAK;MACpBqT,gBAAgB,EAAE,IAAK;MACvB1B,OAAO,EAAEzM,aAAc;MACvBoO,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAQ,CAAE;MAAAnG,QAAA,eAE1D/Q,OAAA,CAACvD,KAAK;QAAC4Y,OAAO,EAAEzM,aAAc;QAAChF,QAAQ,EAAEJ,QAAQ,CAACI,QAAS;QAAC+M,EAAE,EAAE;UAAEmB,KAAK,EAAE;QAAO,CAAE;QAAAf,QAAA,EAC/EvN,QAAQ,CAACG;MAAO;QAAA4N,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGF,CAAC;AAEhB,CAAC,kCAAC;AAACyF,GAAA,GAxjEGhX,0BAA0B;AA0jEhC,eAAeA,0BAA0B;AAAC,IAAAE,EAAA,EAAA8W,GAAA;AAAAC,YAAA,CAAA/W,EAAA;AAAA+W,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}