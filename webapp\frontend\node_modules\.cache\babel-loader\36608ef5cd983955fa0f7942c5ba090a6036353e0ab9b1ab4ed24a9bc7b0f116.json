{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\CertificazioneCavi.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';\nimport { Box, Typography, Button, Paper, Grid, Card, CardContent, CardActions, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, List, ListItem, ListItemText, ListItemIcon, ListItemButton, Alert, CircularProgress, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Divider, IconButton, Chip } from '@mui/material';\nimport { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, PictureAsPdf as PdfIcon, Search as SearchIcon, FilterList as FilterIcon, Save as SaveIcon, Clear as ClearIcon, ViewList as ViewListIcon, Build as BuildIcon } from '@mui/icons-material';\nimport certificazioneService from '../../services/certificazioneService';\nimport caviService from '../../services/caviService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CertificazioneCavi = /*#__PURE__*/_s(/*#__PURE__*/forwardRef(_c = _s(({\n  cantiereId,\n  onSuccess,\n  onError,\n  dialogOnly = false\n}, ref) => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [certificazioni, setCertificazioni] = useState([]);\n  const [cavi, setCavi] = useState([]);\n  const [strumenti, setStrumenti] = useState([]);\n  const [selectedOption, setSelectedOption] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedCertificazione, setSelectedCertificazione] = useState(null);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [filtroCavo, setFiltroCavo] = useState('');\n  const [selectedStrumento, setSelectedStrumento] = useState(null);\n  const [strumentoFormData, setStrumentoFormData] = useState({\n    nome: '',\n    marca: '',\n    modello: '',\n    numero_serie: '',\n    data_calibrazione: '',\n    data_scadenza_calibrazione: '',\n    certificato_calibrazione: '',\n    note: ''\n  });\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    id_strumento: '',\n    lunghezza_misurata: '',\n    valore_continuita: 'OK',\n    valore_isolamento: '',\n    valore_resistenza: 'OK',\n    note: ''\n  });\n\n  // Carica le certificazioni\n  const loadCertificazioni = async (filtroCavo = '') => {\n    try {\n      setLoading(true);\n      const data = await certificazioneService.getCertificazioni(cantiereId, filtroCavo);\n      setCertificazioni(data);\n    } catch (error) {\n      onError('Errore nel caricamento delle certificazioni');\n      console.error('Errore nel caricamento delle certificazioni:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica i cavi disponibili\n  const loadCavi = async () => {\n    try {\n      setLoading(true);\n      const data = await caviService.getCavi(cantiereId);\n      setCavi(data);\n    } catch (error) {\n      onError('Errore nel caricamento dei cavi');\n      console.error('Errore nel caricamento dei cavi:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica gli strumenti certificati\n  const loadStrumenti = async () => {\n    try {\n      setLoading(true);\n      const data = await certificazioneService.getStrumenti(cantiereId);\n      setStrumenti(data);\n    } catch (error) {\n      onError('Errore nel caricamento degli strumenti');\n      console.error('Errore nel caricamento degli strumenti:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Espone i metodi tramite ref\n  useImperativeHandle(ref, () => ({\n    handleOptionSelect,\n    createCertificationForCavo: cavo => {\n      // Metodo per aprire direttamente la certificazione con un cavo preselezionato\n      loadStrumenti();\n      handleCavoSelect(cavo);\n      setOpenDialog(true);\n    },\n    viewCertificationForCavo: cavo => {\n      // Metodo per visualizzare la certificazione di un cavo specifico\n      loadCertificazioni(cavo.id_cavo);\n      setSelectedOption('visualizzaCertificazioni');\n    }\n  }));\n\n  // Carica i dati all'avvio del componente\n  useEffect(() => {\n    loadCertificazioni();\n  }, [cantiereId]);\n\n  // Gestisce la selezione di un'opzione dal menu\n  const handleOptionSelect = option => {\n    setSelectedOption(option);\n    if (option === 'visualizzaCertificazioni') {\n      loadCertificazioni();\n    } else if (option === 'filtraCertificazioni') {\n      setDialogType('filtraCertificazioni');\n      setOpenDialog(true);\n    } else if (option === 'creaCertificazione') {\n      loadCavi();\n      loadStrumenti();\n      setDialogType('selezionaCavo');\n      setOpenDialog(true);\n    } else if (option === 'dettagliCertificazione') {\n      loadCertificazioni();\n      setDialogType('selezionaCertificazione');\n      setOpenDialog(true);\n    } else if (option === 'generaPdf') {\n      loadCertificazioni();\n      setDialogType('selezionaCertificazionePdf');\n      setOpenDialog(true);\n    } else if (option === 'eliminaCertificazione') {\n      loadCertificazioni();\n      setDialogType('eliminaCertificazione');\n      setOpenDialog(true);\n    } else if (option === 'gestioneStrumenti') {\n      loadStrumenti();\n      setDialogType('gestioneStrumenti');\n      setOpenDialog(true);\n    }\n  };\n\n  // Gestisce la chiusura del dialog\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setSelectedCertificazione(null);\n    setSelectedCavo(null);\n    setSelectedStrumento(null);\n    setFormData({\n      id_cavo: '',\n      id_strumento: '',\n      lunghezza_misurata: '',\n      valore_continuita: 'OK',\n      valore_isolamento: '',\n      valore_resistenza: 'OK',\n      note: ''\n    });\n    setStrumentoFormData({\n      nome: '',\n      marca: '',\n      modello: '',\n      numero_serie: '',\n      data_calibrazione: '',\n      data_scadenza_calibrazione: '',\n      certificato_calibrazione: '',\n      note: ''\n    });\n  };\n\n  // Gestisce la selezione di una certificazione\n  const handleCertificazioneSelect = certificazione => {\n    setSelectedCertificazione(certificazione);\n    if (dialogType === 'selezionaCertificazione') {\n      setDialogType('dettagliCertificazione');\n    } else if (dialogType === 'selezionaCertificazionePdf') {\n      handleGeneraPdf(certificazione.id_certificazione);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = cavo => {\n    setSelectedCavo(cavo);\n    setFormData({\n      ...formData,\n      id_cavo: cavo.id_cavo,\n      lunghezza_misurata: cavo.metratura_reale || '0'\n    });\n    setDialogType('creaCertificazione');\n  };\n\n  // Gestisce il cambio dei valori nel form\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n  };\n\n  // Gestisce il filtro per cavo\n  const handleFiltroCavo = () => {\n    loadCertificazioni(filtroCavo);\n    handleCloseDialog();\n  };\n\n  // Funzione per collegare automaticamente un cavo\n  const collegaCavoAutomatico = async (cavoId, responsabile = 'cantiere') => {\n    try {\n      // Importa il servizio cavi\n      const caviService = (await import('../../services/caviService')).default;\n      let partenzaCollegata = false;\n      let arrivoCollegato = false;\n\n      // Prova a collegare il lato partenza\n      try {\n        await caviService.collegaCavo(cantiereId, cavoId, 'partenza', responsabile);\n        partenzaCollegata = true;\n        console.log('Lato partenza collegato con successo');\n      } catch (error) {\n        if (error.detail && error.detail.includes('già collegato')) {\n          partenzaCollegata = true;\n          console.log('Lato partenza già collegato');\n        } else {\n          console.error('Errore nel collegamento lato partenza:', error);\n          throw error;\n        }\n      }\n\n      // Prova a collegare il lato arrivo\n      try {\n        await caviService.collegaCavo(cantiereId, cavoId, 'arrivo', responsabile);\n        arrivoCollegato = true;\n        console.log('Lato arrivo collegato con successo');\n      } catch (error) {\n        if (error.detail && error.detail.includes('già collegato')) {\n          arrivoCollegato = true;\n          console.log('Lato arrivo già collegato');\n        } else {\n          console.error('Errore nel collegamento lato arrivo:', error);\n          throw error;\n        }\n      }\n      return partenzaCollegata && arrivoCollegato;\n    } catch (error) {\n      console.error('Errore nel collegamento automatico:', error);\n      throw error;\n    }\n  };\n\n  // Gestisce la creazione di una certificazione\n  const handleCreaCertificazione = async () => {\n    try {\n      if (!formData.id_cavo || !formData.id_strumento || !formData.valore_isolamento) {\n        onError('Compila tutti i campi obbligatori');\n        return;\n      }\n\n      // Verifica se il cavo è completamente collegato\n      const cavo = cavi.find(c => c.id_cavo === formData.id_cavo);\n      if (cavo) {\n        const isCollegato = cavo.collegamenti === 3;\n        if (!isCollegato) {\n          const statoCollegamenti = cavo.collegamenti === 0 ? 'Non collegato' : cavo.collegamenti === 1 ? 'Solo partenza collegata' : cavo.collegamenti === 2 ? 'Solo arrivo collegato' : 'Stato sconosciuto';\n          const conferma = window.confirm(`ATTENZIONE: Il cavo ${cavo.id_cavo} non risulta completamente collegato.\\n\\n` + `Stato collegamenti: ${statoCollegamenti}\\n\\n` + `Vuoi collegare automaticamente entrambi i lati del cavo e procedere con la certificazione?\\n\\n` + `(Il sistema collegherà automaticamente il cavo a \"cantiere\" su entrambi i lati)`);\n          if (!conferma) {\n            return;\n          }\n\n          // Collega automaticamente il cavo\n          try {\n            setLoading(true);\n            await collegaCavoAutomatico(cavo.id_cavo, 'cantiere');\n            onSuccess('Cavo collegato automaticamente su entrambi i lati');\n\n            // Ricarica i cavi per aggiornare lo stato\n            await loadCavi();\n          } catch (error) {\n            onError('Errore nel collegamento automatico: ' + (error.message || 'Errore sconosciuto'));\n            return;\n          }\n        }\n      }\n      setLoading(true);\n      await certificazioneService.createCertificazione(cantiereId, formData);\n      onSuccess('Certificazione creata con successo');\n      handleCloseDialog();\n      loadCertificazioni();\n    } catch (error) {\n      onError('Errore nella creazione della certificazione: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nella creazione della certificazione:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce l'eliminazione di una certificazione\n  const handleEliminaCertificazione = async () => {\n    try {\n      if (!selectedCertificazione) {\n        onError('Seleziona una certificazione da eliminare');\n        return;\n      }\n      setLoading(true);\n      await certificazioneService.deleteCertificazione(cantiereId, selectedCertificazione.id_certificazione);\n      onSuccess('Certificazione eliminata con successo');\n      handleCloseDialog();\n      loadCertificazioni();\n    } catch (error) {\n      onError('Errore nell\\'eliminazione della certificazione: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nell\\'eliminazione della certificazione:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la generazione del PDF\n  const handleGeneraPdf = async idCertificazione => {\n    try {\n      setLoading(true);\n      const response = await certificazioneService.generatePdf(cantiereId, idCertificazione);\n\n      // Apri il PDF in una nuova finestra\n      window.open(response.file_url, '_blank');\n      onSuccess('PDF generato con successo');\n      handleCloseDialog();\n    } catch (error) {\n      onError('Errore nella generazione del PDF: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nella generazione del PDF:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di uno strumento\n  const handleStrumentoSelect = strumento => {\n    setSelectedStrumento(strumento);\n    setStrumentoFormData({\n      nome: strumento.nome || '',\n      marca: strumento.marca || '',\n      modello: strumento.modello || '',\n      numero_serie: strumento.numero_serie || '',\n      data_calibrazione: strumento.data_calibrazione || '',\n      data_scadenza_calibrazione: strumento.data_scadenza_calibrazione || '',\n      certificato_calibrazione: strumento.certificato_calibrazione || '',\n      note: strumento.note || ''\n    });\n    setDialogType('modificaStrumento');\n  };\n\n  // Gestisce il cambio dei valori nel form strumento\n  const handleStrumentoFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setStrumentoFormData({\n      ...strumentoFormData,\n      [name]: value\n    });\n  };\n\n  // Gestisce la creazione di un nuovo strumento\n  const handleCreaStrumento = () => {\n    setSelectedStrumento(null);\n    setStrumentoFormData({\n      nome: '',\n      marca: '',\n      modello: '',\n      numero_serie: '',\n      data_calibrazione: '',\n      data_scadenza_calibrazione: '',\n      certificato_calibrazione: '',\n      note: ''\n    });\n    setDialogType('creaStrumento');\n  };\n\n  // Gestisce il salvataggio dello strumento\n  const handleSalvaStrumento = async () => {\n    try {\n      if (!strumentoFormData.nome || !strumentoFormData.marca || !strumentoFormData.modello || !strumentoFormData.numero_serie) {\n        onError('Compila tutti i campi obbligatori');\n        return;\n      }\n      if (!strumentoFormData.data_calibrazione || !strumentoFormData.data_scadenza_calibrazione) {\n        onError('Le date di calibrazione e scadenza sono obbligatorie');\n        return;\n      }\n      setLoading(true);\n      if (selectedStrumento) {\n        await certificazioneService.updateStrumento(cantiereId, selectedStrumento.id_strumento, strumentoFormData);\n        onSuccess('Strumento aggiornato con successo');\n      } else {\n        await certificazioneService.createStrumento(cantiereId, strumentoFormData);\n        onSuccess('Strumento creato con successo');\n      }\n      handleCloseDialog();\n      loadStrumenti();\n    } catch (error) {\n      onError('Errore nel salvataggio dello strumento: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nel salvataggio dello strumento:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce l'eliminazione di uno strumento\n  const handleEliminaStrumento = async strumento => {\n    try {\n      if (window.confirm(`Sei sicuro di voler eliminare lo strumento \"${strumento.nome} ${strumento.marca} ${strumento.modello}\"?`)) {\n        setLoading(true);\n        await certificazioneService.deleteStrumento(cantiereId, strumento.id_strumento);\n        onSuccess('Strumento eliminato con successo');\n        loadStrumenti();\n      }\n    } catch (error) {\n      onError('Errore nell\\'eliminazione dello strumento: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nell\\'eliminazione dello strumento:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza le certificazioni in formato tabella\n  const renderCertificazioniTable = () => {\n    if (certificazioni.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        children: \"Nessuna certificazione trovata\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 483,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Paper,\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        size: \"small\",\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"ID\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 493,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Data\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 494,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Operatore\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 495,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Strumento\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 496,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Lunghezza\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 497,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Isolamento\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 498,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Azioni\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 499,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 490,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: certificazioni.map(cert => /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: cert.id_certificazione\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 505,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: cert.id_cavo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: new Date(cert.data_certificazione).toLocaleDateString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 507,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: cert.operatore\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 508,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: cert.strumento\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 509,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: [cert.lunghezza_misurata, \" m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 510,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: [cert.valore_isolamento, \" M\\u03A9\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 511,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: [/*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                onClick: () => {\n                  setSelectedCertificazione(cert);\n                  setDialogType('dettagliCertificazione');\n                  setOpenDialog(true);\n                },\n                children: /*#__PURE__*/_jsxDEV(SearchIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 521,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 513,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                onClick: () => handleGeneraPdf(cert.id_certificazione),\n                children: /*#__PURE__*/_jsxDEV(PdfIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 527,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 523,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                color: \"error\",\n                onClick: () => {\n                  setSelectedCertificazione(cert);\n                  setDialogType('eliminaCertificazione');\n                  setOpenDialog(true);\n                },\n                children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 538,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 529,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 512,\n              columnNumber: 17\n            }, this)]\n          }, cert.id_certificazione, true, {\n            fileName: _jsxFileName,\n            lineNumber: 504,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 502,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 489,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 488,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il dialog in base al tipo\n  const renderDialog = () => {\n    if (dialogType === 'filtraCertificazioni') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"sm\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Filtra Certificazioni per Cavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 554,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"ID Cavo\",\n              variant: \"outlined\",\n              value: filtroCavo,\n              onChange: e => setFiltroCavo(e.target.value),\n              placeholder: \"Inserisci l'ID del cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 557,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 556,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 555,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => {\n              setFiltroCavo('');\n              loadCertificazioni('');\n              handleCloseDialog();\n            },\n            startIcon: /*#__PURE__*/_jsxDEV(ClearIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 574,\n              columnNumber: 26\n            }, this),\n            children: \"Rimuovi Filtro\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 568,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 578,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleFiltroCavo,\n            disabled: loading,\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 582,\n              columnNumber: 36\n            }, this) : /*#__PURE__*/_jsxDEV(FilterIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 582,\n              columnNumber: 69\n            }, this),\n            children: \"Filtra\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 579,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 567,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 553,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'selezionaCavo') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Seleziona Cavo per Certificazione\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 592,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 595,\n            columnNumber: 15\n          }, this) : cavi.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Nessun cavo disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 597,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(List, {\n            children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(ListItem, {\n              button: true,\n              onClick: () => handleCavoSelect(cavo),\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: cavo.id_cavo,\n                secondary: `${cavo.tipologia || 'N/A'} - Da: ${cavo.ubicazione_partenza || 'N/A'} A: ${cavo.ubicazione_arrivo || 'N/A'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 606,\n                columnNumber: 21\n              }, this)\n            }, cavo.id_cavo, false, {\n              fileName: _jsxFileName,\n              lineNumber: 601,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 599,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 593,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 616,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 615,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 591,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'creaCertificazione') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Crea Nuova Certificazione\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 623,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: [\"Cavo selezionato: \", selectedCavo === null || selectedCavo === void 0 ? void 0 : selectedCavo.id_cavo]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 626,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              gutterBottom: true,\n              children: [\"Tipologia: \", (selectedCavo === null || selectedCavo === void 0 ? void 0 : selectedCavo.tipologia) || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 629,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              gutterBottom: true,\n              children: [\"Metratura: \", (selectedCavo === null || selectedCavo === void 0 ? void 0 : selectedCavo.metratura_reale) || '0', \" m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 632,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {\n              sx: {\n                my: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 636,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 2,\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(FormControl, {\n                  fullWidth: true,\n                  variant: \"outlined\",\n                  children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                    children: \"Strumento Utilizzato\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 641,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Select, {\n                    name: \"id_strumento\",\n                    value: formData.id_strumento,\n                    onChange: handleFormChange,\n                    label: \"Strumento Utilizzato\",\n                    required: true,\n                    children: strumenti.map(strumento => /*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: strumento.id_strumento,\n                      children: [strumento.nome, \" - \", strumento.modello]\n                    }, strumento.id_strumento, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 650,\n                      columnNumber: 25\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 642,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 640,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 639,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  name: \"lunghezza_misurata\",\n                  label: \"Lunghezza Misurata (m)\",\n                  type: \"number\",\n                  fullWidth: true,\n                  variant: \"outlined\",\n                  value: formData.lunghezza_misurata,\n                  onChange: handleFormChange,\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 658,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 657,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(FormControl, {\n                  fullWidth: true,\n                  variant: \"outlined\",\n                  children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                    children: \"Test Continuit\\xE0\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 671,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Select, {\n                    name: \"valore_continuita\",\n                    value: formData.valore_continuita,\n                    onChange: handleFormChange,\n                    label: \"Test Continuit\\xE0\",\n                    children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"OK\",\n                      children: \"OK\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 678,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"NON OK\",\n                      children: \"NON OK\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 679,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 672,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 670,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 669,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  name: \"valore_isolamento\",\n                  label: \"Valore Isolamento (M\\u03A9)\",\n                  fullWidth: true,\n                  variant: \"outlined\",\n                  value: formData.valore_isolamento,\n                  onChange: handleFormChange,\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 684,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 683,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(FormControl, {\n                  fullWidth: true,\n                  variant: \"outlined\",\n                  children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                    children: \"Test Resistenza\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 696,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Select, {\n                    name: \"valore_resistenza\",\n                    value: formData.valore_resistenza,\n                    onChange: handleFormChange,\n                    label: \"Test Resistenza\",\n                    children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"OK\",\n                      children: \"OK\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 703,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"NON OK\",\n                      children: \"NON OK\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 704,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 697,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 695,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 694,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  name: \"note\",\n                  label: \"Note\",\n                  fullWidth: true,\n                  multiline: true,\n                  rows: 3,\n                  variant: \"outlined\",\n                  value: formData.note,\n                  onChange: handleFormChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 709,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 708,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 638,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 625,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 624,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 724,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCreaCertificazione,\n            disabled: loading || !formData.id_strumento || !formData.valore_isolamento,\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 728,\n              columnNumber: 36\n            }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 728,\n              columnNumber: 69\n            }, this),\n            children: \"Salva\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 725,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 723,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 622,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'selezionaCertificazione' || dialogType === 'selezionaCertificazionePdf') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: dialogType === 'selezionaCertificazione' ? 'Seleziona Certificazione da Visualizzare' : 'Seleziona Certificazione per PDF'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 738,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 745,\n            columnNumber: 15\n          }, this) : certificazioni.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Nessuna certificazione trovata\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 747,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(List, {\n            children: certificazioni.map(cert => /*#__PURE__*/_jsxDEV(ListItem, {\n              button: true,\n              onClick: () => handleCertificazioneSelect(cert),\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: `ID: ${cert.id_certificazione} - Cavo: ${cert.id_cavo}`,\n                secondary: `Data: ${new Date(cert.data_certificazione).toLocaleDateString()} - Operatore: ${cert.operatore}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 756,\n                columnNumber: 21\n              }, this)\n            }, cert.id_certificazione, false, {\n              fileName: _jsxFileName,\n              lineNumber: 751,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 749,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 743,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 766,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 765,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 737,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'dettagliCertificazione') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Dettagli Certificazione\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 773,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: !selectedCertificazione ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 776,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 2,\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  children: \"ID Certificazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 781,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  gutterBottom: true,\n                  children: selectedCertificazione.id_certificazione\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 782,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 780,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  children: \"Cavo:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 785,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  gutterBottom: true,\n                  children: selectedCertificazione.id_cavo\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 786,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 784,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  children: \"Data Certificazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 789,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  gutterBottom: true,\n                  children: new Date(selectedCertificazione.data_certificazione).toLocaleDateString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 790,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 788,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  children: \"Operatore:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 795,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  gutterBottom: true,\n                  children: selectedCertificazione.operatore\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 796,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 794,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  children: \"Strumento:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 799,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  gutterBottom: true,\n                  children: selectedCertificazione.strumento\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 800,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 798,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  children: \"ID Strumento:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 803,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  gutterBottom: true,\n                  children: selectedCertificazione.id_strumento\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 804,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 802,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  children: \"Lunghezza Misurata:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 807,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  gutterBottom: true,\n                  children: [selectedCertificazione.lunghezza_misurata, \" m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 808,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 806,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  children: \"Test Continuit\\xE0:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 811,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  gutterBottom: true,\n                  children: selectedCertificazione.valore_continuita\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 812,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 810,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  children: \"Valore Isolamento:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 815,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  gutterBottom: true,\n                  children: [selectedCertificazione.valore_isolamento, \" M\\u03A9\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 816,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 814,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  children: \"Test Resistenza:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 819,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  gutterBottom: true,\n                  children: selectedCertificazione.valore_resistenza\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 820,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 818,\n                columnNumber: 19\n              }, this), selectedCertificazione.note && /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  children: \"Note:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 824,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  gutterBottom: true,\n                  children: selectedCertificazione.note\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 825,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 823,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 779,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mt: 2,\n                display: 'flex',\n                justifyContent: 'flex-end'\n              },\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                startIcon: /*#__PURE__*/_jsxDEV(PdfIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 833,\n                  columnNumber: 32\n                }, this),\n                onClick: () => handleGeneraPdf(selectedCertificazione.id_certificazione),\n                sx: {\n                  mr: 1\n                },\n                children: \"Genera PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 831,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 830,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 778,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 774,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Chiudi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 844,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 843,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 772,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'eliminaCertificazione') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"sm\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Elimina Certificazione\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 851,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: !selectedCertificazione ? loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 855,\n            columnNumber: 17\n          }, this) : certificazioni.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Nessuna certificazione disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 857,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(List, {\n            children: certificazioni.map(cert => /*#__PURE__*/_jsxDEV(ListItem, {\n              button: true,\n              onClick: () => setSelectedCertificazione(cert),\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: `ID: ${cert.id_certificazione} - Cavo: ${cert.id_cavo}`,\n                secondary: `Data: ${new Date(cert.data_certificazione).toLocaleDateString()}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 866,\n                columnNumber: 23\n              }, this)\n            }, cert.id_certificazione, false, {\n              fileName: _jsxFileName,\n              lineNumber: 861,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 859,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"warning\",\n              sx: {\n                mb: 2\n              },\n              children: [\"Sei sicuro di voler eliminare la certificazione \", selectedCertificazione.id_certificazione, \"?\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 876,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: \"Questa operazione non pu\\xF2 essere annullata.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 879,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 875,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 852,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 886,\n            columnNumber: 13\n          }, this), selectedCertificazione && /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleEliminaCertificazione,\n            disabled: loading,\n            color: \"error\",\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 892,\n              columnNumber: 38\n            }, this) : /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 892,\n              columnNumber: 71\n            }, this),\n            children: \"Elimina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 888,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 885,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 850,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'gestioneStrumenti') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"lg\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Gestione Strumenti Certificati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 903,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mb: 2,\n              display: 'flex',\n              justifyContent: 'flex-end'\n            },\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 908,\n                columnNumber: 28\n              }, this),\n              onClick: handleCreaStrumento,\n              children: \"Nuovo Strumento\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 906,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 905,\n            columnNumber: 13\n          }, this), loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 916,\n            columnNumber: 15\n          }, this) : strumenti.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Nessuno strumento certificato trovato\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 918,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(TableContainer, {\n            component: Paper,\n            children: /*#__PURE__*/_jsxDEV(Table, {\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                children: /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Nome\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 924,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Marca\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 925,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Modello\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 926,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"N\\xB0 Serie\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 927,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Calibrazione\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 928,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Scadenza\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 929,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Stato\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 930,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Azioni\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 931,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 923,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 922,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                children: strumenti.map(strumento => {\n                  const scadenza = new Date(strumento.data_scadenza_calibrazione);\n                  const oggi = new Date();\n                  const giorni = Math.ceil((scadenza - oggi) / (1000 * 60 * 60 * 24));\n                  let statoColor = 'success';\n                  let statoLabel = 'Valido';\n                  if (giorni < 0) {\n                    statoColor = 'error';\n                    statoLabel = 'Scaduto';\n                  } else if (giorni <= 30) {\n                    statoColor = 'warning';\n                    statoLabel = 'In scadenza';\n                  }\n                  return /*#__PURE__*/_jsxDEV(TableRow, {\n                    children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                      children: strumento.nome\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 953,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: strumento.marca\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 954,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: strumento.modello\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 955,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: strumento.numero_serie\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 956,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: new Date(strumento.data_calibrazione).toLocaleDateString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 957,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: scadenza.toLocaleDateString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 958,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: /*#__PURE__*/_jsxDEV(Chip, {\n                        label: statoLabel,\n                        color: statoColor,\n                        size: \"small\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 960,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 959,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: [/*#__PURE__*/_jsxDEV(IconButton, {\n                        size: \"small\",\n                        onClick: () => handleStrumentoSelect(strumento),\n                        children: /*#__PURE__*/_jsxDEV(EditIcon, {\n                          fontSize: \"small\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 971,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 967,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                        size: \"small\",\n                        color: \"error\",\n                        onClick: () => handleEliminaStrumento(strumento),\n                        children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n                          fontSize: \"small\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 978,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 973,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 966,\n                      columnNumber: 27\n                    }, this)]\n                  }, strumento.id_strumento, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 952,\n                    columnNumber: 25\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 934,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 921,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 920,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 904,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Chiudi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 990,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 989,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 902,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'creaStrumento' || dialogType === 'modificaStrumento') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: dialogType === 'creaStrumento' ? 'Nuovo Strumento' : 'Modifica Strumento'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 997,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 2,\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  name: \"nome\",\n                  label: \"Nome Strumento\",\n                  fullWidth: true,\n                  variant: \"outlined\",\n                  value: strumentoFormData.nome,\n                  onChange: handleStrumentoFormChange,\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1004,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1003,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  name: \"marca\",\n                  label: \"Marca\",\n                  fullWidth: true,\n                  variant: \"outlined\",\n                  value: strumentoFormData.marca,\n                  onChange: handleStrumentoFormChange,\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1015,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1014,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  name: \"modello\",\n                  label: \"Modello\",\n                  fullWidth: true,\n                  variant: \"outlined\",\n                  value: strumentoFormData.modello,\n                  onChange: handleStrumentoFormChange,\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1026,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1025,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  name: \"numero_serie\",\n                  label: \"Numero di Serie\",\n                  fullWidth: true,\n                  variant: \"outlined\",\n                  value: strumentoFormData.numero_serie,\n                  onChange: handleStrumentoFormChange,\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1037,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1036,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  name: \"data_calibrazione\",\n                  label: \"Data Calibrazione\",\n                  type: \"date\",\n                  fullWidth: true,\n                  variant: \"outlined\",\n                  value: strumentoFormData.data_calibrazione,\n                  onChange: handleStrumentoFormChange,\n                  InputLabelProps: {\n                    shrink: true\n                  },\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1048,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1047,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  name: \"data_scadenza_calibrazione\",\n                  label: \"Data Scadenza Calibrazione\",\n                  type: \"date\",\n                  fullWidth: true,\n                  variant: \"outlined\",\n                  value: strumentoFormData.data_scadenza_calibrazione,\n                  onChange: handleStrumentoFormChange,\n                  InputLabelProps: {\n                    shrink: true\n                  },\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1061,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1060,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  name: \"certificato_calibrazione\",\n                  label: \"Percorso Certificato Calibrazione\",\n                  fullWidth: true,\n                  variant: \"outlined\",\n                  value: strumentoFormData.certificato_calibrazione,\n                  onChange: handleStrumentoFormChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1074,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1073,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  name: \"note\",\n                  label: \"Note\",\n                  fullWidth: true,\n                  multiline: true,\n                  rows: 3,\n                  variant: \"outlined\",\n                  value: strumentoFormData.note,\n                  onChange: handleStrumentoFormChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1084,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1083,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1002,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1001,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1000,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1099,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSalvaStrumento,\n            disabled: loading,\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1103,\n              columnNumber: 36\n            }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1103,\n              columnNumber: 69\n            }, this),\n            children: \"Salva\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1100,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1098,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 996,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [selectedOption === 'visualizzaCertificazioni' && !openDialog && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: [\"Certificazioni\", filtroCavo && ` - Filtro: ${filtroCavo}`]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1121,\n          columnNumber: 13\n        }, this), filtroCavo && /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(ClearIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1128,\n            columnNumber: 28\n          }, this),\n          onClick: () => {\n            setFiltroCavo('');\n            loadCertificazioni('');\n          },\n          children: \"Rimuovi Filtro\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1126,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1120,\n        columnNumber: 11\n      }, this), loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1140,\n        columnNumber: 13\n      }, this) : renderCertificazioniTable()]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1119,\n      columnNumber: 9\n    }, this), !selectedOption && /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        minHeight: '300px',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        children: \"Seleziona un'opzione dal menu principale per iniziare.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1149,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1148,\n      columnNumber: 9\n    }, this), renderDialog()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1116,\n    columnNumber: 5\n  }, this);\n}, \"3t1Imx3ADlwU5jDofxFT2MaclE4=\")), \"3t1Imx3ADlwU5jDofxFT2MaclE4=\");\n_c2 = CertificazioneCavi;\nexport default CertificazioneCavi;\nvar _c, _c2;\n$RefreshReg$(_c, \"CertificazioneCavi$forwardRef\");\n$RefreshReg$(_c2, \"CertificazioneCavi\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "forwardRef", "useImperativeHandle", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "List", "ListItem", "ListItemText", "ListItemIcon", "ListItemButton", "<PERSON><PERSON>", "CircularProgress", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Divider", "IconButton", "Chip", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "PictureAsPdf", "PdfIcon", "Search", "SearchIcon", "FilterList", "FilterIcon", "Save", "SaveIcon", "Clear", "ClearIcon", "ViewList", "ViewListIcon", "Build", "BuildIcon", "certificazioneService", "caviService", "jsxDEV", "_jsxDEV", "CertificazioneCavi", "_s", "_c", "cantiereId", "onSuccess", "onError", "dialogOnly", "ref", "loading", "setLoading", "certificazioni", "setCertificazioni", "cavi", "<PERSON><PERSON><PERSON>", "strumenti", "setStrumenti", "selectedOption", "setSelectedOption", "openDialog", "setOpenDialog", "dialogType", "setDialogType", "selectedCertificazione", "setSelectedCertificazione", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "filtroCavo", "setFiltroCavo", "selectedStrumento", "setSelectedStrumento", "strumentoFormData", "setStrumentoFormData", "nome", "marca", "modello", "numero_serie", "data_calibrazione", "data_scadenza_calibrazione", "certificato_calibrazione", "note", "formData", "setFormData", "id_cavo", "id_strumento", "<PERSON><PERSON><PERSON>_misurata", "valore_continuita", "valore_isolamento", "valore_resistenza", "loadCertificazioni", "data", "getCertificazioni", "error", "console", "loadCavi", "get<PERSON><PERSON>", "loadStrumenti", "getStrumenti", "handleOptionSelect", "createCertificationForCavo", "cavo", "handleCavoSelect", "viewCertificationForCavo", "option", "handleCloseDialog", "handleCertificazioneSelect", "certificazione", "handleGeneraPdf", "id_certificazione", "metratura_reale", "handleFormChange", "e", "name", "value", "target", "handleFiltroCavo", "collegaCavoAutomatico", "cavoId", "responsabile", "default", "partenzaCollegata", "arrivoCollegato", "collegaCavo", "log", "detail", "includes", "handleCreaCertificazione", "find", "c", "isCollegato", "colle<PERSON>nti", "statoCollegamenti", "conferma", "window", "confirm", "message", "createCertificazione", "handleEliminaCertificazione", "deleteCertificazione", "idCertificazione", "response", "generatePdf", "open", "file_url", "handleStrumentoSelect", "strumento", "handleStrumentoFormChange", "handleCreaStrumento", "handleSalvaStrumento", "updateStrumento", "createStrumento", "handleEliminaStrumento", "deleteStrumento", "renderCertificazioniTable", "length", "severity", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "component", "size", "map", "cert", "Date", "data_certificazione", "toLocaleDateString", "operatore", "onClick", "fontSize", "color", "renderDialog", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "sx", "mt", "label", "variant", "onChange", "placeholder", "startIcon", "disabled", "button", "primary", "secondary", "tipologia", "ubicazione_partenza", "ubicazione_arrivo", "gutterBottom", "my", "container", "spacing", "item", "xs", "sm", "required", "type", "multiline", "rows", "display", "justifyContent", "mr", "mb", "scadenza", "oggi", "<PERSON>ior<PERSON>", "Math", "ceil", "statoColor", "statoLabel", "InputLabelProps", "shrink", "alignItems", "p", "minHeight", "_c2", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/cavi/CertificazioneCavi.js"], "sourcesContent": ["import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Grid,\n  Card,\n  CardContent,\n  CardActions,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemIcon,\n  ListItemButton,\n  Alert,\n  CircularProgress,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Divider,\n  IconButton,\n  Chip\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  PictureAsPdf as PdfIcon,\n  Search as SearchIcon,\n  FilterList as FilterIcon,\n  Save as SaveIcon,\n  Clear as ClearIcon,\n  ViewList as ViewListIcon,\n  Build as BuildIcon\n} from '@mui/icons-material';\nimport certificazioneService from '../../services/certificazioneService';\nimport caviService from '../../services/caviService';\n\nconst CertificazioneCavi = forwardRef(({ cantiereId, onSuccess, onError, dialogOnly = false }, ref) => {\n  const [loading, setLoading] = useState(false);\n  const [certificazioni, setCertificazioni] = useState([]);\n  const [cavi, setCavi] = useState([]);\n  const [strumenti, setStrumenti] = useState([]);\n  const [selectedOption, setSelectedOption] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedCertificazione, setSelectedCertificazione] = useState(null);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [filtroCavo, setFiltroCavo] = useState('');\n  const [selectedStrumento, setSelectedStrumento] = useState(null);\n  const [strumentoFormData, setStrumentoFormData] = useState({\n    nome: '',\n    marca: '',\n    modello: '',\n    numero_serie: '',\n    data_calibrazione: '',\n    data_scadenza_calibrazione: '',\n    certificato_calibrazione: '',\n    note: ''\n  });\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    id_strumento: '',\n    lunghezza_misurata: '',\n    valore_continuita: 'OK',\n    valore_isolamento: '',\n    valore_resistenza: 'OK',\n    note: ''\n  });\n\n  // Carica le certificazioni\n  const loadCertificazioni = async (filtroCavo = '') => {\n    try {\n      setLoading(true);\n      const data = await certificazioneService.getCertificazioni(cantiereId, filtroCavo);\n      setCertificazioni(data);\n    } catch (error) {\n      onError('Errore nel caricamento delle certificazioni');\n      console.error('Errore nel caricamento delle certificazioni:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica i cavi disponibili\n  const loadCavi = async () => {\n    try {\n      setLoading(true);\n      const data = await caviService.getCavi(cantiereId);\n      setCavi(data);\n    } catch (error) {\n      onError('Errore nel caricamento dei cavi');\n      console.error('Errore nel caricamento dei cavi:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica gli strumenti certificati\n  const loadStrumenti = async () => {\n    try {\n      setLoading(true);\n      const data = await certificazioneService.getStrumenti(cantiereId);\n      setStrumenti(data);\n    } catch (error) {\n      onError('Errore nel caricamento degli strumenti');\n      console.error('Errore nel caricamento degli strumenti:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Espone i metodi tramite ref\n  useImperativeHandle(ref, () => ({\n    handleOptionSelect,\n    createCertificationForCavo: (cavo) => {\n      // Metodo per aprire direttamente la certificazione con un cavo preselezionato\n      loadStrumenti();\n      handleCavoSelect(cavo);\n      setOpenDialog(true);\n    },\n    viewCertificationForCavo: (cavo) => {\n      // Metodo per visualizzare la certificazione di un cavo specifico\n      loadCertificazioni(cavo.id_cavo);\n      setSelectedOption('visualizzaCertificazioni');\n    }\n  }));\n\n  // Carica i dati all'avvio del componente\n  useEffect(() => {\n    loadCertificazioni();\n  }, [cantiereId]);\n\n  // Gestisce la selezione di un'opzione dal menu\n  const handleOptionSelect = (option) => {\n    setSelectedOption(option);\n\n    if (option === 'visualizzaCertificazioni') {\n      loadCertificazioni();\n    } else if (option === 'filtraCertificazioni') {\n      setDialogType('filtraCertificazioni');\n      setOpenDialog(true);\n    } else if (option === 'creaCertificazione') {\n      loadCavi();\n      loadStrumenti();\n      setDialogType('selezionaCavo');\n      setOpenDialog(true);\n    } else if (option === 'dettagliCertificazione') {\n      loadCertificazioni();\n      setDialogType('selezionaCertificazione');\n      setOpenDialog(true);\n    } else if (option === 'generaPdf') {\n      loadCertificazioni();\n      setDialogType('selezionaCertificazionePdf');\n      setOpenDialog(true);\n    } else if (option === 'eliminaCertificazione') {\n      loadCertificazioni();\n      setDialogType('eliminaCertificazione');\n      setOpenDialog(true);\n    } else if (option === 'gestioneStrumenti') {\n      loadStrumenti();\n      setDialogType('gestioneStrumenti');\n      setOpenDialog(true);\n    }\n  };\n\n  // Gestisce la chiusura del dialog\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setSelectedCertificazione(null);\n    setSelectedCavo(null);\n    setSelectedStrumento(null);\n    setFormData({\n      id_cavo: '',\n      id_strumento: '',\n      lunghezza_misurata: '',\n      valore_continuita: 'OK',\n      valore_isolamento: '',\n      valore_resistenza: 'OK',\n      note: ''\n    });\n    setStrumentoFormData({\n      nome: '',\n      marca: '',\n      modello: '',\n      numero_serie: '',\n      data_calibrazione: '',\n      data_scadenza_calibrazione: '',\n      certificato_calibrazione: '',\n      note: ''\n    });\n  };\n\n  // Gestisce la selezione di una certificazione\n  const handleCertificazioneSelect = (certificazione) => {\n    setSelectedCertificazione(certificazione);\n\n    if (dialogType === 'selezionaCertificazione') {\n      setDialogType('dettagliCertificazione');\n    } else if (dialogType === 'selezionaCertificazionePdf') {\n      handleGeneraPdf(certificazione.id_certificazione);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = (cavo) => {\n    setSelectedCavo(cavo);\n    setFormData({\n      ...formData,\n      id_cavo: cavo.id_cavo,\n      lunghezza_misurata: cavo.metratura_reale || '0'\n    });\n    setDialogType('creaCertificazione');\n  };\n\n  // Gestisce il cambio dei valori nel form\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n  };\n\n  // Gestisce il filtro per cavo\n  const handleFiltroCavo = () => {\n    loadCertificazioni(filtroCavo);\n    handleCloseDialog();\n  };\n\n  // Funzione per collegare automaticamente un cavo\n  const collegaCavoAutomatico = async (cavoId, responsabile = 'cantiere') => {\n    try {\n      // Importa il servizio cavi\n      const caviService = (await import('../../services/caviService')).default;\n\n      let partenzaCollegata = false;\n      let arrivoCollegato = false;\n\n      // Prova a collegare il lato partenza\n      try {\n        await caviService.collegaCavo(cantiereId, cavoId, 'partenza', responsabile);\n        partenzaCollegata = true;\n        console.log('Lato partenza collegato con successo');\n      } catch (error) {\n        if (error.detail && error.detail.includes('già collegato')) {\n          partenzaCollegata = true;\n          console.log('Lato partenza già collegato');\n        } else {\n          console.error('Errore nel collegamento lato partenza:', error);\n          throw error;\n        }\n      }\n\n      // Prova a collegare il lato arrivo\n      try {\n        await caviService.collegaCavo(cantiereId, cavoId, 'arrivo', responsabile);\n        arrivoCollegato = true;\n        console.log('Lato arrivo collegato con successo');\n      } catch (error) {\n        if (error.detail && error.detail.includes('già collegato')) {\n          arrivoCollegato = true;\n          console.log('Lato arrivo già collegato');\n        } else {\n          console.error('Errore nel collegamento lato arrivo:', error);\n          throw error;\n        }\n      }\n\n      return partenzaCollegata && arrivoCollegato;\n    } catch (error) {\n      console.error('Errore nel collegamento automatico:', error);\n      throw error;\n    }\n  };\n\n  // Gestisce la creazione di una certificazione\n  const handleCreaCertificazione = async () => {\n    try {\n      if (!formData.id_cavo || !formData.id_strumento || !formData.valore_isolamento) {\n        onError('Compila tutti i campi obbligatori');\n        return;\n      }\n\n      // Verifica se il cavo è completamente collegato\n      const cavo = cavi.find(c => c.id_cavo === formData.id_cavo);\n      if (cavo) {\n        const isCollegato = cavo.collegamenti === 3;\n\n        if (!isCollegato) {\n          const statoCollegamenti = cavo.collegamenti === 0 ? 'Non collegato' :\n                                   cavo.collegamenti === 1 ? 'Solo partenza collegata' :\n                                   cavo.collegamenti === 2 ? 'Solo arrivo collegato' :\n                                   'Stato sconosciuto';\n\n          const conferma = window.confirm(\n            `ATTENZIONE: Il cavo ${cavo.id_cavo} non risulta completamente collegato.\\n\\n` +\n            `Stato collegamenti: ${statoCollegamenti}\\n\\n` +\n            `Vuoi collegare automaticamente entrambi i lati del cavo e procedere con la certificazione?\\n\\n` +\n            `(Il sistema collegherà automaticamente il cavo a \"cantiere\" su entrambi i lati)`\n          );\n\n          if (!conferma) {\n            return;\n          }\n\n          // Collega automaticamente il cavo\n          try {\n            setLoading(true);\n            await collegaCavoAutomatico(cavo.id_cavo, 'cantiere');\n            onSuccess('Cavo collegato automaticamente su entrambi i lati');\n\n            // Ricarica i cavi per aggiornare lo stato\n            await loadCavi();\n          } catch (error) {\n            onError('Errore nel collegamento automatico: ' + (error.message || 'Errore sconosciuto'));\n            return;\n          }\n        }\n      }\n\n      setLoading(true);\n      await certificazioneService.createCertificazione(cantiereId, formData);\n      onSuccess('Certificazione creata con successo');\n      handleCloseDialog();\n      loadCertificazioni();\n    } catch (error) {\n      onError('Errore nella creazione della certificazione: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nella creazione della certificazione:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce l'eliminazione di una certificazione\n  const handleEliminaCertificazione = async () => {\n    try {\n      if (!selectedCertificazione) {\n        onError('Seleziona una certificazione da eliminare');\n        return;\n      }\n\n      setLoading(true);\n      await certificazioneService.deleteCertificazione(cantiereId, selectedCertificazione.id_certificazione);\n      onSuccess('Certificazione eliminata con successo');\n      handleCloseDialog();\n      loadCertificazioni();\n    } catch (error) {\n      onError('Errore nell\\'eliminazione della certificazione: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nell\\'eliminazione della certificazione:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la generazione del PDF\n  const handleGeneraPdf = async (idCertificazione) => {\n    try {\n      setLoading(true);\n      const response = await certificazioneService.generatePdf(cantiereId, idCertificazione);\n\n      // Apri il PDF in una nuova finestra\n      window.open(response.file_url, '_blank');\n\n      onSuccess('PDF generato con successo');\n      handleCloseDialog();\n    } catch (error) {\n      onError('Errore nella generazione del PDF: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nella generazione del PDF:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di uno strumento\n  const handleStrumentoSelect = (strumento) => {\n    setSelectedStrumento(strumento);\n    setStrumentoFormData({\n      nome: strumento.nome || '',\n      marca: strumento.marca || '',\n      modello: strumento.modello || '',\n      numero_serie: strumento.numero_serie || '',\n      data_calibrazione: strumento.data_calibrazione || '',\n      data_scadenza_calibrazione: strumento.data_scadenza_calibrazione || '',\n      certificato_calibrazione: strumento.certificato_calibrazione || '',\n      note: strumento.note || ''\n    });\n    setDialogType('modificaStrumento');\n  };\n\n  // Gestisce il cambio dei valori nel form strumento\n  const handleStrumentoFormChange = (e) => {\n    const { name, value } = e.target;\n    setStrumentoFormData({\n      ...strumentoFormData,\n      [name]: value\n    });\n  };\n\n  // Gestisce la creazione di un nuovo strumento\n  const handleCreaStrumento = () => {\n    setSelectedStrumento(null);\n    setStrumentoFormData({\n      nome: '',\n      marca: '',\n      modello: '',\n      numero_serie: '',\n      data_calibrazione: '',\n      data_scadenza_calibrazione: '',\n      certificato_calibrazione: '',\n      note: ''\n    });\n    setDialogType('creaStrumento');\n  };\n\n  // Gestisce il salvataggio dello strumento\n  const handleSalvaStrumento = async () => {\n    try {\n      if (!strumentoFormData.nome || !strumentoFormData.marca || !strumentoFormData.modello || !strumentoFormData.numero_serie) {\n        onError('Compila tutti i campi obbligatori');\n        return;\n      }\n\n      if (!strumentoFormData.data_calibrazione || !strumentoFormData.data_scadenza_calibrazione) {\n        onError('Le date di calibrazione e scadenza sono obbligatorie');\n        return;\n      }\n\n      setLoading(true);\n\n      if (selectedStrumento) {\n        await certificazioneService.updateStrumento(cantiereId, selectedStrumento.id_strumento, strumentoFormData);\n        onSuccess('Strumento aggiornato con successo');\n      } else {\n        await certificazioneService.createStrumento(cantiereId, strumentoFormData);\n        onSuccess('Strumento creato con successo');\n      }\n\n      handleCloseDialog();\n      loadStrumenti();\n    } catch (error) {\n      onError('Errore nel salvataggio dello strumento: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nel salvataggio dello strumento:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce l'eliminazione di uno strumento\n  const handleEliminaStrumento = async (strumento) => {\n    try {\n      if (window.confirm(`Sei sicuro di voler eliminare lo strumento \"${strumento.nome} ${strumento.marca} ${strumento.modello}\"?`)) {\n        setLoading(true);\n        await certificazioneService.deleteStrumento(cantiereId, strumento.id_strumento);\n        onSuccess('Strumento eliminato con successo');\n        loadStrumenti();\n      }\n    } catch (error) {\n      onError('Errore nell\\'eliminazione dello strumento: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nell\\'eliminazione dello strumento:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza le certificazioni in formato tabella\n  const renderCertificazioniTable = () => {\n    if (certificazioni.length === 0) {\n      return (\n        <Alert severity=\"info\">Nessuna certificazione trovata</Alert>\n      );\n    }\n\n    return (\n      <TableContainer component={Paper}>\n        <Table size=\"small\">\n          <TableHead>\n            <TableRow>\n              <TableCell>ID</TableCell>\n              <TableCell>Cavo</TableCell>\n              <TableCell>Data</TableCell>\n              <TableCell>Operatore</TableCell>\n              <TableCell>Strumento</TableCell>\n              <TableCell>Lunghezza</TableCell>\n              <TableCell>Isolamento</TableCell>\n              <TableCell>Azioni</TableCell>\n            </TableRow>\n          </TableHead>\n          <TableBody>\n            {certificazioni.map((cert) => (\n              <TableRow key={cert.id_certificazione}>\n                <TableCell>{cert.id_certificazione}</TableCell>\n                <TableCell>{cert.id_cavo}</TableCell>\n                <TableCell>{new Date(cert.data_certificazione).toLocaleDateString()}</TableCell>\n                <TableCell>{cert.operatore}</TableCell>\n                <TableCell>{cert.strumento}</TableCell>\n                <TableCell>{cert.lunghezza_misurata} m</TableCell>\n                <TableCell>{cert.valore_isolamento} MΩ</TableCell>\n                <TableCell>\n                  <IconButton\n                    size=\"small\"\n                    onClick={() => {\n                      setSelectedCertificazione(cert);\n                      setDialogType('dettagliCertificazione');\n                      setOpenDialog(true);\n                    }}\n                  >\n                    <SearchIcon fontSize=\"small\" />\n                  </IconButton>\n                  <IconButton\n                    size=\"small\"\n                    onClick={() => handleGeneraPdf(cert.id_certificazione)}\n                  >\n                    <PdfIcon fontSize=\"small\" />\n                  </IconButton>\n                  <IconButton\n                    size=\"small\"\n                    color=\"error\"\n                    onClick={() => {\n                      setSelectedCertificazione(cert);\n                      setDialogType('eliminaCertificazione');\n                      setOpenDialog(true);\n                    }}\n                  >\n                    <DeleteIcon fontSize=\"small\" />\n                  </IconButton>\n                </TableCell>\n              </TableRow>\n            ))}\n          </TableBody>\n        </Table>\n      </TableContainer>\n    );\n  };\n\n  // Renderizza il dialog in base al tipo\n  const renderDialog = () => {\n    if (dialogType === 'filtraCertificazioni') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n          <DialogTitle>Filtra Certificazioni per Cavo</DialogTitle>\n          <DialogContent>\n            <Box sx={{ mt: 2 }}>\n              <TextField\n                fullWidth\n                label=\"ID Cavo\"\n                variant=\"outlined\"\n                value={filtroCavo}\n                onChange={(e) => setFiltroCavo(e.target.value)}\n                placeholder=\"Inserisci l'ID del cavo\"\n              />\n            </Box>\n          </DialogContent>\n          <DialogActions>\n            <Button\n              onClick={() => {\n                setFiltroCavo('');\n                loadCertificazioni('');\n                handleCloseDialog();\n              }}\n              startIcon={<ClearIcon />}\n            >\n              Rimuovi Filtro\n            </Button>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            <Button\n              onClick={handleFiltroCavo}\n              disabled={loading}\n              startIcon={loading ? <CircularProgress size={20} /> : <FilterIcon />}\n            >\n              Filtra\n            </Button>\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'selezionaCavo') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>Seleziona Cavo per Certificazione</DialogTitle>\n          <DialogContent>\n            {loading ? (\n              <CircularProgress />\n            ) : cavi.length === 0 ? (\n              <Alert severity=\"info\">Nessun cavo disponibile</Alert>\n            ) : (\n              <List>\n                {cavi.map((cavo) => (\n                  <ListItem\n                    button\n                    key={cavo.id_cavo}\n                    onClick={() => handleCavoSelect(cavo)}\n                  >\n                    <ListItemText\n                      primary={cavo.id_cavo}\n                      secondary={`${cavo.tipologia || 'N/A'} - Da: ${cavo.ubicazione_partenza || 'N/A'} A: ${cavo.ubicazione_arrivo || 'N/A'}`}\n                    />\n                  </ListItem>\n                ))}\n              </List>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'creaCertificazione') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>Crea Nuova Certificazione</DialogTitle>\n          <DialogContent>\n            <Box sx={{ mt: 2 }}>\n              <Typography variant=\"subtitle1\" gutterBottom>\n                Cavo selezionato: {selectedCavo?.id_cavo}\n              </Typography>\n              <Typography variant=\"body2\" gutterBottom>\n                Tipologia: {selectedCavo?.tipologia || 'N/A'}\n              </Typography>\n              <Typography variant=\"body2\" gutterBottom>\n                Metratura: {selectedCavo?.metratura_reale || '0'} m\n              </Typography>\n\n              <Divider sx={{ my: 2 }} />\n\n              <Grid container spacing={2}>\n                <Grid item xs={12} sm={6}>\n                  <FormControl fullWidth variant=\"outlined\">\n                    <InputLabel>Strumento Utilizzato</InputLabel>\n                    <Select\n                      name=\"id_strumento\"\n                      value={formData.id_strumento}\n                      onChange={handleFormChange}\n                      label=\"Strumento Utilizzato\"\n                      required\n                    >\n                      {strumenti.map((strumento) => (\n                        <MenuItem key={strumento.id_strumento} value={strumento.id_strumento}>\n                          {strumento.nome} - {strumento.modello}\n                        </MenuItem>\n                      ))}\n                    </Select>\n                  </FormControl>\n                </Grid>\n                <Grid item xs={12} sm={6}>\n                  <TextField\n                    name=\"lunghezza_misurata\"\n                    label=\"Lunghezza Misurata (m)\"\n                    type=\"number\"\n                    fullWidth\n                    variant=\"outlined\"\n                    value={formData.lunghezza_misurata}\n                    onChange={handleFormChange}\n                    required\n                  />\n                </Grid>\n                <Grid item xs={12} sm={6}>\n                  <FormControl fullWidth variant=\"outlined\">\n                    <InputLabel>Test Continuità</InputLabel>\n                    <Select\n                      name=\"valore_continuita\"\n                      value={formData.valore_continuita}\n                      onChange={handleFormChange}\n                      label=\"Test Continuità\"\n                    >\n                      <MenuItem value=\"OK\">OK</MenuItem>\n                      <MenuItem value=\"NON OK\">NON OK</MenuItem>\n                    </Select>\n                  </FormControl>\n                </Grid>\n                <Grid item xs={12} sm={6}>\n                  <TextField\n                    name=\"valore_isolamento\"\n                    label=\"Valore Isolamento (MΩ)\"\n                    fullWidth\n                    variant=\"outlined\"\n                    value={formData.valore_isolamento}\n                    onChange={handleFormChange}\n                    required\n                  />\n                </Grid>\n                <Grid item xs={12} sm={6}>\n                  <FormControl fullWidth variant=\"outlined\">\n                    <InputLabel>Test Resistenza</InputLabel>\n                    <Select\n                      name=\"valore_resistenza\"\n                      value={formData.valore_resistenza}\n                      onChange={handleFormChange}\n                      label=\"Test Resistenza\"\n                    >\n                      <MenuItem value=\"OK\">OK</MenuItem>\n                      <MenuItem value=\"NON OK\">NON OK</MenuItem>\n                    </Select>\n                  </FormControl>\n                </Grid>\n                <Grid item xs={12}>\n                  <TextField\n                    name=\"note\"\n                    label=\"Note\"\n                    fullWidth\n                    multiline\n                    rows={3}\n                    variant=\"outlined\"\n                    value={formData.note}\n                    onChange={handleFormChange}\n                  />\n                </Grid>\n              </Grid>\n            </Box>\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            <Button\n              onClick={handleCreaCertificazione}\n              disabled={loading || !formData.id_strumento || !formData.valore_isolamento}\n              startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}\n            >\n              Salva\n            </Button>\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'selezionaCertificazione' || dialogType === 'selezionaCertificazionePdf') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>\n            {dialogType === 'selezionaCertificazione'\n              ? 'Seleziona Certificazione da Visualizzare'\n              : 'Seleziona Certificazione per PDF'}\n          </DialogTitle>\n          <DialogContent>\n            {loading ? (\n              <CircularProgress />\n            ) : certificazioni.length === 0 ? (\n              <Alert severity=\"info\">Nessuna certificazione trovata</Alert>\n            ) : (\n              <List>\n                {certificazioni.map((cert) => (\n                  <ListItem\n                    button\n                    key={cert.id_certificazione}\n                    onClick={() => handleCertificazioneSelect(cert)}\n                  >\n                    <ListItemText\n                      primary={`ID: ${cert.id_certificazione} - Cavo: ${cert.id_cavo}`}\n                      secondary={`Data: ${new Date(cert.data_certificazione).toLocaleDateString()} - Operatore: ${cert.operatore}`}\n                    />\n                  </ListItem>\n                ))}\n              </List>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'dettagliCertificazione') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>Dettagli Certificazione</DialogTitle>\n          <DialogContent>\n            {!selectedCertificazione ? (\n              <CircularProgress />\n            ) : (\n              <Box sx={{ mt: 2 }}>\n                <Grid container spacing={2}>\n                  <Grid item xs={12} sm={6}>\n                    <Typography variant=\"subtitle2\">ID Certificazione:</Typography>\n                    <Typography variant=\"body1\" gutterBottom>{selectedCertificazione.id_certificazione}</Typography>\n                  </Grid>\n                  <Grid item xs={12} sm={6}>\n                    <Typography variant=\"subtitle2\">Cavo:</Typography>\n                    <Typography variant=\"body1\" gutterBottom>{selectedCertificazione.id_cavo}</Typography>\n                  </Grid>\n                  <Grid item xs={12} sm={6}>\n                    <Typography variant=\"subtitle2\">Data Certificazione:</Typography>\n                    <Typography variant=\"body1\" gutterBottom>\n                      {new Date(selectedCertificazione.data_certificazione).toLocaleDateString()}\n                    </Typography>\n                  </Grid>\n                  <Grid item xs={12} sm={6}>\n                    <Typography variant=\"subtitle2\">Operatore:</Typography>\n                    <Typography variant=\"body1\" gutterBottom>{selectedCertificazione.operatore}</Typography>\n                  </Grid>\n                  <Grid item xs={12} sm={6}>\n                    <Typography variant=\"subtitle2\">Strumento:</Typography>\n                    <Typography variant=\"body1\" gutterBottom>{selectedCertificazione.strumento}</Typography>\n                  </Grid>\n                  <Grid item xs={12} sm={6}>\n                    <Typography variant=\"subtitle2\">ID Strumento:</Typography>\n                    <Typography variant=\"body1\" gutterBottom>{selectedCertificazione.id_strumento}</Typography>\n                  </Grid>\n                  <Grid item xs={12} sm={6}>\n                    <Typography variant=\"subtitle2\">Lunghezza Misurata:</Typography>\n                    <Typography variant=\"body1\" gutterBottom>{selectedCertificazione.lunghezza_misurata} m</Typography>\n                  </Grid>\n                  <Grid item xs={12} sm={6}>\n                    <Typography variant=\"subtitle2\">Test Continuità:</Typography>\n                    <Typography variant=\"body1\" gutterBottom>{selectedCertificazione.valore_continuita}</Typography>\n                  </Grid>\n                  <Grid item xs={12} sm={6}>\n                    <Typography variant=\"subtitle2\">Valore Isolamento:</Typography>\n                    <Typography variant=\"body1\" gutterBottom>{selectedCertificazione.valore_isolamento} MΩ</Typography>\n                  </Grid>\n                  <Grid item xs={12} sm={6}>\n                    <Typography variant=\"subtitle2\">Test Resistenza:</Typography>\n                    <Typography variant=\"body1\" gutterBottom>{selectedCertificazione.valore_resistenza}</Typography>\n                  </Grid>\n                  {selectedCertificazione.note && (\n                    <Grid item xs={12}>\n                      <Typography variant=\"subtitle2\">Note:</Typography>\n                      <Typography variant=\"body1\" gutterBottom>{selectedCertificazione.note}</Typography>\n                    </Grid>\n                  )}\n                </Grid>\n\n                <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>\n                  <Button\n                    variant=\"contained\"\n                    startIcon={<PdfIcon />}\n                    onClick={() => handleGeneraPdf(selectedCertificazione.id_certificazione)}\n                    sx={{ mr: 1 }}\n                  >\n                    Genera PDF\n                  </Button>\n                </Box>\n              </Box>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Chiudi</Button>\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'eliminaCertificazione') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n          <DialogTitle>Elimina Certificazione</DialogTitle>\n          <DialogContent>\n            {!selectedCertificazione ? (\n              loading ? (\n                <CircularProgress />\n              ) : certificazioni.length === 0 ? (\n                <Alert severity=\"info\">Nessuna certificazione disponibile</Alert>\n              ) : (\n                <List>\n                  {certificazioni.map((cert) => (\n                    <ListItem\n                      button\n                      key={cert.id_certificazione}\n                      onClick={() => setSelectedCertificazione(cert)}\n                    >\n                      <ListItemText\n                        primary={`ID: ${cert.id_certificazione} - Cavo: ${cert.id_cavo}`}\n                        secondary={`Data: ${new Date(cert.data_certificazione).toLocaleDateString()}`}\n                      />\n                    </ListItem>\n                  ))}\n                </List>\n              )\n            ) : (\n              <Box>\n                <Alert severity=\"warning\" sx={{ mb: 2 }}>\n                  Sei sicuro di voler eliminare la certificazione {selectedCertificazione.id_certificazione}?\n                </Alert>\n                <Typography variant=\"body1\">\n                  Questa operazione non può essere annullata.\n                </Typography>\n              </Box>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            {selectedCertificazione && (\n              <Button\n                onClick={handleEliminaCertificazione}\n                disabled={loading}\n                color=\"error\"\n                startIcon={loading ? <CircularProgress size={20} /> : <DeleteIcon />}\n              >\n                Elimina\n              </Button>\n            )}\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'gestioneStrumenti') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"lg\" fullWidth>\n          <DialogTitle>Gestione Strumenti Certificati</DialogTitle>\n          <DialogContent>\n            <Box sx={{ mb: 2, display: 'flex', justifyContent: 'flex-end' }}>\n              <Button\n                variant=\"contained\"\n                startIcon={<AddIcon />}\n                onClick={handleCreaStrumento}\n              >\n                Nuovo Strumento\n              </Button>\n            </Box>\n\n            {loading ? (\n              <CircularProgress />\n            ) : strumenti.length === 0 ? (\n              <Alert severity=\"info\">Nessuno strumento certificato trovato</Alert>\n            ) : (\n              <TableContainer component={Paper}>\n                <Table size=\"small\">\n                  <TableHead>\n                    <TableRow>\n                      <TableCell>Nome</TableCell>\n                      <TableCell>Marca</TableCell>\n                      <TableCell>Modello</TableCell>\n                      <TableCell>N° Serie</TableCell>\n                      <TableCell>Calibrazione</TableCell>\n                      <TableCell>Scadenza</TableCell>\n                      <TableCell>Stato</TableCell>\n                      <TableCell>Azioni</TableCell>\n                    </TableRow>\n                  </TableHead>\n                  <TableBody>\n                    {strumenti.map((strumento) => {\n                      const scadenza = new Date(strumento.data_scadenza_calibrazione);\n                      const oggi = new Date();\n                      const giorni = Math.ceil((scadenza - oggi) / (1000 * 60 * 60 * 24));\n\n                      let statoColor = 'success';\n                      let statoLabel = 'Valido';\n\n                      if (giorni < 0) {\n                        statoColor = 'error';\n                        statoLabel = 'Scaduto';\n                      } else if (giorni <= 30) {\n                        statoColor = 'warning';\n                        statoLabel = 'In scadenza';\n                      }\n\n                      return (\n                        <TableRow key={strumento.id_strumento}>\n                          <TableCell>{strumento.nome}</TableCell>\n                          <TableCell>{strumento.marca}</TableCell>\n                          <TableCell>{strumento.modello}</TableCell>\n                          <TableCell>{strumento.numero_serie}</TableCell>\n                          <TableCell>{new Date(strumento.data_calibrazione).toLocaleDateString()}</TableCell>\n                          <TableCell>{scadenza.toLocaleDateString()}</TableCell>\n                          <TableCell>\n                            <Chip\n                              label={statoLabel}\n                              color={statoColor}\n                              size=\"small\"\n                            />\n                          </TableCell>\n                          <TableCell>\n                            <IconButton\n                              size=\"small\"\n                              onClick={() => handleStrumentoSelect(strumento)}\n                            >\n                              <EditIcon fontSize=\"small\" />\n                            </IconButton>\n                            <IconButton\n                              size=\"small\"\n                              color=\"error\"\n                              onClick={() => handleEliminaStrumento(strumento)}\n                            >\n                              <DeleteIcon fontSize=\"small\" />\n                            </IconButton>\n                          </TableCell>\n                        </TableRow>\n                      );\n                    })}\n                  </TableBody>\n                </Table>\n              </TableContainer>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Chiudi</Button>\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'creaStrumento' || dialogType === 'modificaStrumento') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>\n            {dialogType === 'creaStrumento' ? 'Nuovo Strumento' : 'Modifica Strumento'}\n          </DialogTitle>\n          <DialogContent>\n            <Box sx={{ mt: 2 }}>\n              <Grid container spacing={2}>\n                <Grid item xs={12} sm={6}>\n                  <TextField\n                    name=\"nome\"\n                    label=\"Nome Strumento\"\n                    fullWidth\n                    variant=\"outlined\"\n                    value={strumentoFormData.nome}\n                    onChange={handleStrumentoFormChange}\n                    required\n                  />\n                </Grid>\n                <Grid item xs={12} sm={6}>\n                  <TextField\n                    name=\"marca\"\n                    label=\"Marca\"\n                    fullWidth\n                    variant=\"outlined\"\n                    value={strumentoFormData.marca}\n                    onChange={handleStrumentoFormChange}\n                    required\n                  />\n                </Grid>\n                <Grid item xs={12} sm={6}>\n                  <TextField\n                    name=\"modello\"\n                    label=\"Modello\"\n                    fullWidth\n                    variant=\"outlined\"\n                    value={strumentoFormData.modello}\n                    onChange={handleStrumentoFormChange}\n                    required\n                  />\n                </Grid>\n                <Grid item xs={12} sm={6}>\n                  <TextField\n                    name=\"numero_serie\"\n                    label=\"Numero di Serie\"\n                    fullWidth\n                    variant=\"outlined\"\n                    value={strumentoFormData.numero_serie}\n                    onChange={handleStrumentoFormChange}\n                    required\n                  />\n                </Grid>\n                <Grid item xs={12} sm={6}>\n                  <TextField\n                    name=\"data_calibrazione\"\n                    label=\"Data Calibrazione\"\n                    type=\"date\"\n                    fullWidth\n                    variant=\"outlined\"\n                    value={strumentoFormData.data_calibrazione}\n                    onChange={handleStrumentoFormChange}\n                    InputLabelProps={{ shrink: true }}\n                    required\n                  />\n                </Grid>\n                <Grid item xs={12} sm={6}>\n                  <TextField\n                    name=\"data_scadenza_calibrazione\"\n                    label=\"Data Scadenza Calibrazione\"\n                    type=\"date\"\n                    fullWidth\n                    variant=\"outlined\"\n                    value={strumentoFormData.data_scadenza_calibrazione}\n                    onChange={handleStrumentoFormChange}\n                    InputLabelProps={{ shrink: true }}\n                    required\n                  />\n                </Grid>\n                <Grid item xs={12}>\n                  <TextField\n                    name=\"certificato_calibrazione\"\n                    label=\"Percorso Certificato Calibrazione\"\n                    fullWidth\n                    variant=\"outlined\"\n                    value={strumentoFormData.certificato_calibrazione}\n                    onChange={handleStrumentoFormChange}\n                  />\n                </Grid>\n                <Grid item xs={12}>\n                  <TextField\n                    name=\"note\"\n                    label=\"Note\"\n                    fullWidth\n                    multiline\n                    rows={3}\n                    variant=\"outlined\"\n                    value={strumentoFormData.note}\n                    onChange={handleStrumentoFormChange}\n                  />\n                </Grid>\n              </Grid>\n            </Box>\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            <Button\n              onClick={handleSalvaStrumento}\n              disabled={loading}\n              startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}\n            >\n              Salva\n            </Button>\n          </DialogActions>\n        </Dialog>\n      );\n    }\n\n    return null;\n  };\n\n  return (\n    <Box>\n\n      {selectedOption === 'visualizzaCertificazioni' && !openDialog && (\n        <Box sx={{ mt: 3 }}>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n            <Typography variant=\"h6\">\n              Certificazioni\n              {filtroCavo && ` - Filtro: ${filtroCavo}`}\n            </Typography>\n            {filtroCavo && (\n              <Button\n                variant=\"outlined\"\n                startIcon={<ClearIcon />}\n                onClick={() => {\n                  setFiltroCavo('');\n                  loadCertificazioni('');\n                }}\n              >\n                Rimuovi Filtro\n              </Button>\n            )}\n          </Box>\n\n          {loading ? (\n            <CircularProgress />\n          ) : (\n            renderCertificazioniTable()\n          )}\n        </Box>\n      )}\n\n      {!selectedOption && (\n        <Paper sx={{ p: 3, minHeight: '300px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n          <Typography variant=\"body1\">\n            Seleziona un'opzione dal menu principale per iniziare.\n          </Typography>\n        </Paper>\n      )}\n\n      {renderDialog()}\n    </Box>\n  );\n});\n\nexport default CertificazioneCavi;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,EAAEC,mBAAmB,QAAQ,OAAO;AACnF,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,KAAK,EACLC,gBAAgB,EAChBC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,OAAO,EACPC,UAAU,EACVC,IAAI,QACC,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,YAAY,IAAIC,OAAO,EACvBC,MAAM,IAAIC,UAAU,EACpBC,UAAU,IAAIC,UAAU,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,KAAK,IAAIC,SAAS,EAClBC,QAAQ,IAAIC,YAAY,EACxBC,KAAK,IAAIC,SAAS,QACb,qBAAqB;AAC5B,OAAOC,qBAAqB,MAAM,sCAAsC;AACxE,OAAOC,WAAW,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,kBAAkB,gBAAAC,EAAA,cAAG5D,UAAU,CAAA6D,EAAA,GAAAD,EAAA,CAAC,CAAC;EAAEE,UAAU;EAAEC,SAAS;EAAEC,OAAO;EAAEC,UAAU,GAAG;AAAM,CAAC,EAAEC,GAAG,KAAK;EAAAN,EAAA;EACrG,MAAM,CAACO,OAAO,EAAEC,UAAU,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuE,cAAc,EAAEC,iBAAiB,CAAC,GAAGxE,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACyE,IAAI,EAAEC,OAAO,CAAC,GAAG1E,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAAC2E,SAAS,EAAEC,YAAY,CAAC,GAAG5E,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC6E,cAAc,EAAEC,iBAAiB,CAAC,GAAG9E,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC+E,UAAU,EAAEC,aAAa,CAAC,GAAGhF,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACiF,UAAU,EAAEC,aAAa,CAAC,GAAGlF,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmF,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGpF,QAAQ,CAAC,IAAI,CAAC;EAC1E,MAAM,CAACqF,YAAY,EAAEC,eAAe,CAAC,GAAGtF,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACuF,UAAU,EAAEC,aAAa,CAAC,GAAGxF,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACyF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1F,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAAC2F,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5F,QAAQ,CAAC;IACzD6F,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,iBAAiB,EAAE,EAAE;IACrBC,0BAA0B,EAAE,EAAE;IAC9BC,wBAAwB,EAAE,EAAE;IAC5BC,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGtG,QAAQ,CAAC;IACvCuG,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,kBAAkB,EAAE,EAAE;IACtBC,iBAAiB,EAAE,IAAI;IACvBC,iBAAiB,EAAE,EAAE;IACrBC,iBAAiB,EAAE,IAAI;IACvBR,IAAI,EAAE;EACR,CAAC,CAAC;;EAEF;EACA,MAAMS,kBAAkB,GAAG,MAAAA,CAAOtB,UAAU,GAAG,EAAE,KAAK;IACpD,IAAI;MACFjB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMwC,IAAI,GAAG,MAAMrD,qBAAqB,CAACsD,iBAAiB,CAAC/C,UAAU,EAAEuB,UAAU,CAAC;MAClFf,iBAAiB,CAACsC,IAAI,CAAC;IACzB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd9C,OAAO,CAAC,6CAA6C,CAAC;MACtD+C,OAAO,CAACD,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;IACtE,CAAC,SAAS;MACR1C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM4C,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACF5C,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMwC,IAAI,GAAG,MAAMpD,WAAW,CAACyD,OAAO,CAACnD,UAAU,CAAC;MAClDU,OAAO,CAACoC,IAAI,CAAC;IACf,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd9C,OAAO,CAAC,iCAAiC,CAAC;MAC1C+C,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;IAC1D,CAAC,SAAS;MACR1C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM8C,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF9C,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMwC,IAAI,GAAG,MAAMrD,qBAAqB,CAAC4D,YAAY,CAACrD,UAAU,CAAC;MACjEY,YAAY,CAACkC,IAAI,CAAC;IACpB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd9C,OAAO,CAAC,wCAAwC,CAAC;MACjD+C,OAAO,CAACD,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;IACjE,CAAC,SAAS;MACR1C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACAnE,mBAAmB,CAACiE,GAAG,EAAE,OAAO;IAC9BkD,kBAAkB;IAClBC,0BAA0B,EAAGC,IAAI,IAAK;MACpC;MACAJ,aAAa,CAAC,CAAC;MACfK,gBAAgB,CAACD,IAAI,CAAC;MACtBxC,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC;IACD0C,wBAAwB,EAAGF,IAAI,IAAK;MAClC;MACAX,kBAAkB,CAACW,IAAI,CAACjB,OAAO,CAAC;MAChCzB,iBAAiB,CAAC,0BAA0B,CAAC;IAC/C;EACF,CAAC,CAAC,CAAC;;EAEH;EACA7E,SAAS,CAAC,MAAM;IACd4G,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAAC7C,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAMsD,kBAAkB,GAAIK,MAAM,IAAK;IACrC7C,iBAAiB,CAAC6C,MAAM,CAAC;IAEzB,IAAIA,MAAM,KAAK,0BAA0B,EAAE;MACzCd,kBAAkB,CAAC,CAAC;IACtB,CAAC,MAAM,IAAIc,MAAM,KAAK,sBAAsB,EAAE;MAC5CzC,aAAa,CAAC,sBAAsB,CAAC;MACrCF,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAI2C,MAAM,KAAK,oBAAoB,EAAE;MAC1CT,QAAQ,CAAC,CAAC;MACVE,aAAa,CAAC,CAAC;MACflC,aAAa,CAAC,eAAe,CAAC;MAC9BF,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAI2C,MAAM,KAAK,wBAAwB,EAAE;MAC9Cd,kBAAkB,CAAC,CAAC;MACpB3B,aAAa,CAAC,yBAAyB,CAAC;MACxCF,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAI2C,MAAM,KAAK,WAAW,EAAE;MACjCd,kBAAkB,CAAC,CAAC;MACpB3B,aAAa,CAAC,4BAA4B,CAAC;MAC3CF,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAI2C,MAAM,KAAK,uBAAuB,EAAE;MAC7Cd,kBAAkB,CAAC,CAAC;MACpB3B,aAAa,CAAC,uBAAuB,CAAC;MACtCF,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAI2C,MAAM,KAAK,mBAAmB,EAAE;MACzCP,aAAa,CAAC,CAAC;MACflC,aAAa,CAAC,mBAAmB,CAAC;MAClCF,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAM4C,iBAAiB,GAAGA,CAAA,KAAM;IAC9B5C,aAAa,CAAC,KAAK,CAAC;IACpBI,yBAAyB,CAAC,IAAI,CAAC;IAC/BE,eAAe,CAAC,IAAI,CAAC;IACrBI,oBAAoB,CAAC,IAAI,CAAC;IAC1BY,WAAW,CAAC;MACVC,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,EAAE;MAChBC,kBAAkB,EAAE,EAAE;MACtBC,iBAAiB,EAAE,IAAI;MACvBC,iBAAiB,EAAE,EAAE;MACrBC,iBAAiB,EAAE,IAAI;MACvBR,IAAI,EAAE;IACR,CAAC,CAAC;IACFR,oBAAoB,CAAC;MACnBC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,EAAE;MAChBC,iBAAiB,EAAE,EAAE;MACrBC,0BAA0B,EAAE,EAAE;MAC9BC,wBAAwB,EAAE,EAAE;MAC5BC,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMyB,0BAA0B,GAAIC,cAAc,IAAK;IACrD1C,yBAAyB,CAAC0C,cAAc,CAAC;IAEzC,IAAI7C,UAAU,KAAK,yBAAyB,EAAE;MAC5CC,aAAa,CAAC,wBAAwB,CAAC;IACzC,CAAC,MAAM,IAAID,UAAU,KAAK,4BAA4B,EAAE;MACtD8C,eAAe,CAACD,cAAc,CAACE,iBAAiB,CAAC;IACnD;EACF,CAAC;;EAED;EACA,MAAMP,gBAAgB,GAAID,IAAI,IAAK;IACjClC,eAAe,CAACkC,IAAI,CAAC;IACrBlB,WAAW,CAAC;MACV,GAAGD,QAAQ;MACXE,OAAO,EAAEiB,IAAI,CAACjB,OAAO;MACrBE,kBAAkB,EAAEe,IAAI,CAACS,eAAe,IAAI;IAC9C,CAAC,CAAC;IACF/C,aAAa,CAAC,oBAAoB,CAAC;EACrC,CAAC;;EAED;EACA,MAAMgD,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChChC,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAAC+B,IAAI,GAAGC;IACV,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAME,gBAAgB,GAAGA,CAAA,KAAM;IAC7B1B,kBAAkB,CAACtB,UAAU,CAAC;IAC9BqC,iBAAiB,CAAC,CAAC;EACrB,CAAC;;EAED;EACA,MAAMY,qBAAqB,GAAG,MAAAA,CAAOC,MAAM,EAAEC,YAAY,GAAG,UAAU,KAAK;IACzE,IAAI;MACF;MACA,MAAMhF,WAAW,GAAG,CAAC,MAAM,MAAM,CAAC,4BAA4B,CAAC,EAAEiF,OAAO;MAExE,IAAIC,iBAAiB,GAAG,KAAK;MAC7B,IAAIC,eAAe,GAAG,KAAK;;MAE3B;MACA,IAAI;QACF,MAAMnF,WAAW,CAACoF,WAAW,CAAC9E,UAAU,EAAEyE,MAAM,EAAE,UAAU,EAAEC,YAAY,CAAC;QAC3EE,iBAAiB,GAAG,IAAI;QACxB3B,OAAO,CAAC8B,GAAG,CAAC,sCAAsC,CAAC;MACrD,CAAC,CAAC,OAAO/B,KAAK,EAAE;QACd,IAAIA,KAAK,CAACgC,MAAM,IAAIhC,KAAK,CAACgC,MAAM,CAACC,QAAQ,CAAC,eAAe,CAAC,EAAE;UAC1DL,iBAAiB,GAAG,IAAI;UACxB3B,OAAO,CAAC8B,GAAG,CAAC,6BAA6B,CAAC;QAC5C,CAAC,MAAM;UACL9B,OAAO,CAACD,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;UAC9D,MAAMA,KAAK;QACb;MACF;;MAEA;MACA,IAAI;QACF,MAAMtD,WAAW,CAACoF,WAAW,CAAC9E,UAAU,EAAEyE,MAAM,EAAE,QAAQ,EAAEC,YAAY,CAAC;QACzEG,eAAe,GAAG,IAAI;QACtB5B,OAAO,CAAC8B,GAAG,CAAC,oCAAoC,CAAC;MACnD,CAAC,CAAC,OAAO/B,KAAK,EAAE;QACd,IAAIA,KAAK,CAACgC,MAAM,IAAIhC,KAAK,CAACgC,MAAM,CAACC,QAAQ,CAAC,eAAe,CAAC,EAAE;UAC1DJ,eAAe,GAAG,IAAI;UACtB5B,OAAO,CAAC8B,GAAG,CAAC,2BAA2B,CAAC;QAC1C,CAAC,MAAM;UACL9B,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;UAC5D,MAAMA,KAAK;QACb;MACF;MAEA,OAAO4B,iBAAiB,IAAIC,eAAe;IAC7C,CAAC,CAAC,OAAO7B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,MAAMA,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMkC,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C,IAAI;MACF,IAAI,CAAC7C,QAAQ,CAACE,OAAO,IAAI,CAACF,QAAQ,CAACG,YAAY,IAAI,CAACH,QAAQ,CAACM,iBAAiB,EAAE;QAC9EzC,OAAO,CAAC,mCAAmC,CAAC;QAC5C;MACF;;MAEA;MACA,MAAMsD,IAAI,GAAG/C,IAAI,CAAC0E,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7C,OAAO,KAAKF,QAAQ,CAACE,OAAO,CAAC;MAC3D,IAAIiB,IAAI,EAAE;QACR,MAAM6B,WAAW,GAAG7B,IAAI,CAAC8B,YAAY,KAAK,CAAC;QAE3C,IAAI,CAACD,WAAW,EAAE;UAChB,MAAME,iBAAiB,GAAG/B,IAAI,CAAC8B,YAAY,KAAK,CAAC,GAAG,eAAe,GAC1C9B,IAAI,CAAC8B,YAAY,KAAK,CAAC,GAAG,yBAAyB,GACnD9B,IAAI,CAAC8B,YAAY,KAAK,CAAC,GAAG,uBAAuB,GACjD,mBAAmB;UAE5C,MAAME,QAAQ,GAAGC,MAAM,CAACC,OAAO,CAC7B,uBAAuBlC,IAAI,CAACjB,OAAO,2CAA2C,GAC9E,uBAAuBgD,iBAAiB,MAAM,GAC9C,gGAAgG,GAChG,iFACF,CAAC;UAED,IAAI,CAACC,QAAQ,EAAE;YACb;UACF;;UAEA;UACA,IAAI;YACFlF,UAAU,CAAC,IAAI,CAAC;YAChB,MAAMkE,qBAAqB,CAAChB,IAAI,CAACjB,OAAO,EAAE,UAAU,CAAC;YACrDtC,SAAS,CAAC,mDAAmD,CAAC;;YAE9D;YACA,MAAMiD,QAAQ,CAAC,CAAC;UAClB,CAAC,CAAC,OAAOF,KAAK,EAAE;YACd9C,OAAO,CAAC,sCAAsC,IAAI8C,KAAK,CAAC2C,OAAO,IAAI,oBAAoB,CAAC,CAAC;YACzF;UACF;QACF;MACF;MAEArF,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMb,qBAAqB,CAACmG,oBAAoB,CAAC5F,UAAU,EAAEqC,QAAQ,CAAC;MACtEpC,SAAS,CAAC,oCAAoC,CAAC;MAC/C2D,iBAAiB,CAAC,CAAC;MACnBf,kBAAkB,CAAC,CAAC;IACtB,CAAC,CAAC,OAAOG,KAAK,EAAE;MACd9C,OAAO,CAAC,+CAA+C,IAAI8C,KAAK,CAAC2C,OAAO,IAAI,oBAAoB,CAAC,CAAC;MAClG1C,OAAO,CAACD,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;IACtE,CAAC,SAAS;MACR1C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMuF,2BAA2B,GAAG,MAAAA,CAAA,KAAY;IAC9C,IAAI;MACF,IAAI,CAAC1E,sBAAsB,EAAE;QAC3BjB,OAAO,CAAC,2CAA2C,CAAC;QACpD;MACF;MAEAI,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMb,qBAAqB,CAACqG,oBAAoB,CAAC9F,UAAU,EAAEmB,sBAAsB,CAAC6C,iBAAiB,CAAC;MACtG/D,SAAS,CAAC,uCAAuC,CAAC;MAClD2D,iBAAiB,CAAC,CAAC;MACnBf,kBAAkB,CAAC,CAAC;IACtB,CAAC,CAAC,OAAOG,KAAK,EAAE;MACd9C,OAAO,CAAC,kDAAkD,IAAI8C,KAAK,CAAC2C,OAAO,IAAI,oBAAoB,CAAC,CAAC;MACrG1C,OAAO,CAACD,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;IACzE,CAAC,SAAS;MACR1C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMyD,eAAe,GAAG,MAAOgC,gBAAgB,IAAK;IAClD,IAAI;MACFzF,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM0F,QAAQ,GAAG,MAAMvG,qBAAqB,CAACwG,WAAW,CAACjG,UAAU,EAAE+F,gBAAgB,CAAC;;MAEtF;MACAN,MAAM,CAACS,IAAI,CAACF,QAAQ,CAACG,QAAQ,EAAE,QAAQ,CAAC;MAExClG,SAAS,CAAC,2BAA2B,CAAC;MACtC2D,iBAAiB,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACd9C,OAAO,CAAC,oCAAoC,IAAI8C,KAAK,CAAC2C,OAAO,IAAI,oBAAoB,CAAC,CAAC;MACvF1C,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;IAC3D,CAAC,SAAS;MACR1C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM8F,qBAAqB,GAAIC,SAAS,IAAK;IAC3C3E,oBAAoB,CAAC2E,SAAS,CAAC;IAC/BzE,oBAAoB,CAAC;MACnBC,IAAI,EAAEwE,SAAS,CAACxE,IAAI,IAAI,EAAE;MAC1BC,KAAK,EAAEuE,SAAS,CAACvE,KAAK,IAAI,EAAE;MAC5BC,OAAO,EAAEsE,SAAS,CAACtE,OAAO,IAAI,EAAE;MAChCC,YAAY,EAAEqE,SAAS,CAACrE,YAAY,IAAI,EAAE;MAC1CC,iBAAiB,EAAEoE,SAAS,CAACpE,iBAAiB,IAAI,EAAE;MACpDC,0BAA0B,EAAEmE,SAAS,CAACnE,0BAA0B,IAAI,EAAE;MACtEC,wBAAwB,EAAEkE,SAAS,CAAClE,wBAAwB,IAAI,EAAE;MAClEC,IAAI,EAAEiE,SAAS,CAACjE,IAAI,IAAI;IAC1B,CAAC,CAAC;IACFlB,aAAa,CAAC,mBAAmB,CAAC;EACpC,CAAC;;EAED;EACA,MAAMoF,yBAAyB,GAAInC,CAAC,IAAK;IACvC,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChC1C,oBAAoB,CAAC;MACnB,GAAGD,iBAAiB;MACpB,CAACyC,IAAI,GAAGC;IACV,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMkC,mBAAmB,GAAGA,CAAA,KAAM;IAChC7E,oBAAoB,CAAC,IAAI,CAAC;IAC1BE,oBAAoB,CAAC;MACnBC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,EAAE;MAChBC,iBAAiB,EAAE,EAAE;MACrBC,0BAA0B,EAAE,EAAE;MAC9BC,wBAAwB,EAAE,EAAE;MAC5BC,IAAI,EAAE;IACR,CAAC,CAAC;IACFlB,aAAa,CAAC,eAAe,CAAC;EAChC,CAAC;;EAED;EACA,MAAMsF,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF,IAAI,CAAC7E,iBAAiB,CAACE,IAAI,IAAI,CAACF,iBAAiB,CAACG,KAAK,IAAI,CAACH,iBAAiB,CAACI,OAAO,IAAI,CAACJ,iBAAiB,CAACK,YAAY,EAAE;QACxH9B,OAAO,CAAC,mCAAmC,CAAC;QAC5C;MACF;MAEA,IAAI,CAACyB,iBAAiB,CAACM,iBAAiB,IAAI,CAACN,iBAAiB,CAACO,0BAA0B,EAAE;QACzFhC,OAAO,CAAC,sDAAsD,CAAC;QAC/D;MACF;MAEAI,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAImB,iBAAiB,EAAE;QACrB,MAAMhC,qBAAqB,CAACgH,eAAe,CAACzG,UAAU,EAAEyB,iBAAiB,CAACe,YAAY,EAAEb,iBAAiB,CAAC;QAC1G1B,SAAS,CAAC,mCAAmC,CAAC;MAChD,CAAC,MAAM;QACL,MAAMR,qBAAqB,CAACiH,eAAe,CAAC1G,UAAU,EAAE2B,iBAAiB,CAAC;QAC1E1B,SAAS,CAAC,+BAA+B,CAAC;MAC5C;MAEA2D,iBAAiB,CAAC,CAAC;MACnBR,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACd9C,OAAO,CAAC,0CAA0C,IAAI8C,KAAK,CAAC2C,OAAO,IAAI,oBAAoB,CAAC,CAAC;MAC7F1C,OAAO,CAACD,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;IACjE,CAAC,SAAS;MACR1C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMqG,sBAAsB,GAAG,MAAON,SAAS,IAAK;IAClD,IAAI;MACF,IAAIZ,MAAM,CAACC,OAAO,CAAC,+CAA+CW,SAAS,CAACxE,IAAI,IAAIwE,SAAS,CAACvE,KAAK,IAAIuE,SAAS,CAACtE,OAAO,IAAI,CAAC,EAAE;QAC7HzB,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMb,qBAAqB,CAACmH,eAAe,CAAC5G,UAAU,EAAEqG,SAAS,CAAC7D,YAAY,CAAC;QAC/EvC,SAAS,CAAC,kCAAkC,CAAC;QAC7CmD,aAAa,CAAC,CAAC;MACjB;IACF,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACd9C,OAAO,CAAC,6CAA6C,IAAI8C,KAAK,CAAC2C,OAAO,IAAI,oBAAoB,CAAC,CAAC;MAChG1C,OAAO,CAACD,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;IACpE,CAAC,SAAS;MACR1C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMuG,yBAAyB,GAAGA,CAAA,KAAM;IACtC,IAAItG,cAAc,CAACuG,MAAM,KAAK,CAAC,EAAE;MAC/B,oBACElH,OAAA,CAAClC,KAAK;QAACqJ,QAAQ,EAAC,MAAM;QAAAC,QAAA,EAAC;MAA8B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAEjE;IAEA,oBACExH,OAAA,CAAC7B,cAAc;MAACsJ,SAAS,EAAE9K,KAAM;MAAAyK,QAAA,eAC/BpH,OAAA,CAAChC,KAAK;QAAC0J,IAAI,EAAC,OAAO;QAAAN,QAAA,gBACjBpH,OAAA,CAAC5B,SAAS;UAAAgJ,QAAA,eACRpH,OAAA,CAAC3B,QAAQ;YAAA+I,QAAA,gBACPpH,OAAA,CAAC9B,SAAS;cAAAkJ,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACzBxH,OAAA,CAAC9B,SAAS;cAAAkJ,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC3BxH,OAAA,CAAC9B,SAAS;cAAAkJ,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC3BxH,OAAA,CAAC9B,SAAS;cAAAkJ,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAChCxH,OAAA,CAAC9B,SAAS;cAAAkJ,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAChCxH,OAAA,CAAC9B,SAAS;cAAAkJ,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAChCxH,OAAA,CAAC9B,SAAS;cAAAkJ,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACjCxH,OAAA,CAAC9B,SAAS;cAAAkJ,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZxH,OAAA,CAAC/B,SAAS;UAAAmJ,QAAA,EACPzG,cAAc,CAACgH,GAAG,CAAEC,IAAI,iBACvB5H,OAAA,CAAC3B,QAAQ;YAAA+I,QAAA,gBACPpH,OAAA,CAAC9B,SAAS;cAAAkJ,QAAA,EAAEQ,IAAI,CAACxD;YAAiB;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/CxH,OAAA,CAAC9B,SAAS;cAAAkJ,QAAA,EAAEQ,IAAI,CAACjF;YAAO;cAAA0E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACrCxH,OAAA,CAAC9B,SAAS;cAAAkJ,QAAA,EAAE,IAAIS,IAAI,CAACD,IAAI,CAACE,mBAAmB,CAAC,CAACC,kBAAkB,CAAC;YAAC;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChFxH,OAAA,CAAC9B,SAAS;cAAAkJ,QAAA,EAAEQ,IAAI,CAACI;YAAS;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACvCxH,OAAA,CAAC9B,SAAS;cAAAkJ,QAAA,EAAEQ,IAAI,CAACnB;YAAS;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACvCxH,OAAA,CAAC9B,SAAS;cAAAkJ,QAAA,GAAEQ,IAAI,CAAC/E,kBAAkB,EAAC,IAAE;YAAA;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAClDxH,OAAA,CAAC9B,SAAS;cAAAkJ,QAAA,GAAEQ,IAAI,CAAC7E,iBAAiB,EAAC,UAAG;YAAA;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAClDxH,OAAA,CAAC9B,SAAS;cAAAkJ,QAAA,gBACRpH,OAAA,CAACzB,UAAU;gBACTmJ,IAAI,EAAC,OAAO;gBACZO,OAAO,EAAEA,CAAA,KAAM;kBACbzG,yBAAyB,CAACoG,IAAI,CAAC;kBAC/BtG,aAAa,CAAC,wBAAwB,CAAC;kBACvCF,aAAa,CAAC,IAAI,CAAC;gBACrB,CAAE;gBAAAgG,QAAA,eAEFpH,OAAA,CAACd,UAAU;kBAACgJ,QAAQ,EAAC;gBAAO;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC,eACbxH,OAAA,CAACzB,UAAU;gBACTmJ,IAAI,EAAC,OAAO;gBACZO,OAAO,EAAEA,CAAA,KAAM9D,eAAe,CAACyD,IAAI,CAACxD,iBAAiB,CAAE;gBAAAgD,QAAA,eAEvDpH,OAAA,CAAChB,OAAO;kBAACkJ,QAAQ,EAAC;gBAAO;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC,eACbxH,OAAA,CAACzB,UAAU;gBACTmJ,IAAI,EAAC,OAAO;gBACZS,KAAK,EAAC,OAAO;gBACbF,OAAO,EAAEA,CAAA,KAAM;kBACbzG,yBAAyB,CAACoG,IAAI,CAAC;kBAC/BtG,aAAa,CAAC,uBAAuB,CAAC;kBACtCF,aAAa,CAAC,IAAI,CAAC;gBACrB,CAAE;gBAAAgG,QAAA,eAEFpH,OAAA,CAAClB,UAAU;kBAACoJ,QAAQ,EAAC;gBAAO;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA,GApCCI,IAAI,CAACxD,iBAAiB;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAqC3B,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC;EAErB,CAAC;;EAED;EACA,MAAMY,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI/G,UAAU,KAAK,sBAAsB,EAAE;MACzC,oBACErB,OAAA,CAAChD,MAAM;QAACsJ,IAAI,EAAEnF,UAAW;QAACkH,OAAO,EAAErE,iBAAkB;QAACsE,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAnB,QAAA,gBAC3EpH,OAAA,CAAC/C,WAAW;UAAAmK,QAAA,EAAC;QAA8B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACzDxH,OAAA,CAAC9C,aAAa;UAAAkK,QAAA,eACZpH,OAAA,CAACxD,GAAG;YAACgM,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAArB,QAAA,eACjBpH,OAAA,CAAC5C,SAAS;cACRmL,SAAS;cACTG,KAAK,EAAC,SAAS;cACfC,OAAO,EAAC,UAAU;cAClBlE,KAAK,EAAE9C,UAAW;cAClBiH,QAAQ,EAAGrE,CAAC,IAAK3C,aAAa,CAAC2C,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;cAC/CoE,WAAW,EAAC;YAAyB;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAChBxH,OAAA,CAAC7C,aAAa;UAAAiK,QAAA,gBACZpH,OAAA,CAACtD,MAAM;YACLuL,OAAO,EAAEA,CAAA,KAAM;cACbrG,aAAa,CAAC,EAAE,CAAC;cACjBqB,kBAAkB,CAAC,EAAE,CAAC;cACtBe,iBAAiB,CAAC,CAAC;YACrB,CAAE;YACF8E,SAAS,eAAE9I,OAAA,CAACR,SAAS;cAAA6H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EAC1B;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTxH,OAAA,CAACtD,MAAM;YAACuL,OAAO,EAAEjE,iBAAkB;YAAAoD,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpDxH,OAAA,CAACtD,MAAM;YACLuL,OAAO,EAAEtD,gBAAiB;YAC1BoE,QAAQ,EAAEtI,OAAQ;YAClBqI,SAAS,EAAErI,OAAO,gBAAGT,OAAA,CAACjC,gBAAgB;cAAC2J,IAAI,EAAE;YAAG;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGxH,OAAA,CAACZ,UAAU;cAAAiI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EACtE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAInG,UAAU,KAAK,eAAe,EAAE;MACzC,oBACErB,OAAA,CAAChD,MAAM;QAACsJ,IAAI,EAAEnF,UAAW;QAACkH,OAAO,EAAErE,iBAAkB;QAACsE,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAnB,QAAA,gBAC3EpH,OAAA,CAAC/C,WAAW;UAAAmK,QAAA,EAAC;QAAiC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAC5DxH,OAAA,CAAC9C,aAAa;UAAAkK,QAAA,EACX3G,OAAO,gBACNT,OAAA,CAACjC,gBAAgB;YAAAsJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClB3G,IAAI,CAACqG,MAAM,KAAK,CAAC,gBACnBlH,OAAA,CAAClC,KAAK;YAACqJ,QAAQ,EAAC,MAAM;YAAAC,QAAA,EAAC;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,gBAEtDxH,OAAA,CAACvC,IAAI;YAAA2J,QAAA,EACFvG,IAAI,CAAC8G,GAAG,CAAE/D,IAAI,iBACb5D,OAAA,CAACtC,QAAQ;cACPsL,MAAM;cAENf,OAAO,EAAEA,CAAA,KAAMpE,gBAAgB,CAACD,IAAI,CAAE;cAAAwD,QAAA,eAEtCpH,OAAA,CAACrC,YAAY;gBACXsL,OAAO,EAAErF,IAAI,CAACjB,OAAQ;gBACtBuG,SAAS,EAAE,GAAGtF,IAAI,CAACuF,SAAS,IAAI,KAAK,UAAUvF,IAAI,CAACwF,mBAAmB,IAAI,KAAK,OAAOxF,IAAI,CAACyF,iBAAiB,IAAI,KAAK;cAAG;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1H;YAAC,GANG5D,IAAI,CAACjB,OAAO;cAAA0E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOT,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QACP;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChBxH,OAAA,CAAC7C,aAAa;UAAAiK,QAAA,eACZpH,OAAA,CAACtD,MAAM;YAACuL,OAAO,EAAEjE,iBAAkB;YAAAoD,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAInG,UAAU,KAAK,oBAAoB,EAAE;MAC9C,oBACErB,OAAA,CAAChD,MAAM;QAACsJ,IAAI,EAAEnF,UAAW;QAACkH,OAAO,EAAErE,iBAAkB;QAACsE,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAnB,QAAA,gBAC3EpH,OAAA,CAAC/C,WAAW;UAAAmK,QAAA,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACpDxH,OAAA,CAAC9C,aAAa;UAAAkK,QAAA,eACZpH,OAAA,CAACxD,GAAG;YAACgM,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAArB,QAAA,gBACjBpH,OAAA,CAACvD,UAAU;cAACkM,OAAO,EAAC,WAAW;cAACW,YAAY;cAAAlC,QAAA,GAAC,oBACzB,EAAC3F,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEkB,OAAO;YAAA;cAAA0E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eACbxH,OAAA,CAACvD,UAAU;cAACkM,OAAO,EAAC,OAAO;cAACW,YAAY;cAAAlC,QAAA,GAAC,aAC5B,EAAC,CAAA3F,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE0H,SAAS,KAAI,KAAK;YAAA;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACbxH,OAAA,CAACvD,UAAU;cAACkM,OAAO,EAAC,OAAO;cAACW,YAAY;cAAAlC,QAAA,GAAC,aAC5B,EAAC,CAAA3F,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE4C,eAAe,KAAI,GAAG,EAAC,IACnD;YAAA;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAEbxH,OAAA,CAAC1B,OAAO;cAACkK,EAAE,EAAE;gBAAEe,EAAE,EAAE;cAAE;YAAE;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAE1BxH,OAAA,CAACpD,IAAI;cAAC4M,SAAS;cAACC,OAAO,EAAE,CAAE;cAAArC,QAAA,gBACzBpH,OAAA,CAACpD,IAAI;gBAAC8M,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAAxC,QAAA,eACvBpH,OAAA,CAAC3C,WAAW;kBAACkL,SAAS;kBAACI,OAAO,EAAC,UAAU;kBAAAvB,QAAA,gBACvCpH,OAAA,CAAC1C,UAAU;oBAAA8J,QAAA,EAAC;kBAAoB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC7CxH,OAAA,CAACzC,MAAM;oBACLiH,IAAI,EAAC,cAAc;oBACnBC,KAAK,EAAEhC,QAAQ,CAACG,YAAa;oBAC7BgG,QAAQ,EAAEtE,gBAAiB;oBAC3BoE,KAAK,EAAC,sBAAsB;oBAC5BmB,QAAQ;oBAAAzC,QAAA,EAEPrG,SAAS,CAAC4G,GAAG,CAAElB,SAAS,iBACvBzG,OAAA,CAACxC,QAAQ;sBAA8BiH,KAAK,EAAEgC,SAAS,CAAC7D,YAAa;sBAAAwE,QAAA,GAClEX,SAAS,CAACxE,IAAI,EAAC,KAAG,EAACwE,SAAS,CAACtE,OAAO;oBAAA,GADxBsE,SAAS,CAAC7D,YAAY;sBAAAyE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAE3B,CACX;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACPxH,OAAA,CAACpD,IAAI;gBAAC8M,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAAxC,QAAA,eACvBpH,OAAA,CAAC5C,SAAS;kBACRoH,IAAI,EAAC,oBAAoB;kBACzBkE,KAAK,EAAC,wBAAwB;kBAC9BoB,IAAI,EAAC,QAAQ;kBACbvB,SAAS;kBACTI,OAAO,EAAC,UAAU;kBAClBlE,KAAK,EAAEhC,QAAQ,CAACI,kBAAmB;kBACnC+F,QAAQ,EAAEtE,gBAAiB;kBAC3BuF,QAAQ;gBAAA;kBAAAxC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACPxH,OAAA,CAACpD,IAAI;gBAAC8M,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAAxC,QAAA,eACvBpH,OAAA,CAAC3C,WAAW;kBAACkL,SAAS;kBAACI,OAAO,EAAC,UAAU;kBAAAvB,QAAA,gBACvCpH,OAAA,CAAC1C,UAAU;oBAAA8J,QAAA,EAAC;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACxCxH,OAAA,CAACzC,MAAM;oBACLiH,IAAI,EAAC,mBAAmB;oBACxBC,KAAK,EAAEhC,QAAQ,CAACK,iBAAkB;oBAClC8F,QAAQ,EAAEtE,gBAAiB;oBAC3BoE,KAAK,EAAC,oBAAiB;oBAAAtB,QAAA,gBAEvBpH,OAAA,CAACxC,QAAQ;sBAACiH,KAAK,EAAC,IAAI;sBAAA2C,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC,eAClCxH,OAAA,CAACxC,QAAQ;sBAACiH,KAAK,EAAC,QAAQ;sBAAA2C,QAAA,EAAC;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACPxH,OAAA,CAACpD,IAAI;gBAAC8M,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAAxC,QAAA,eACvBpH,OAAA,CAAC5C,SAAS;kBACRoH,IAAI,EAAC,mBAAmB;kBACxBkE,KAAK,EAAC,6BAAwB;kBAC9BH,SAAS;kBACTI,OAAO,EAAC,UAAU;kBAClBlE,KAAK,EAAEhC,QAAQ,CAACM,iBAAkB;kBAClC6F,QAAQ,EAAEtE,gBAAiB;kBAC3BuF,QAAQ;gBAAA;kBAAAxC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACPxH,OAAA,CAACpD,IAAI;gBAAC8M,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAAxC,QAAA,eACvBpH,OAAA,CAAC3C,WAAW;kBAACkL,SAAS;kBAACI,OAAO,EAAC,UAAU;kBAAAvB,QAAA,gBACvCpH,OAAA,CAAC1C,UAAU;oBAAA8J,QAAA,EAAC;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACxCxH,OAAA,CAACzC,MAAM;oBACLiH,IAAI,EAAC,mBAAmB;oBACxBC,KAAK,EAAEhC,QAAQ,CAACO,iBAAkB;oBAClC4F,QAAQ,EAAEtE,gBAAiB;oBAC3BoE,KAAK,EAAC,iBAAiB;oBAAAtB,QAAA,gBAEvBpH,OAAA,CAACxC,QAAQ;sBAACiH,KAAK,EAAC,IAAI;sBAAA2C,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC,eAClCxH,OAAA,CAACxC,QAAQ;sBAACiH,KAAK,EAAC,QAAQ;sBAAA2C,QAAA,EAAC;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACPxH,OAAA,CAACpD,IAAI;gBAAC8M,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAAAvC,QAAA,eAChBpH,OAAA,CAAC5C,SAAS;kBACRoH,IAAI,EAAC,MAAM;kBACXkE,KAAK,EAAC,MAAM;kBACZH,SAAS;kBACTwB,SAAS;kBACTC,IAAI,EAAE,CAAE;kBACRrB,OAAO,EAAC,UAAU;kBAClBlE,KAAK,EAAEhC,QAAQ,CAACD,IAAK;kBACrBoG,QAAQ,EAAEtE;gBAAiB;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAChBxH,OAAA,CAAC7C,aAAa;UAAAiK,QAAA,gBACZpH,OAAA,CAACtD,MAAM;YAACuL,OAAO,EAAEjE,iBAAkB;YAAAoD,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpDxH,OAAA,CAACtD,MAAM;YACLuL,OAAO,EAAE3C,wBAAyB;YAClCyD,QAAQ,EAAEtI,OAAO,IAAI,CAACgC,QAAQ,CAACG,YAAY,IAAI,CAACH,QAAQ,CAACM,iBAAkB;YAC3E+F,SAAS,EAAErI,OAAO,gBAAGT,OAAA,CAACjC,gBAAgB;cAAC2J,IAAI,EAAE;YAAG;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGxH,OAAA,CAACV,QAAQ;cAAA+H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EACpE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAInG,UAAU,KAAK,yBAAyB,IAAIA,UAAU,KAAK,4BAA4B,EAAE;MAClG,oBACErB,OAAA,CAAChD,MAAM;QAACsJ,IAAI,EAAEnF,UAAW;QAACkH,OAAO,EAAErE,iBAAkB;QAACsE,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAnB,QAAA,gBAC3EpH,OAAA,CAAC/C,WAAW;UAAAmK,QAAA,EACT/F,UAAU,KAAK,yBAAyB,GACrC,0CAA0C,GAC1C;QAAkC;UAAAgG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eACdxH,OAAA,CAAC9C,aAAa;UAAAkK,QAAA,EACX3G,OAAO,gBACNT,OAAA,CAACjC,gBAAgB;YAAAsJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClB7G,cAAc,CAACuG,MAAM,KAAK,CAAC,gBAC7BlH,OAAA,CAAClC,KAAK;YAACqJ,QAAQ,EAAC,MAAM;YAAAC,QAAA,EAAC;UAA8B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,gBAE7DxH,OAAA,CAACvC,IAAI;YAAA2J,QAAA,EACFzG,cAAc,CAACgH,GAAG,CAAEC,IAAI,iBACvB5H,OAAA,CAACtC,QAAQ;cACPsL,MAAM;cAENf,OAAO,EAAEA,CAAA,KAAMhE,0BAA0B,CAAC2D,IAAI,CAAE;cAAAR,QAAA,eAEhDpH,OAAA,CAACrC,YAAY;gBACXsL,OAAO,EAAE,OAAOrB,IAAI,CAACxD,iBAAiB,YAAYwD,IAAI,CAACjF,OAAO,EAAG;gBACjEuG,SAAS,EAAE,SAAS,IAAIrB,IAAI,CAACD,IAAI,CAACE,mBAAmB,CAAC,CAACC,kBAAkB,CAAC,CAAC,iBAAiBH,IAAI,CAACI,SAAS;cAAG;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9G;YAAC,GANGI,IAAI,CAACxD,iBAAiB;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOnB,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QACP;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChBxH,OAAA,CAAC7C,aAAa;UAAAiK,QAAA,eACZpH,OAAA,CAACtD,MAAM;YAACuL,OAAO,EAAEjE,iBAAkB;YAAAoD,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAInG,UAAU,KAAK,wBAAwB,EAAE;MAClD,oBACErB,OAAA,CAAChD,MAAM;QAACsJ,IAAI,EAAEnF,UAAW;QAACkH,OAAO,EAAErE,iBAAkB;QAACsE,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAnB,QAAA,gBAC3EpH,OAAA,CAAC/C,WAAW;UAAAmK,QAAA,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAClDxH,OAAA,CAAC9C,aAAa;UAAAkK,QAAA,EACX,CAAC7F,sBAAsB,gBACtBvB,OAAA,CAACjC,gBAAgB;YAAAsJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEpBxH,OAAA,CAACxD,GAAG;YAACgM,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAArB,QAAA,gBACjBpH,OAAA,CAACpD,IAAI;cAAC4M,SAAS;cAACC,OAAO,EAAE,CAAE;cAAArC,QAAA,gBACzBpH,OAAA,CAACpD,IAAI;gBAAC8M,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAAxC,QAAA,gBACvBpH,OAAA,CAACvD,UAAU;kBAACkM,OAAO,EAAC,WAAW;kBAAAvB,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC/DxH,OAAA,CAACvD,UAAU;kBAACkM,OAAO,EAAC,OAAO;kBAACW,YAAY;kBAAAlC,QAAA,EAAE7F,sBAAsB,CAAC6C;gBAAiB;kBAAAiD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5F,CAAC,eACPxH,OAAA,CAACpD,IAAI;gBAAC8M,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAAxC,QAAA,gBACvBpH,OAAA,CAACvD,UAAU;kBAACkM,OAAO,EAAC,WAAW;kBAAAvB,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAClDxH,OAAA,CAACvD,UAAU;kBAACkM,OAAO,EAAC,OAAO;kBAACW,YAAY;kBAAAlC,QAAA,EAAE7F,sBAAsB,CAACoB;gBAAO;kBAAA0E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClF,CAAC,eACPxH,OAAA,CAACpD,IAAI;gBAAC8M,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAAxC,QAAA,gBACvBpH,OAAA,CAACvD,UAAU;kBAACkM,OAAO,EAAC,WAAW;kBAAAvB,QAAA,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACjExH,OAAA,CAACvD,UAAU;kBAACkM,OAAO,EAAC,OAAO;kBAACW,YAAY;kBAAAlC,QAAA,EACrC,IAAIS,IAAI,CAACtG,sBAAsB,CAACuG,mBAAmB,CAAC,CAACC,kBAAkB,CAAC;gBAAC;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACPxH,OAAA,CAACpD,IAAI;gBAAC8M,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAAxC,QAAA,gBACvBpH,OAAA,CAACvD,UAAU;kBAACkM,OAAO,EAAC,WAAW;kBAAAvB,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACvDxH,OAAA,CAACvD,UAAU;kBAACkM,OAAO,EAAC,OAAO;kBAACW,YAAY;kBAAAlC,QAAA,EAAE7F,sBAAsB,CAACyG;gBAAS;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpF,CAAC,eACPxH,OAAA,CAACpD,IAAI;gBAAC8M,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAAxC,QAAA,gBACvBpH,OAAA,CAACvD,UAAU;kBAACkM,OAAO,EAAC,WAAW;kBAAAvB,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACvDxH,OAAA,CAACvD,UAAU;kBAACkM,OAAO,EAAC,OAAO;kBAACW,YAAY;kBAAAlC,QAAA,EAAE7F,sBAAsB,CAACkF;gBAAS;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpF,CAAC,eACPxH,OAAA,CAACpD,IAAI;gBAAC8M,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAAxC,QAAA,gBACvBpH,OAAA,CAACvD,UAAU;kBAACkM,OAAO,EAAC,WAAW;kBAAAvB,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC1DxH,OAAA,CAACvD,UAAU;kBAACkM,OAAO,EAAC,OAAO;kBAACW,YAAY;kBAAAlC,QAAA,EAAE7F,sBAAsB,CAACqB;gBAAY;kBAAAyE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvF,CAAC,eACPxH,OAAA,CAACpD,IAAI;gBAAC8M,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAAxC,QAAA,gBACvBpH,OAAA,CAACvD,UAAU;kBAACkM,OAAO,EAAC,WAAW;kBAAAvB,QAAA,EAAC;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAChExH,OAAA,CAACvD,UAAU;kBAACkM,OAAO,EAAC,OAAO;kBAACW,YAAY;kBAAAlC,QAAA,GAAE7F,sBAAsB,CAACsB,kBAAkB,EAAC,IAAE;gBAAA;kBAAAwE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/F,CAAC,eACPxH,OAAA,CAACpD,IAAI;gBAAC8M,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAAxC,QAAA,gBACvBpH,OAAA,CAACvD,UAAU;kBAACkM,OAAO,EAAC,WAAW;kBAAAvB,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7DxH,OAAA,CAACvD,UAAU;kBAACkM,OAAO,EAAC,OAAO;kBAACW,YAAY;kBAAAlC,QAAA,EAAE7F,sBAAsB,CAACuB;gBAAiB;kBAAAuE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5F,CAAC,eACPxH,OAAA,CAACpD,IAAI;gBAAC8M,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAAxC,QAAA,gBACvBpH,OAAA,CAACvD,UAAU;kBAACkM,OAAO,EAAC,WAAW;kBAAAvB,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC/DxH,OAAA,CAACvD,UAAU;kBAACkM,OAAO,EAAC,OAAO;kBAACW,YAAY;kBAAAlC,QAAA,GAAE7F,sBAAsB,CAACwB,iBAAiB,EAAC,UAAG;gBAAA;kBAAAsE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/F,CAAC,eACPxH,OAAA,CAACpD,IAAI;gBAAC8M,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAAxC,QAAA,gBACvBpH,OAAA,CAACvD,UAAU;kBAACkM,OAAO,EAAC,WAAW;kBAAAvB,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7DxH,OAAA,CAACvD,UAAU;kBAACkM,OAAO,EAAC,OAAO;kBAACW,YAAY;kBAAAlC,QAAA,EAAE7F,sBAAsB,CAACyB;gBAAiB;kBAAAqE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5F,CAAC,EACNjG,sBAAsB,CAACiB,IAAI,iBAC1BxC,OAAA,CAACpD,IAAI;gBAAC8M,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAAAvC,QAAA,gBAChBpH,OAAA,CAACvD,UAAU;kBAACkM,OAAO,EAAC,WAAW;kBAAAvB,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAClDxH,OAAA,CAACvD,UAAU;kBAACkM,OAAO,EAAC,OAAO;kBAACW,YAAY;kBAAAlC,QAAA,EAAE7F,sBAAsB,CAACiB;gBAAI;kBAAA6E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/E,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eAEPxH,OAAA,CAACxD,GAAG;cAACgM,EAAE,EAAE;gBAAEC,EAAE,EAAE,CAAC;gBAAEwB,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE;cAAW,CAAE;cAAA9C,QAAA,eAC9DpH,OAAA,CAACtD,MAAM;gBACLiM,OAAO,EAAC,WAAW;gBACnBG,SAAS,eAAE9I,OAAA,CAAChB,OAAO;kBAAAqI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACvBS,OAAO,EAAEA,CAAA,KAAM9D,eAAe,CAAC5C,sBAAsB,CAAC6C,iBAAiB,CAAE;gBACzEoE,EAAE,EAAE;kBAAE2B,EAAE,EAAE;gBAAE,CAAE;gBAAA/C,QAAA,EACf;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChBxH,OAAA,CAAC7C,aAAa;UAAAiK,QAAA,eACZpH,OAAA,CAACtD,MAAM;YAACuL,OAAO,EAAEjE,iBAAkB;YAAAoD,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAInG,UAAU,KAAK,uBAAuB,EAAE;MACjD,oBACErB,OAAA,CAAChD,MAAM;QAACsJ,IAAI,EAAEnF,UAAW;QAACkH,OAAO,EAAErE,iBAAkB;QAACsE,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAnB,QAAA,gBAC3EpH,OAAA,CAAC/C,WAAW;UAAAmK,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACjDxH,OAAA,CAAC9C,aAAa;UAAAkK,QAAA,EACX,CAAC7F,sBAAsB,GACtBd,OAAO,gBACLT,OAAA,CAACjC,gBAAgB;YAAAsJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClB7G,cAAc,CAACuG,MAAM,KAAK,CAAC,gBAC7BlH,OAAA,CAAClC,KAAK;YAACqJ,QAAQ,EAAC,MAAM;YAAAC,QAAA,EAAC;UAAkC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,gBAEjExH,OAAA,CAACvC,IAAI;YAAA2J,QAAA,EACFzG,cAAc,CAACgH,GAAG,CAAEC,IAAI,iBACvB5H,OAAA,CAACtC,QAAQ;cACPsL,MAAM;cAENf,OAAO,EAAEA,CAAA,KAAMzG,yBAAyB,CAACoG,IAAI,CAAE;cAAAR,QAAA,eAE/CpH,OAAA,CAACrC,YAAY;gBACXsL,OAAO,EAAE,OAAOrB,IAAI,CAACxD,iBAAiB,YAAYwD,IAAI,CAACjF,OAAO,EAAG;gBACjEuG,SAAS,EAAE,SAAS,IAAIrB,IAAI,CAACD,IAAI,CAACE,mBAAmB,CAAC,CAACC,kBAAkB,CAAC,CAAC;cAAG;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/E;YAAC,GANGI,IAAI,CAACxD,iBAAiB;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOnB,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACP,gBAEDxH,OAAA,CAACxD,GAAG;YAAA4K,QAAA,gBACFpH,OAAA,CAAClC,KAAK;cAACqJ,QAAQ,EAAC,SAAS;cAACqB,EAAE,EAAE;gBAAE4B,EAAE,EAAE;cAAE,CAAE;cAAAhD,QAAA,GAAC,kDACS,EAAC7F,sBAAsB,CAAC6C,iBAAiB,EAAC,GAC5F;YAAA;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRxH,OAAA,CAACvD,UAAU;cAACkM,OAAO,EAAC,OAAO;cAAAvB,QAAA,EAAC;YAE5B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChBxH,OAAA,CAAC7C,aAAa;UAAAiK,QAAA,gBACZpH,OAAA,CAACtD,MAAM;YAACuL,OAAO,EAAEjE,iBAAkB;YAAAoD,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACnDjG,sBAAsB,iBACrBvB,OAAA,CAACtD,MAAM;YACLuL,OAAO,EAAEhC,2BAA4B;YACrC8C,QAAQ,EAAEtI,OAAQ;YAClB0H,KAAK,EAAC,OAAO;YACbW,SAAS,EAAErI,OAAO,gBAAGT,OAAA,CAACjC,gBAAgB;cAAC2J,IAAI,EAAE;YAAG;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGxH,OAAA,CAAClB,UAAU;cAAAuI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EACtE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAInG,UAAU,KAAK,mBAAmB,EAAE;MAC7C,oBACErB,OAAA,CAAChD,MAAM;QAACsJ,IAAI,EAAEnF,UAAW;QAACkH,OAAO,EAAErE,iBAAkB;QAACsE,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAnB,QAAA,gBAC3EpH,OAAA,CAAC/C,WAAW;UAAAmK,QAAA,EAAC;QAA8B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACzDxH,OAAA,CAAC9C,aAAa;UAAAkK,QAAA,gBACZpH,OAAA,CAACxD,GAAG;YAACgM,EAAE,EAAE;cAAE4B,EAAE,EAAE,CAAC;cAAEH,OAAO,EAAE,MAAM;cAAEC,cAAc,EAAE;YAAW,CAAE;YAAA9C,QAAA,eAC9DpH,OAAA,CAACtD,MAAM;cACLiM,OAAO,EAAC,WAAW;cACnBG,SAAS,eAAE9I,OAAA,CAACtB,OAAO;gBAAA2I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBS,OAAO,EAAEtB,mBAAoB;cAAAS,QAAA,EAC9B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAEL/G,OAAO,gBACNT,OAAA,CAACjC,gBAAgB;YAAAsJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClBzG,SAAS,CAACmG,MAAM,KAAK,CAAC,gBACxBlH,OAAA,CAAClC,KAAK;YAACqJ,QAAQ,EAAC,MAAM;YAAAC,QAAA,EAAC;UAAqC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,gBAEpExH,OAAA,CAAC7B,cAAc;YAACsJ,SAAS,EAAE9K,KAAM;YAAAyK,QAAA,eAC/BpH,OAAA,CAAChC,KAAK;cAAC0J,IAAI,EAAC,OAAO;cAAAN,QAAA,gBACjBpH,OAAA,CAAC5B,SAAS;gBAAAgJ,QAAA,eACRpH,OAAA,CAAC3B,QAAQ;kBAAA+I,QAAA,gBACPpH,OAAA,CAAC9B,SAAS;oBAAAkJ,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC3BxH,OAAA,CAAC9B,SAAS;oBAAAkJ,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC5BxH,OAAA,CAAC9B,SAAS;oBAAAkJ,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC9BxH,OAAA,CAAC9B,SAAS;oBAAAkJ,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC/BxH,OAAA,CAAC9B,SAAS;oBAAAkJ,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eACnCxH,OAAA,CAAC9B,SAAS;oBAAAkJ,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC/BxH,OAAA,CAAC9B,SAAS;oBAAAkJ,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC5BxH,OAAA,CAAC9B,SAAS;oBAAAkJ,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACZxH,OAAA,CAAC/B,SAAS;gBAAAmJ,QAAA,EACPrG,SAAS,CAAC4G,GAAG,CAAElB,SAAS,IAAK;kBAC5B,MAAM4D,QAAQ,GAAG,IAAIxC,IAAI,CAACpB,SAAS,CAACnE,0BAA0B,CAAC;kBAC/D,MAAMgI,IAAI,GAAG,IAAIzC,IAAI,CAAC,CAAC;kBACvB,MAAM0C,MAAM,GAAGC,IAAI,CAACC,IAAI,CAAC,CAACJ,QAAQ,GAAGC,IAAI,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;kBAEnE,IAAII,UAAU,GAAG,SAAS;kBAC1B,IAAIC,UAAU,GAAG,QAAQ;kBAEzB,IAAIJ,MAAM,GAAG,CAAC,EAAE;oBACdG,UAAU,GAAG,OAAO;oBACpBC,UAAU,GAAG,SAAS;kBACxB,CAAC,MAAM,IAAIJ,MAAM,IAAI,EAAE,EAAE;oBACvBG,UAAU,GAAG,SAAS;oBACtBC,UAAU,GAAG,aAAa;kBAC5B;kBAEA,oBACE3K,OAAA,CAAC3B,QAAQ;oBAAA+I,QAAA,gBACPpH,OAAA,CAAC9B,SAAS;sBAAAkJ,QAAA,EAAEX,SAAS,CAACxE;oBAAI;sBAAAoF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACvCxH,OAAA,CAAC9B,SAAS;sBAAAkJ,QAAA,EAAEX,SAAS,CAACvE;oBAAK;sBAAAmF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACxCxH,OAAA,CAAC9B,SAAS;sBAAAkJ,QAAA,EAAEX,SAAS,CAACtE;oBAAO;sBAAAkF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC1CxH,OAAA,CAAC9B,SAAS;sBAAAkJ,QAAA,EAAEX,SAAS,CAACrE;oBAAY;sBAAAiF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC/CxH,OAAA,CAAC9B,SAAS;sBAAAkJ,QAAA,EAAE,IAAIS,IAAI,CAACpB,SAAS,CAACpE,iBAAiB,CAAC,CAAC0F,kBAAkB,CAAC;oBAAC;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACnFxH,OAAA,CAAC9B,SAAS;sBAAAkJ,QAAA,EAAEiD,QAAQ,CAACtC,kBAAkB,CAAC;oBAAC;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACtDxH,OAAA,CAAC9B,SAAS;sBAAAkJ,QAAA,eACRpH,OAAA,CAACxB,IAAI;wBACHkK,KAAK,EAAEiC,UAAW;wBAClBxC,KAAK,EAAEuC,UAAW;wBAClBhD,IAAI,EAAC;sBAAO;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACb;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACO,CAAC,eACZxH,OAAA,CAAC9B,SAAS;sBAAAkJ,QAAA,gBACRpH,OAAA,CAACzB,UAAU;wBACTmJ,IAAI,EAAC,OAAO;wBACZO,OAAO,EAAEA,CAAA,KAAMzB,qBAAqB,CAACC,SAAS,CAAE;wBAAAW,QAAA,eAEhDpH,OAAA,CAACpB,QAAQ;0BAACsJ,QAAQ,EAAC;wBAAO;0BAAAb,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CAAC,eACbxH,OAAA,CAACzB,UAAU;wBACTmJ,IAAI,EAAC,OAAO;wBACZS,KAAK,EAAC,OAAO;wBACbF,OAAO,EAAEA,CAAA,KAAMlB,sBAAsB,CAACN,SAAS,CAAE;wBAAAW,QAAA,eAEjDpH,OAAA,CAAClB,UAAU;0BAACoJ,QAAQ,EAAC;wBAAO;0BAAAb,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA,GA5BCf,SAAS,CAAC7D,YAAY;oBAAAyE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OA6B3B,CAAC;gBAEf,CAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CACjB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChBxH,OAAA,CAAC7C,aAAa;UAAAiK,QAAA,eACZpH,OAAA,CAACtD,MAAM;YAACuL,OAAO,EAAEjE,iBAAkB;YAAAoD,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAInG,UAAU,KAAK,eAAe,IAAIA,UAAU,KAAK,mBAAmB,EAAE;MAC/E,oBACErB,OAAA,CAAChD,MAAM;QAACsJ,IAAI,EAAEnF,UAAW;QAACkH,OAAO,EAAErE,iBAAkB;QAACsE,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAnB,QAAA,gBAC3EpH,OAAA,CAAC/C,WAAW;UAAAmK,QAAA,EACT/F,UAAU,KAAK,eAAe,GAAG,iBAAiB,GAAG;QAAoB;UAAAgG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC,eACdxH,OAAA,CAAC9C,aAAa;UAAAkK,QAAA,eACZpH,OAAA,CAACxD,GAAG;YAACgM,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAArB,QAAA,eACjBpH,OAAA,CAACpD,IAAI;cAAC4M,SAAS;cAACC,OAAO,EAAE,CAAE;cAAArC,QAAA,gBACzBpH,OAAA,CAACpD,IAAI;gBAAC8M,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAAxC,QAAA,eACvBpH,OAAA,CAAC5C,SAAS;kBACRoH,IAAI,EAAC,MAAM;kBACXkE,KAAK,EAAC,gBAAgB;kBACtBH,SAAS;kBACTI,OAAO,EAAC,UAAU;kBAClBlE,KAAK,EAAE1C,iBAAiB,CAACE,IAAK;kBAC9B2G,QAAQ,EAAElC,yBAA0B;kBACpCmD,QAAQ;gBAAA;kBAAAxC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACPxH,OAAA,CAACpD,IAAI;gBAAC8M,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAAxC,QAAA,eACvBpH,OAAA,CAAC5C,SAAS;kBACRoH,IAAI,EAAC,OAAO;kBACZkE,KAAK,EAAC,OAAO;kBACbH,SAAS;kBACTI,OAAO,EAAC,UAAU;kBAClBlE,KAAK,EAAE1C,iBAAiB,CAACG,KAAM;kBAC/B0G,QAAQ,EAAElC,yBAA0B;kBACpCmD,QAAQ;gBAAA;kBAAAxC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACPxH,OAAA,CAACpD,IAAI;gBAAC8M,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAAxC,QAAA,eACvBpH,OAAA,CAAC5C,SAAS;kBACRoH,IAAI,EAAC,SAAS;kBACdkE,KAAK,EAAC,SAAS;kBACfH,SAAS;kBACTI,OAAO,EAAC,UAAU;kBAClBlE,KAAK,EAAE1C,iBAAiB,CAACI,OAAQ;kBACjCyG,QAAQ,EAAElC,yBAA0B;kBACpCmD,QAAQ;gBAAA;kBAAAxC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACPxH,OAAA,CAACpD,IAAI;gBAAC8M,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAAxC,QAAA,eACvBpH,OAAA,CAAC5C,SAAS;kBACRoH,IAAI,EAAC,cAAc;kBACnBkE,KAAK,EAAC,iBAAiB;kBACvBH,SAAS;kBACTI,OAAO,EAAC,UAAU;kBAClBlE,KAAK,EAAE1C,iBAAiB,CAACK,YAAa;kBACtCwG,QAAQ,EAAElC,yBAA0B;kBACpCmD,QAAQ;gBAAA;kBAAAxC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACPxH,OAAA,CAACpD,IAAI;gBAAC8M,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAAxC,QAAA,eACvBpH,OAAA,CAAC5C,SAAS;kBACRoH,IAAI,EAAC,mBAAmB;kBACxBkE,KAAK,EAAC,mBAAmB;kBACzBoB,IAAI,EAAC,MAAM;kBACXvB,SAAS;kBACTI,OAAO,EAAC,UAAU;kBAClBlE,KAAK,EAAE1C,iBAAiB,CAACM,iBAAkB;kBAC3CuG,QAAQ,EAAElC,yBAA0B;kBACpCkE,eAAe,EAAE;oBAAEC,MAAM,EAAE;kBAAK,CAAE;kBAClChB,QAAQ;gBAAA;kBAAAxC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACPxH,OAAA,CAACpD,IAAI;gBAAC8M,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAAxC,QAAA,eACvBpH,OAAA,CAAC5C,SAAS;kBACRoH,IAAI,EAAC,4BAA4B;kBACjCkE,KAAK,EAAC,4BAA4B;kBAClCoB,IAAI,EAAC,MAAM;kBACXvB,SAAS;kBACTI,OAAO,EAAC,UAAU;kBAClBlE,KAAK,EAAE1C,iBAAiB,CAACO,0BAA2B;kBACpDsG,QAAQ,EAAElC,yBAA0B;kBACpCkE,eAAe,EAAE;oBAAEC,MAAM,EAAE;kBAAK,CAAE;kBAClChB,QAAQ;gBAAA;kBAAAxC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACPxH,OAAA,CAACpD,IAAI;gBAAC8M,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAAAvC,QAAA,eAChBpH,OAAA,CAAC5C,SAAS;kBACRoH,IAAI,EAAC,0BAA0B;kBAC/BkE,KAAK,EAAC,mCAAmC;kBACzCH,SAAS;kBACTI,OAAO,EAAC,UAAU;kBAClBlE,KAAK,EAAE1C,iBAAiB,CAACQ,wBAAyB;kBAClDqG,QAAQ,EAAElC;gBAA0B;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACPxH,OAAA,CAACpD,IAAI;gBAAC8M,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAAAvC,QAAA,eAChBpH,OAAA,CAAC5C,SAAS;kBACRoH,IAAI,EAAC,MAAM;kBACXkE,KAAK,EAAC,MAAM;kBACZH,SAAS;kBACTwB,SAAS;kBACTC,IAAI,EAAE,CAAE;kBACRrB,OAAO,EAAC,UAAU;kBAClBlE,KAAK,EAAE1C,iBAAiB,CAACS,IAAK;kBAC9BoG,QAAQ,EAAElC;gBAA0B;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAChBxH,OAAA,CAAC7C,aAAa;UAAAiK,QAAA,gBACZpH,OAAA,CAACtD,MAAM;YAACuL,OAAO,EAAEjE,iBAAkB;YAAAoD,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpDxH,OAAA,CAACtD,MAAM;YACLuL,OAAO,EAAErB,oBAAqB;YAC9BmC,QAAQ,EAAEtI,OAAQ;YAClBqI,SAAS,EAAErI,OAAO,gBAAGT,OAAA,CAACjC,gBAAgB;cAAC2J,IAAI,EAAE;YAAG;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGxH,OAAA,CAACV,QAAQ;cAAA+H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EACpE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb;IAEA,OAAO,IAAI;EACb,CAAC;EAED,oBACExH,OAAA,CAACxD,GAAG;IAAA4K,QAAA,GAEDnG,cAAc,KAAK,0BAA0B,IAAI,CAACE,UAAU,iBAC3DnB,OAAA,CAACxD,GAAG;MAACgM,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAArB,QAAA,gBACjBpH,OAAA,CAACxD,GAAG;QAACgM,EAAE,EAAE;UAAEyB,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEY,UAAU,EAAE,QAAQ;UAAEV,EAAE,EAAE;QAAE,CAAE;QAAAhD,QAAA,gBACzFpH,OAAA,CAACvD,UAAU;UAACkM,OAAO,EAAC,IAAI;UAAAvB,QAAA,GAAC,gBAEvB,EAACzF,UAAU,IAAI,cAAcA,UAAU,EAAE;QAAA;UAAA0F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,EACZ7F,UAAU,iBACT3B,OAAA,CAACtD,MAAM;UACLiM,OAAO,EAAC,UAAU;UAClBG,SAAS,eAAE9I,OAAA,CAACR,SAAS;YAAA6H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBS,OAAO,EAAEA,CAAA,KAAM;YACbrG,aAAa,CAAC,EAAE,CAAC;YACjBqB,kBAAkB,CAAC,EAAE,CAAC;UACxB,CAAE;UAAAmE,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAEL/G,OAAO,gBACNT,OAAA,CAACjC,gBAAgB;QAAAsJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,GAEpBP,yBAAyB,CAAC,CAC3B;IAAA;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,EAEA,CAACvG,cAAc,iBACdjB,OAAA,CAACrD,KAAK;MAAC6L,EAAE,EAAE;QAAEuC,CAAC,EAAE,CAAC;QAAEC,SAAS,EAAE,OAAO;QAAEf,OAAO,EAAE,MAAM;QAAEa,UAAU,EAAE,QAAQ;QAAEZ,cAAc,EAAE;MAAS,CAAE;MAAA9C,QAAA,eACvGpH,OAAA,CAACvD,UAAU;QAACkM,OAAO,EAAC,OAAO;QAAAvB,QAAA,EAAC;MAE5B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACR,EAEAY,YAAY,CAAC,CAAC;EAAA;IAAAf,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEV,CAAC,kCAAC;AAACyD,GAAA,GAllCGhL,kBAAkB;AAolCxB,eAAeA,kBAAkB;AAAC,IAAAE,EAAA,EAAA8K,GAAA;AAAAC,YAAA,CAAA/K,EAAA;AAAA+K,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}