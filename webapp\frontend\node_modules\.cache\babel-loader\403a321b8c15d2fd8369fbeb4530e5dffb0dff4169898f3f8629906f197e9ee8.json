{"ast": null, "code": "import axiosInstance from './axiosConfig';\n\n/**\n * Ser<PERSON>zio per il recupero di dati meteorologici\n */\nconst weatherService = {\n  /**\n   * Ottiene i dati meteorologici per un cantiere specifico\n   * @param {number} cantiereId - ID del cantiere\n   * @returns {Promise<Object>} Dati meteorologici\n   */\n  getWeatherForCantiere: async cantiereId => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/cantieri/${cantiereIdNum}/weather`);\n      return response.data;\n    } catch (error) {\n      console.error('Errore nel recupero dati meteorologici:', error);\n\n      // Restituisci dati demo in caso di errore\n      return {\n        success: false,\n        data: {\n          temperature: 22.5,\n          humidity: 65,\n          pressure: 1013,\n          description: 'Dati non disponibili',\n          city: 'N/D',\n          country: 'IT',\n          timestamp: new Date(),\n          is_demo: true\n        },\n        message: 'Errore nel recupero dati meteorologici, utilizzando valori demo',\n        error: true\n      };\n    }\n  },\n  /**\n   * Formatta i dati meteorologici per la visualizzazione\n   * @param {Object} weatherData - Dati meteorologici grezzi\n   * @returns {Object} Dati formattati\n   */\n  formatWeatherData: weatherData => {\n    if (!weatherData || !weatherData.data) {\n      return {\n        temperature: 'N/D',\n        humidity: 'N/D',\n        displayText: 'Dati meteorologici non disponibili',\n        isDemo: true,\n        success: false\n      };\n    }\n    const data = weatherData.data;\n    const temp = data.temperature ? `${data.temperature}°C` : 'N/D';\n    const humidity = data.humidity ? `${data.humidity}%` : 'N/D';\n    const city = data.city || 'N/D';\n    let displayText = `${temp}, ${humidity}`;\n    if (city !== 'N/D') {\n      displayText += ` (${city})`;\n    }\n\n    // Gestisci diversi tipi di sorgenti dati\n    let statusText = '';\n    if (data.source === 'cantiere_database') {\n      statusText = ' - Dati cantiere';\n    } else if (data.is_demo) {\n      statusText = ' - Dati demo';\n    } else {\n      statusText = ' - Dati live';\n    }\n    displayText += statusText;\n    return {\n      temperature: data.temperature,\n      humidity: data.humidity,\n      pressure: data.pressure,\n      description: data.description,\n      city: data.city,\n      country: data.country,\n      timestamp: data.timestamp,\n      displayText,\n      isDemo: data.is_demo || false,\n      success: weatherData.success || false,\n      source: data.source || 'unknown'\n    };\n  },\n  /**\n   * Ottiene dati meteorologici formattati per un cantiere\n   * @param {number} cantiereId - ID del cantiere\n   * @returns {Promise<Object>} Dati meteorologici formattati\n   */\n  getFormattedWeatherForCantiere: async cantiereId => {\n    try {\n      const rawData = await weatherService.getWeatherForCantiere(cantiereId);\n      return weatherService.formatWeatherData(rawData);\n    } catch (error) {\n      console.error('Errore nel recupero dati meteorologici formattati:', error);\n      return {\n        temperature: 'N/D',\n        humidity: 'N/D',\n        displayText: 'Errore nel recupero dati meteorologici',\n        isDemo: true,\n        success: false\n      };\n    }\n  }\n};\nexport default weatherService;", "map": {"version": 3, "names": ["axiosInstance", "weatherService", "getWeatherForCantiere", "cantiereId", "cantiereIdNum", "parseInt", "isNaN", "Error", "response", "get", "data", "error", "console", "success", "temperature", "humidity", "pressure", "description", "city", "country", "timestamp", "Date", "is_demo", "message", "formatWeatherData", "weatherData", "displayText", "isDemo", "temp", "statusText", "source", "getFormattedWeatherForCantiere", "rawData"], "sources": ["C:/CMS/webapp/frontend/src/services/weatherService.js"], "sourcesContent": ["import axiosInstance from './axiosConfig';\n\n/**\n * Servizio per il recupero di dati meteorologici\n */\nconst weatherService = {\n  /**\n   * Ottiene i dati meteorologici per un cantiere specifico\n   * @param {number} cantiereId - ID del cantiere\n   * @returns {Promise<Object>} Dati meteorologici\n   */\n  getWeatherForCantiere: async (cantiereId) => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.get(`/cantieri/${cantiereIdNum}/weather`);\n      return response.data;\n    } catch (error) {\n      console.error('Errore nel recupero dati meteorologici:', error);\n      \n      // Restituisci dati demo in caso di errore\n      return {\n        success: false,\n        data: {\n          temperature: 22.5,\n          humidity: 65,\n          pressure: 1013,\n          description: 'Dati non disponibili',\n          city: 'N/D',\n          country: 'IT',\n          timestamp: new Date(),\n          is_demo: true\n        },\n        message: 'Errore nel recupero dati meteorologici, utilizzando valori demo',\n        error: true\n      };\n    }\n  },\n\n  /**\n   * Formatta i dati meteorologici per la visualizzazione\n   * @param {Object} weatherData - Dati meteorologici grezzi\n   * @returns {Object} Dati formattati\n   */\n  formatWeatherData: (weatherData) => {\n    if (!weatherData || !weatherData.data) {\n      return {\n        temperature: 'N/D',\n        humidity: 'N/D',\n        displayText: 'Dati meteorologici non disponibili',\n        isDemo: true,\n        success: false\n      };\n    }\n\n    const data = weatherData.data;\n    const temp = data.temperature ? `${data.temperature}°C` : 'N/D';\n    const humidity = data.humidity ? `${data.humidity}%` : 'N/D';\n    const city = data.city || 'N/D';\n\n    let displayText = `${temp}, ${humidity}`;\n    if (city !== 'N/D') {\n      displayText += ` (${city})`;\n    }\n\n    // Gestisci diversi tipi di sorgenti dati\n    let statusText = '';\n    if (data.source === 'cantiere_database') {\n      statusText = ' - Dati cantiere';\n    } else if (data.is_demo) {\n      statusText = ' - Dati demo';\n    } else {\n      statusText = ' - Dati live';\n    }\n\n    displayText += statusText;\n\n    return {\n      temperature: data.temperature,\n      humidity: data.humidity,\n      pressure: data.pressure,\n      description: data.description,\n      city: data.city,\n      country: data.country,\n      timestamp: data.timestamp,\n      displayText,\n      isDemo: data.is_demo || false,\n      success: weatherData.success || false,\n      source: data.source || 'unknown'\n    };\n  },\n\n  /**\n   * Ottiene dati meteorologici formattati per un cantiere\n   * @param {number} cantiereId - ID del cantiere\n   * @returns {Promise<Object>} Dati meteorologici formattati\n   */\n  getFormattedWeatherForCantiere: async (cantiereId) => {\n    try {\n      const rawData = await weatherService.getWeatherForCantiere(cantiereId);\n      return weatherService.formatWeatherData(rawData);\n    } catch (error) {\n      console.error('Errore nel recupero dati meteorologici formattati:', error);\n      return {\n        temperature: 'N/D',\n        humidity: 'N/D',\n        displayText: 'Errore nel recupero dati meteorologici',\n        isDemo: true,\n        success: false\n      };\n    }\n  }\n};\n\nexport default weatherService;\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,eAAe;;AAEzC;AACA;AACA;AACA,MAAMC,cAAc,GAAG;EACrB;AACF;AACA;AACA;AACA;EACEC,qBAAqB,EAAE,MAAOC,UAAU,IAAK;IAC3C,IAAI;MACF,MAAMC,aAAa,GAAGC,QAAQ,CAACF,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIG,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BJ,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMK,QAAQ,GAAG,MAAMR,aAAa,CAACS,GAAG,CAAC,aAAaL,aAAa,UAAU,CAAC;MAC9E,OAAOI,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;;MAE/D;MACA,OAAO;QACLE,OAAO,EAAE,KAAK;QACdH,IAAI,EAAE;UACJI,WAAW,EAAE,IAAI;UACjBC,QAAQ,EAAE,EAAE;UACZC,QAAQ,EAAE,IAAI;UACdC,WAAW,EAAE,sBAAsB;UACnCC,IAAI,EAAE,KAAK;UACXC,OAAO,EAAE,IAAI;UACbC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;UACrBC,OAAO,EAAE;QACX,CAAC;QACDC,OAAO,EAAE,iEAAiE;QAC1EZ,KAAK,EAAE;MACT,CAAC;IACH;EACF,CAAC;EAED;AACF;AACA;AACA;AACA;EACEa,iBAAiB,EAAGC,WAAW,IAAK;IAClC,IAAI,CAACA,WAAW,IAAI,CAACA,WAAW,CAACf,IAAI,EAAE;MACrC,OAAO;QACLI,WAAW,EAAE,KAAK;QAClBC,QAAQ,EAAE,KAAK;QACfW,WAAW,EAAE,oCAAoC;QACjDC,MAAM,EAAE,IAAI;QACZd,OAAO,EAAE;MACX,CAAC;IACH;IAEA,MAAMH,IAAI,GAAGe,WAAW,CAACf,IAAI;IAC7B,MAAMkB,IAAI,GAAGlB,IAAI,CAACI,WAAW,GAAG,GAAGJ,IAAI,CAACI,WAAW,IAAI,GAAG,KAAK;IAC/D,MAAMC,QAAQ,GAAGL,IAAI,CAACK,QAAQ,GAAG,GAAGL,IAAI,CAACK,QAAQ,GAAG,GAAG,KAAK;IAC5D,MAAMG,IAAI,GAAGR,IAAI,CAACQ,IAAI,IAAI,KAAK;IAE/B,IAAIQ,WAAW,GAAG,GAAGE,IAAI,KAAKb,QAAQ,EAAE;IACxC,IAAIG,IAAI,KAAK,KAAK,EAAE;MAClBQ,WAAW,IAAI,KAAKR,IAAI,GAAG;IAC7B;;IAEA;IACA,IAAIW,UAAU,GAAG,EAAE;IACnB,IAAInB,IAAI,CAACoB,MAAM,KAAK,mBAAmB,EAAE;MACvCD,UAAU,GAAG,kBAAkB;IACjC,CAAC,MAAM,IAAInB,IAAI,CAACY,OAAO,EAAE;MACvBO,UAAU,GAAG,cAAc;IAC7B,CAAC,MAAM;MACLA,UAAU,GAAG,cAAc;IAC7B;IAEAH,WAAW,IAAIG,UAAU;IAEzB,OAAO;MACLf,WAAW,EAAEJ,IAAI,CAACI,WAAW;MAC7BC,QAAQ,EAAEL,IAAI,CAACK,QAAQ;MACvBC,QAAQ,EAAEN,IAAI,CAACM,QAAQ;MACvBC,WAAW,EAAEP,IAAI,CAACO,WAAW;MAC7BC,IAAI,EAAER,IAAI,CAACQ,IAAI;MACfC,OAAO,EAAET,IAAI,CAACS,OAAO;MACrBC,SAAS,EAAEV,IAAI,CAACU,SAAS;MACzBM,WAAW;MACXC,MAAM,EAAEjB,IAAI,CAACY,OAAO,IAAI,KAAK;MAC7BT,OAAO,EAAEY,WAAW,CAACZ,OAAO,IAAI,KAAK;MACrCiB,MAAM,EAAEpB,IAAI,CAACoB,MAAM,IAAI;IACzB,CAAC;EACH,CAAC;EAED;AACF;AACA;AACA;AACA;EACEC,8BAA8B,EAAE,MAAO5B,UAAU,IAAK;IACpD,IAAI;MACF,MAAM6B,OAAO,GAAG,MAAM/B,cAAc,CAACC,qBAAqB,CAACC,UAAU,CAAC;MACtE,OAAOF,cAAc,CAACuB,iBAAiB,CAACQ,OAAO,CAAC;IAClD,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oDAAoD,EAAEA,KAAK,CAAC;MAC1E,OAAO;QACLG,WAAW,EAAE,KAAK;QAClBC,QAAQ,EAAE,KAAK;QACfW,WAAW,EAAE,wCAAwC;QACrDC,MAAM,EAAE,IAAI;QACZd,OAAO,EAAE;MACX,CAAC;IACH;EACF;AACF,CAAC;AAED,eAAeZ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}