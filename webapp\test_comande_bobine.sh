#!/bin/bash

# Test script per verificare il caricamento delle bobine nelle comande
echo "🚀 Test sistema comande-bobine..."

API_BASE="http://localhost:8001/api"

# 1. Autenticazione
echo "1. Autenticazione..."
TOKEN_RESPONSE=$(curl -s -X POST "$API_BASE/auth/login" \
  -H "Content-Type: application/json" \
  -d "{\"username\":\"a\",\"password\":\"a\"}")

if [[ $? -ne 0 ]]; then
  echo "❌ Errore nella connessione al server"
  exit 1
fi

echo "Risposta autenticazione: $TOKEN_RESPONSE"

# Estrai il token (assumendo formato JSON)
TOKEN=$(echo "$TOKEN_RESPONSE" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)

if [[ -z "$TOKEN" ]]; then
  echo "❌ Errore nell'estrazione del token"
  echo "Risposta completa: $TOKEN_RESPONSE"
  exit 1
fi

echo "✅ Token ottenuto: ${TOKEN:0:20}..."

# 2. Test caricamento bobine disponibili
echo ""
echo "2. Test caricamento bobine disponibili..."
BOBINE_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" \
  "$API_BASE/parco-cavi/1?disponibili_only=true")

echo "Risposta bobine: $BOBINE_RESPONSE"

# 3. Test caricamento comande
echo ""
echo "3. Test caricamento comande..."
COMANDE_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" \
  "$API_BASE/comande/cantiere/1")

echo "Risposta comande: $COMANDE_RESPONSE"

# 4. Test caricamento cavi di una comanda specifica (se esiste)
echo ""
echo "4. Test caricamento cavi comanda POS007..."
CAVI_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" \
  "$API_BASE/comande/POS007/cavi")

echo "Risposta cavi comanda: $CAVI_RESPONSE"

echo ""
echo "✅ Test completato!"
