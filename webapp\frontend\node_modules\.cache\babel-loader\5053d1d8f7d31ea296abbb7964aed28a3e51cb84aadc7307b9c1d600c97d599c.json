{"ast": null, "code": "import config from '../config';\nimport axiosInstance from './axiosConfig';\nconst certificazioneService = {\n  // Ottiene la lista delle certificazioni di un cantiere\n  getCertificazioni: async (cantiereId, filtroCavo = '') => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      let url = `/cantieri/${cantiereIdNum}/certificazioni`;\n      if (filtroCavo) {\n        url += `?filtro_cavo=${filtroCavo}`;\n      }\n      const response = await axiosInstance.get(url);\n      return response.data;\n    } catch (error) {\n      console.error('Get certificazioni error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Crea una nuova certificazione\n  createCertificazione: async (cantiereId, certificazioneData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.post(`/cantieri/${cantiereIdNum}/certificazioni`, certificazioneData);\n      return response.data;\n    } catch (error) {\n      console.error('Create certificazione error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene i dettagli di una certificazione\n  getCertificazione: async (cantiereId, idCertificazione) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/cantieri/${cantiereIdNum}/certificazioni/${idCertificazione}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get certificazione error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Aggiorna una certificazione\n  updateCertificazione: async (cantiereId, idCertificazione, certificazioneData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.put(`/cantieri/${cantiereIdNum}/certificazioni/${idCertificazione}`, certificazioneData);\n      return response.data;\n    } catch (error) {\n      console.error('Update certificazione error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Elimina una certificazione\n  deleteCertificazione: async (cantiereId, idCertificazione) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.delete(`/cantieri/${cantiereIdNum}/certificazioni/${idCertificazione}`);\n      return response.data;\n    } catch (error) {\n      console.error('Delete certificazione error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Genera PDF di una certificazione\n  generatePdf: async (cantiereId, idCertificazione) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      console.log(`🔍 DEBUG PDF - Richiesta PDF per cantiere ${cantiereIdNum}, certificazione ${idCertificazione}`);\n      const response = await axiosInstance.get(`/cantieri/${cantiereIdNum}/certificazioni/${idCertificazione}/pdf`, {\n        responseType: 'blob'\n      });\n      console.log(`🔍 DEBUG PDF - Risposta ricevuta:`, {\n        status: response.status,\n        statusText: response.statusText,\n        headers: response.headers,\n        dataType: typeof response.data,\n        dataSize: response.data.size\n      });\n\n      // Verifica che la risposta sia un blob valido\n      if (!response.data || response.data.size === 0) {\n        throw new Error('PDF vuoto o non valido ricevuto dal server');\n      }\n\n      // Crea un URL per il blob e scarica il file\n      const blob = new Blob([response.data], {\n        type: 'application/pdf'\n      });\n      console.log(`🔍 DEBUG PDF - Blob creato:`, {\n        size: blob.size,\n        type: blob.type\n      });\n      const url = window.URL.createObjectURL(blob);\n      console.log(`🔍 DEBUG PDF - URL creato:`, url);\n\n      // Crea un link temporaneo per il download\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = `certificato_${idCertificazione}.pdf`;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n\n      // Pulisci l'URL\n      window.URL.revokeObjectURL(url);\n      console.log(`✅ DEBUG PDF - Download completato con successo`);\n      return {\n        success: true,\n        message: 'PDF scaricato con successo'\n      };\n    } catch (error) {\n      var _error$response, _error$response2;\n      console.error('❌ Generate PDF error:', error);\n      console.error('❌ Error details:', {\n        message: error.message,\n        response: error.response,\n        status: (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status,\n        data: (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.data\n      });\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene la lista degli strumenti certificati\n  getStrumenti: async cantiereId => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/cantieri/${cantiereIdNum}/strumenti`);\n      return response.data;\n    } catch (error) {\n      console.error('Get strumenti error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Crea un nuovo strumento certificato\n  createStrumento: async (cantiereId, strumentoData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.post(`/cantieri/${cantiereIdNum}/strumenti`, strumentoData);\n      return response.data;\n    } catch (error) {\n      console.error('Create strumento error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Aggiorna uno strumento certificato\n  updateStrumento: async (cantiereId, idStrumento, strumentoData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.put(`/cantieri/${cantiereIdNum}/strumenti/${idStrumento}`, strumentoData);\n      return response.data;\n    } catch (error) {\n      console.error('Update strumento error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Elimina uno strumento certificato\n  deleteStrumento: async (cantiereId, idStrumento) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.delete(`/cantieri/${cantiereIdNum}/strumenti/${idStrumento}`);\n      return response.data;\n    } catch (error) {\n      console.error('Delete strumento error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Funzioni avanzate per il nuovo sistema\n\n  // Ottiene statistiche delle certificazioni\n  getStatistiche: async cantiereId => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/cantieri/${cantiereIdNum}/certificazioni/statistiche`);\n      return response.data;\n    } catch (error) {\n      console.error('Get statistiche error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Esporta certificazioni in formato CSV\n  exportCertificazioni: async (cantiereId, filtri = {}) => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const params = new URLSearchParams(filtri);\n      const response = await axiosInstance.get(`/cantieri/${cantiereIdNum}/certificazioni/export?${params}`, {\n        responseType: 'blob'\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Export certificazioni error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Genera report completo delle certificazioni\n  generateReport: async (cantiereId, tipoReport = 'completo') => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/cantieri/${cantiereIdNum}/certificazioni/report/${tipoReport}`);\n      return response.data;\n    } catch (error) {\n      console.error('Generate report error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Operazioni bulk per certificazioni\n  bulkDelete: async (cantiereId, idCertificazioni) => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.post(`/cantieri/${cantiereIdNum}/certificazioni/bulk-delete`, {\n        ids: idCertificazioni\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Bulk delete error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Genera PDF multipli\n  generateBulkPdf: async (cantiereId, idCertificazioni) => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.post(`/cantieri/${cantiereIdNum}/certificazioni/bulk-pdf`, {\n        ids: idCertificazioni\n      }, {\n        responseType: 'blob'\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Bulk PDF error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Valida dati certificazione\n  validateCertificazione: async (cantiereId, certificazioneData) => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.post(`/cantieri/${cantiereIdNum}/certificazioni/validate`, certificazioneData);\n      return response.data;\n    } catch (error) {\n      console.error('Validate certificazione error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\nexport default certificazioneService;", "map": {"version": 3, "names": ["config", "axiosInstance", "certificazioneService", "getCertificazioni", "cantiereId", "filtroCavo", "cantiereIdNum", "parseInt", "isNaN", "Error", "url", "response", "get", "data", "error", "console", "createCertificazione", "certificazioneData", "post", "getCertificazione", "idCertificazione", "updateCertificazione", "put", "deleteCertificazione", "delete", "generatePdf", "log", "responseType", "status", "statusText", "headers", "dataType", "dataSize", "size", "blob", "Blob", "type", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "success", "message", "_error$response", "_error$response2", "getStrumenti", "createStrumento", "strumentoData", "updateStrumento", "idStrumento", "deleteStrumento", "getStatistiche", "exportCertificazioni", "filtri", "params", "URLSearchParams", "generateReport", "tipoReport", "bulkDelete", "idCertificazioni", "ids", "generateBulkPdf", "validateCertificazione"], "sources": ["C:/CMS/webapp/frontend/src/services/certificazioneService.js"], "sourcesContent": ["import config from '../config';\r\nimport axiosInstance from './axiosConfig';\r\n\r\nconst certificazioneService = {\r\n  // Ottiene la lista delle certificazioni di un cantiere\r\n  getCertificazioni: async (cantiereId, filtroCavo = '') => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      let url = `/cantieri/${cantiereIdNum}/certificazioni`;\r\n      if (filtroCavo) {\r\n        url += `?filtro_cavo=${filtroCavo}`;\r\n      }\r\n      const response = await axiosInstance.get(url);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Get certificazioni error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Crea una nuova certificazione\r\n  createCertificazione: async (cantiereId, certificazioneData) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.post(`/cantieri/${cantiereIdNum}/certificazioni`, certificazioneData);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Create certificazione error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Ottiene i dettagli di una certificazione\r\n  getCertificazione: async (cantiereId, idCertificazione) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.get(`/cantieri/${cantiereIdNum}/certificazioni/${idCertificazione}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Get certificazione error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Aggiorna una certificazione\r\n  updateCertificazione: async (cantiereId, idCertificazione, certificazioneData) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.put(`/cantieri/${cantiereIdNum}/certificazioni/${idCertificazione}`, certificazioneData);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Update certificazione error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Elimina una certificazione\r\n  deleteCertificazione: async (cantiereId, idCertificazione) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.delete(`/cantieri/${cantiereIdNum}/certificazioni/${idCertificazione}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Delete certificazione error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Genera PDF di una certificazione\r\n  generatePdf: async (cantiereId, idCertificazione) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      console.log(`🔍 DEBUG PDF - Richiesta PDF per cantiere ${cantiereIdNum}, certificazione ${idCertificazione}`);\r\n\r\n      const response = await axiosInstance.get(`/cantieri/${cantiereIdNum}/certificazioni/${idCertificazione}/pdf`, {\r\n        responseType: 'blob'\r\n      });\r\n\r\n      console.log(`🔍 DEBUG PDF - Risposta ricevuta:`, {\r\n        status: response.status,\r\n        statusText: response.statusText,\r\n        headers: response.headers,\r\n        dataType: typeof response.data,\r\n        dataSize: response.data.size\r\n      });\r\n\r\n      // Verifica che la risposta sia un blob valido\r\n      if (!response.data || response.data.size === 0) {\r\n        throw new Error('PDF vuoto o non valido ricevuto dal server');\r\n      }\r\n\r\n      // Crea un URL per il blob e scarica il file\r\n      const blob = new Blob([response.data], { type: 'application/pdf' });\r\n      console.log(`🔍 DEBUG PDF - Blob creato:`, {\r\n        size: blob.size,\r\n        type: blob.type\r\n      });\r\n\r\n      const url = window.URL.createObjectURL(blob);\r\n      console.log(`🔍 DEBUG PDF - URL creato:`, url);\r\n\r\n      // Crea un link temporaneo per il download\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = `certificato_${idCertificazione}.pdf`;\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n\r\n      // Pulisci l'URL\r\n      window.URL.revokeObjectURL(url);\r\n\r\n      console.log(`✅ DEBUG PDF - Download completato con successo`);\r\n      return { success: true, message: 'PDF scaricato con successo' };\r\n    } catch (error) {\r\n      console.error('❌ Generate PDF error:', error);\r\n      console.error('❌ Error details:', {\r\n        message: error.message,\r\n        response: error.response,\r\n        status: error.response?.status,\r\n        data: error.response?.data\r\n      });\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Ottiene la lista degli strumenti certificati\r\n  getStrumenti: async (cantiereId) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.get(`/cantieri/${cantiereIdNum}/strumenti`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Get strumenti error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Crea un nuovo strumento certificato\r\n  createStrumento: async (cantiereId, strumentoData) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.post(`/cantieri/${cantiereIdNum}/strumenti`, strumentoData);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Create strumento error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Aggiorna uno strumento certificato\r\n  updateStrumento: async (cantiereId, idStrumento, strumentoData) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.put(`/cantieri/${cantiereIdNum}/strumenti/${idStrumento}`, strumentoData);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Update strumento error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Elimina uno strumento certificato\r\n  deleteStrumento: async (cantiereId, idStrumento) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.delete(`/cantieri/${cantiereIdNum}/strumenti/${idStrumento}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Delete strumento error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Funzioni avanzate per il nuovo sistema\r\n\r\n  // Ottiene statistiche delle certificazioni\r\n  getStatistiche: async (cantiereId) => {\r\n    try {\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.get(`/cantieri/${cantiereIdNum}/certificazioni/statistiche`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Get statistiche error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Esporta certificazioni in formato CSV\r\n  exportCertificazioni: async (cantiereId, filtri = {}) => {\r\n    try {\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const params = new URLSearchParams(filtri);\r\n      const response = await axiosInstance.get(`/cantieri/${cantiereIdNum}/certificazioni/export?${params}`, {\r\n        responseType: 'blob'\r\n      });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Export certificazioni error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Genera report completo delle certificazioni\r\n  generateReport: async (cantiereId, tipoReport = 'completo') => {\r\n    try {\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.get(`/cantieri/${cantiereIdNum}/certificazioni/report/${tipoReport}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Generate report error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Operazioni bulk per certificazioni\r\n  bulkDelete: async (cantiereId, idCertificazioni) => {\r\n    try {\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.post(`/cantieri/${cantiereIdNum}/certificazioni/bulk-delete`, {\r\n        ids: idCertificazioni\r\n      });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Bulk delete error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Genera PDF multipli\r\n  generateBulkPdf: async (cantiereId, idCertificazioni) => {\r\n    try {\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.post(`/cantieri/${cantiereIdNum}/certificazioni/bulk-pdf`, {\r\n        ids: idCertificazioni\r\n      }, {\r\n        responseType: 'blob'\r\n      });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Bulk PDF error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Valida dati certificazione\r\n  validateCertificazione: async (cantiereId, certificazioneData) => {\r\n    try {\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.post(`/cantieri/${cantiereIdNum}/certificazioni/validate`, certificazioneData);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Validate certificazione error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  }\r\n};\r\n\r\nexport default certificazioneService;\r\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,WAAW;AAC9B,OAAOC,aAAa,MAAM,eAAe;AAEzC,MAAMC,qBAAqB,GAAG;EAC5B;EACAC,iBAAiB,EAAE,MAAAA,CAAOC,UAAU,EAAEC,UAAU,GAAG,EAAE,KAAK;IACxD,IAAI;MACF;MACA,MAAMC,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,IAAIM,GAAG,GAAG,aAAaJ,aAAa,iBAAiB;MACrD,IAAID,UAAU,EAAE;QACdK,GAAG,IAAI,gBAAgBL,UAAU,EAAE;MACrC;MACA,MAAMM,QAAQ,GAAG,MAAMV,aAAa,CAACW,GAAG,CAACF,GAAG,CAAC;MAC7C,OAAOC,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAE,oBAAoB,EAAE,MAAAA,CAAOZ,UAAU,EAAEa,kBAAkB,KAAK;IAC9D,IAAI;MACF;MACA,MAAMX,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMO,QAAQ,GAAG,MAAMV,aAAa,CAACiB,IAAI,CAAC,aAAaZ,aAAa,iBAAiB,EAAEW,kBAAkB,CAAC;MAC1G,OAAON,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAK,iBAAiB,EAAE,MAAAA,CAAOf,UAAU,EAAEgB,gBAAgB,KAAK;IACzD,IAAI;MACF;MACA,MAAMd,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMO,QAAQ,GAAG,MAAMV,aAAa,CAACW,GAAG,CAAC,aAAaN,aAAa,mBAAmBc,gBAAgB,EAAE,CAAC;MACzG,OAAOT,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAO,oBAAoB,EAAE,MAAAA,CAAOjB,UAAU,EAAEgB,gBAAgB,EAAEH,kBAAkB,KAAK;IAChF,IAAI;MACF;MACA,MAAMX,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMO,QAAQ,GAAG,MAAMV,aAAa,CAACqB,GAAG,CAAC,aAAahB,aAAa,mBAAmBc,gBAAgB,EAAE,EAAEH,kBAAkB,CAAC;MAC7H,OAAON,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAS,oBAAoB,EAAE,MAAAA,CAAOnB,UAAU,EAAEgB,gBAAgB,KAAK;IAC5D,IAAI;MACF;MACA,MAAMd,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMO,QAAQ,GAAG,MAAMV,aAAa,CAACuB,MAAM,CAAC,aAAalB,aAAa,mBAAmBc,gBAAgB,EAAE,CAAC;MAC5G,OAAOT,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAW,WAAW,EAAE,MAAAA,CAAOrB,UAAU,EAAEgB,gBAAgB,KAAK;IACnD,IAAI;MACF;MACA,MAAMd,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEAW,OAAO,CAACW,GAAG,CAAC,6CAA6CpB,aAAa,oBAAoBc,gBAAgB,EAAE,CAAC;MAE7G,MAAMT,QAAQ,GAAG,MAAMV,aAAa,CAACW,GAAG,CAAC,aAAaN,aAAa,mBAAmBc,gBAAgB,MAAM,EAAE;QAC5GO,YAAY,EAAE;MAChB,CAAC,CAAC;MAEFZ,OAAO,CAACW,GAAG,CAAC,mCAAmC,EAAE;QAC/CE,MAAM,EAAEjB,QAAQ,CAACiB,MAAM;QACvBC,UAAU,EAAElB,QAAQ,CAACkB,UAAU;QAC/BC,OAAO,EAAEnB,QAAQ,CAACmB,OAAO;QACzBC,QAAQ,EAAE,OAAOpB,QAAQ,CAACE,IAAI;QAC9BmB,QAAQ,EAAErB,QAAQ,CAACE,IAAI,CAACoB;MAC1B,CAAC,CAAC;;MAEF;MACA,IAAI,CAACtB,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACoB,IAAI,KAAK,CAAC,EAAE;QAC9C,MAAM,IAAIxB,KAAK,CAAC,4CAA4C,CAAC;MAC/D;;MAEA;MACA,MAAMyB,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACxB,QAAQ,CAACE,IAAI,CAAC,EAAE;QAAEuB,IAAI,EAAE;MAAkB,CAAC,CAAC;MACnErB,OAAO,CAACW,GAAG,CAAC,6BAA6B,EAAE;QACzCO,IAAI,EAAEC,IAAI,CAACD,IAAI;QACfG,IAAI,EAAEF,IAAI,CAACE;MACb,CAAC,CAAC;MAEF,MAAM1B,GAAG,GAAG2B,MAAM,CAACC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;MAC5CnB,OAAO,CAACW,GAAG,CAAC,4BAA4B,EAAEhB,GAAG,CAAC;;MAE9C;MACA,MAAM8B,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGjC,GAAG;MACf8B,IAAI,CAACI,QAAQ,GAAG,eAAexB,gBAAgB,MAAM;MACrDqB,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;MACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;;MAE/B;MACAH,MAAM,CAACC,GAAG,CAACW,eAAe,CAACvC,GAAG,CAAC;MAE/BK,OAAO,CAACW,GAAG,CAAC,gDAAgD,CAAC;MAC7D,OAAO;QAAEwB,OAAO,EAAE,IAAI;QAAEC,OAAO,EAAE;MAA6B,CAAC;IACjE,CAAC,CAAC,OAAOrC,KAAK,EAAE;MAAA,IAAAsC,eAAA,EAAAC,gBAAA;MACdtC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CC,OAAO,CAACD,KAAK,CAAC,kBAAkB,EAAE;QAChCqC,OAAO,EAAErC,KAAK,CAACqC,OAAO;QACtBxC,QAAQ,EAAEG,KAAK,CAACH,QAAQ;QACxBiB,MAAM,GAAAwB,eAAA,GAAEtC,KAAK,CAACH,QAAQ,cAAAyC,eAAA,uBAAdA,eAAA,CAAgBxB,MAAM;QAC9Bf,IAAI,GAAAwC,gBAAA,GAAEvC,KAAK,CAACH,QAAQ,cAAA0C,gBAAA,uBAAdA,gBAAA,CAAgBxC;MACxB,CAAC,CAAC;MACF,MAAMC,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAwC,YAAY,EAAE,MAAOlD,UAAU,IAAK;IAClC,IAAI;MACF;MACA,MAAME,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMO,QAAQ,GAAG,MAAMV,aAAa,CAACW,GAAG,CAAC,aAAaN,aAAa,YAAY,CAAC;MAChF,OAAOK,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAyC,eAAe,EAAE,MAAAA,CAAOnD,UAAU,EAAEoD,aAAa,KAAK;IACpD,IAAI;MACF;MACA,MAAMlD,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMO,QAAQ,GAAG,MAAMV,aAAa,CAACiB,IAAI,CAAC,aAAaZ,aAAa,YAAY,EAAEkD,aAAa,CAAC;MAChG,OAAO7C,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACA2C,eAAe,EAAE,MAAAA,CAAOrD,UAAU,EAAEsD,WAAW,EAAEF,aAAa,KAAK;IACjE,IAAI;MACF;MACA,MAAMlD,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMO,QAAQ,GAAG,MAAMV,aAAa,CAACqB,GAAG,CAAC,aAAahB,aAAa,cAAcoD,WAAW,EAAE,EAAEF,aAAa,CAAC;MAC9G,OAAO7C,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACA6C,eAAe,EAAE,MAAAA,CAAOvD,UAAU,EAAEsD,WAAW,KAAK;IAClD,IAAI;MACF;MACA,MAAMpD,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMO,QAAQ,GAAG,MAAMV,aAAa,CAACuB,MAAM,CAAC,aAAalB,aAAa,cAAcoD,WAAW,EAAE,CAAC;MAClG,OAAO/C,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;;EAEA;EACA8C,cAAc,EAAE,MAAOxD,UAAU,IAAK;IACpC,IAAI;MACF,MAAME,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMO,QAAQ,GAAG,MAAMV,aAAa,CAACW,GAAG,CAAC,aAAaN,aAAa,6BAA6B,CAAC;MACjG,OAAOK,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACA+C,oBAAoB,EAAE,MAAAA,CAAOzD,UAAU,EAAE0D,MAAM,GAAG,CAAC,CAAC,KAAK;IACvD,IAAI;MACF,MAAMxD,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAM2D,MAAM,GAAG,IAAIC,eAAe,CAACF,MAAM,CAAC;MAC1C,MAAMnD,QAAQ,GAAG,MAAMV,aAAa,CAACW,GAAG,CAAC,aAAaN,aAAa,0BAA0ByD,MAAM,EAAE,EAAE;QACrGpC,YAAY,EAAE;MAChB,CAAC,CAAC;MACF,OAAOhB,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAmD,cAAc,EAAE,MAAAA,CAAO7D,UAAU,EAAE8D,UAAU,GAAG,UAAU,KAAK;IAC7D,IAAI;MACF,MAAM5D,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMO,QAAQ,GAAG,MAAMV,aAAa,CAACW,GAAG,CAAC,aAAaN,aAAa,0BAA0B4D,UAAU,EAAE,CAAC;MAC1G,OAAOvD,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAqD,UAAU,EAAE,MAAAA,CAAO/D,UAAU,EAAEgE,gBAAgB,KAAK;IAClD,IAAI;MACF,MAAM9D,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMO,QAAQ,GAAG,MAAMV,aAAa,CAACiB,IAAI,CAAC,aAAaZ,aAAa,6BAA6B,EAAE;QACjG+D,GAAG,EAAED;MACP,CAAC,CAAC;MACF,OAAOzD,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAwD,eAAe,EAAE,MAAAA,CAAOlE,UAAU,EAAEgE,gBAAgB,KAAK;IACvD,IAAI;MACF,MAAM9D,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMO,QAAQ,GAAG,MAAMV,aAAa,CAACiB,IAAI,CAAC,aAAaZ,aAAa,0BAA0B,EAAE;QAC9F+D,GAAG,EAAED;MACP,CAAC,EAAE;QACDzC,YAAY,EAAE;MAChB,CAAC,CAAC;MACF,OAAOhB,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvC,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAyD,sBAAsB,EAAE,MAAAA,CAAOnE,UAAU,EAAEa,kBAAkB,KAAK;IAChE,IAAI;MACF,MAAMX,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMO,QAAQ,GAAG,MAAMV,aAAa,CAACiB,IAAI,CAAC,aAAaZ,aAAa,0BAA0B,EAAEW,kBAAkB,CAAC;MACnH,OAAON,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF;AACF,CAAC;AAED,eAAeZ,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}