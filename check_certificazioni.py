#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script per verificare le certificazioni esistenti nel database
"""

import sys
import os
import psycopg2
from datetime import datetime

# Configurazione database
DB_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'database': 'cantieri',
    'user': 'postgres',
    'password': 'Taranto'
}

def check_certificazioni():
    """Verifica le certificazioni esistenti nel database."""
    try:
        # Connessione al database
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        print("🔍 Verifica certificazioni esistenti nel database...")
        print("=" * 60)
        
        # Verifica se la tabella esiste
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'certificazionicavi'
            );
        """)
        table_exists = cursor.fetchone()[0]
        
        if not table_exists:
            print("❌ Tabella 'certificazionicavi' non trovata!")
            return
        
        print("✅ Tabella 'certificazionicavi' trovata")
        
        # Conta tutte le certificazioni
        cursor.execute("SELECT COUNT(*) FROM certificazionicavi;")
        total_count = cursor.fetchone()[0]
        print(f"📊 Totale certificazioni: {total_count}")
        
        if total_count == 0:
            print("ℹ️ Nessuna certificazione trovata nel database")
            return
        
        # Mostra le certificazioni per cantiere
        cursor.execute("""
            SELECT id_cantiere, COUNT(*) as count
            FROM certificazionicavi 
            GROUP BY id_cantiere 
            ORDER BY id_cantiere;
        """)
        
        print("\n📋 Certificazioni per cantiere:")
        for row in cursor.fetchall():
            cantiere_id, count = row
            print(f"  Cantiere {cantiere_id}: {count} certificazioni")
        
        # Mostra le prime 10 certificazioni con dettagli
        cursor.execute("""
            SELECT c.id_certificazione, c.id_cantiere, c.id_cavo, c.numero_certificato, 
                   c.data_certificazione, c.id_operatore, c.valore_isolamento
            FROM certificazionicavi c
            ORDER BY c.id_certificazione DESC
            LIMIT 10;
        """)
        
        print("\n🔍 Ultime 10 certificazioni:")
        print("-" * 80)
        print(f"{'ID':<5} {'Cantiere':<8} {'Cavo':<10} {'Certificato':<15} {'Data':<12} {'Operatore':<15} {'Isolamento'}")
        print("-" * 80)
        
        for row in cursor.fetchall():
            id_cert, id_cantiere, id_cavo, numero_cert, data_cert, operatore, isolamento = row
            data_str = data_cert.strftime('%Y-%m-%d') if data_cert else 'N/A'
            print(f"{id_cert:<5} {id_cantiere:<8} {id_cavo:<10} {numero_cert or 'N/A':<15} {data_str:<12} {operatore or 'N/A':<15} {isolamento or 'N/A'}")
        
        # Verifica cavi con certificazioni
        cursor.execute("""
            SELECT c.id_cavo, c.id_cantiere, c.stato_installazione,
                   CASE WHEN cert.id_certificazione IS NOT NULL THEN 'SI' ELSE 'NO' END as certificato
            FROM cavi c
            LEFT JOIN certificazionicavi cert ON c.id_cavo = cert.id_cavo AND c.id_cantiere = cert.id_cantiere
            WHERE cert.id_certificazione IS NOT NULL
            ORDER BY c.id_cantiere, c.id_cavo
            LIMIT 10;
        """)
        
        print(f"\n🔗 Cavi con certificazioni (primi 10):")
        print("-" * 50)
        print(f"{'Cavo':<10} {'Cantiere':<8} {'Stato':<15} {'Certificato'}")
        print("-" * 50)
        
        for row in cursor.fetchall():
            id_cavo, id_cantiere, stato, certificato = row
            print(f"{id_cavo:<10} {id_cantiere:<8} {stato or 'N/A':<15} {certificato}")
        
        cursor.close()
        conn.close()
        
        print("\n✅ Verifica completata!")
        
    except Exception as e:
        print(f"❌ Errore durante la verifica: {str(e)}")

if __name__ == "__main__":
    check_certificazioni()
