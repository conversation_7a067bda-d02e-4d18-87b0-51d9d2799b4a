import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import {
  Box,
  Typography,
  Button,
  Paper,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  CircularProgress,
  List,
  ListItem,
  ListItemText,
  Stack,
  MenuItem,
  Divider
} from '@mui/material';
import {
  Add as AddIcon,
  Assignment as AssignIcon,
  Person as PersonIcon,
  CheckCircle as CheckCircleIcon,
  Verified as VerifiedIcon,
  People as PeopleIcon,
  Construction as ConstructionIcon
} from '@mui/icons-material';
import comandeService from '../../services/comandeService';
import responsabiliService from '../../services/responsabiliService';
import CreaComandaConCavi from './CreaComandaConCavi';
import ResponsabiliListPopup from './ResponsabiliListPopup';
import ComandeListTable from './ComandeListTable';
import InserimentoMetriDialog from './InserimentoMetriDialog';

const ComandeListRivoluzionato = ({ cantiereId, cantiereName }) => {
  // Hook per gestire i parametri URL
  const [searchParams, setSearchParams] = useSearchParams();

  // Stati principali - Responsabili come elemento principale
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchingComanda, setSearchingComanda] = useState(null);

  // Stati comande
  const [statistiche, setStatistiche] = useState(null);
  const [allComande, setAllComande] = useState([]);
  const [loadingComande, setLoadingComande] = useState(false);

  // Stati per popup responsabili
  const [openResponsabiliPopup, setOpenResponsabiliPopup] = useState(false);
  const [openCreaConCavi, setOpenCreaConCavi] = useState(false);

  // Stati responsabili
  const [responsabili, setResponsabili] = useState([]);
  const [loadingResponsabili, setLoadingResponsabili] = useState(false);
  const [comandePerResponsabile, setComandePerResponsabile] = useState({});
  const [openResponsabileDialog, setOpenResponsabileDialog] = useState(false);
  const [dialogModeResponsabile, setDialogModeResponsabile] = useState('create');
  const [selectedResponsabile, setSelectedResponsabile] = useState(null);
  const [formDataResponsabile, setFormDataResponsabile] = useState({
    nome_responsabile: '',
    email: '',
    telefono: ''
  });

  // Stati per dialog comande
  const [openComandaDialog, setOpenComandaDialog] = useState(false);
  const [selectedComanda, setSelectedComanda] = useState(null);
  const [formDataComanda, setFormDataComanda] = useState({
    tipo_comanda: 'POSA',
    descrizione: '',
    responsabile: '',
    data_scadenza: '',
    note_capo_cantiere: ''
  });

  // Stati per dialog inserimento metri
  const [openInserimentoMetri, setOpenInserimentoMetri] = useState(false);
  const [comandaPerMetri, setComandaPerMetri] = useState(null);

  const loadComande = async () => {
    try {
      setLoadingComande(true);
      console.log('🔄 Caricamento comande per cantiere:', cantiereId);
      const comandeData = await comandeService.getComande(cantiereId);
      console.log('📋 Dati comande ricevuti:', comandeData);
      console.log('📋 Tipo dati:', typeof comandeData, 'Array?', Array.isArray(comandeData));

      // Gestisci diversi formati di risposta
      let comandeArray = [];
      if (Array.isArray(comandeData)) {
        comandeArray = comandeData;
      } else if (comandeData && Array.isArray(comandeData.comande)) {
        comandeArray = comandeData.comande;
      } else if (comandeData && Array.isArray(comandeData.data)) {
        comandeArray = comandeData.data;
      }

      console.log('📋 Array comande finale:', comandeArray, 'Lunghezza:', comandeArray.length);
      setAllComande(comandeArray);
    } catch (err) {
      console.error('Errore nel caricamento comande:', err);
      setError('Errore nel caricamento delle comande');
    } finally {
      setLoadingComande(false);
    }
  };

  const loadStatistiche = async () => {
    try {
      console.log('🔄 Caricamento statistiche per cantiere:', cantiereId);
      const stats = await comandeService.getStatisticheComande(cantiereId);
      console.log('📊 Statistiche ricevute:', stats);
      setStatistiche(stats);
    } catch (err) {
      console.error('❌ Errore nel caricamento delle statistiche:', err);
      console.error('❌ Dettagli errore:', err.response?.data || err.message);
    }
  };

  const loadResponsabili = async () => {
    try {
      setLoadingResponsabili(true);
      setError(null);

      const data = await responsabiliService.getResponsabiliCantiere(cantiereId);
      setResponsabili(data || []);
      await loadComandePerResponsabili(data || []);
    } catch (err) {
      console.error('Errore nel caricamento dei responsabili:', err);
      const errorMessage = err.response?.data?.detail || err.message || 'Errore nel caricamento dei responsabili';
      setError(`Errore nel caricamento dei responsabili: ${errorMessage}`);
    } finally {
      setLoadingResponsabili(false);
    }
  };



  // Carica dati al mount - Focus sui responsabili
  useEffect(() => {
    const initializeData = async () => {
      if (cantiereId) {
        setLoading(true);
        try {
          await Promise.all([
            loadResponsabili(),
            loadComande(),
            loadStatistiche()
          ]);
        } catch (err) {
          console.error('Errore nel caricamento iniziale:', err);
        } finally {
          setLoading(false);
        }
      }
    };

    initializeData();
  }, [cantiereId]); // eslint-disable-line react-hooks/exhaustive-deps

  // Gestione parametro URL per aprire comanda specifica
  useEffect(() => {
    const comandaParam = searchParams.get('comanda');
    console.log('🔍 Controllo parametro URL comanda:', comandaParam);
    console.log('📊 Stato dati:', {
      responsabili: responsabili.length,
      comandePerResponsabile: Object.keys(comandePerResponsabile).length,
      loading,
      loadingResponsabili
    });

    // Imposta lo stato di ricerca
    if (comandaParam && comandaParam !== searchingComanda) {
      setSearchingComanda(comandaParam);
    }

    if (comandaParam && responsabili.length > 0 && Object.keys(comandePerResponsabile).length > 0) {
      console.log('🔎 Ricerca comanda tra i responsabili...');

      // Cerca la comanda tra tutti i responsabili
      let comandaTrovata = null;

      for (const responsabile of responsabili) {
        const comandeResp = comandePerResponsabile[responsabile.id_responsabile] || [];
        console.log(`📋 Responsabile ${responsabile.nome_responsabile}: ${comandeResp.length} comande`);
        comandaTrovata = comandeResp.find(c => c.codice_comanda === comandaParam);
        if (comandaTrovata) {
          console.log('✅ Comanda trovata:', comandaTrovata);
          break;
        }
      }

      if (comandaTrovata) {
        console.log('🎯 Apertura automatica comanda da URL:', comandaParam);
        setSearchingComanda(null); // Rimuovi lo stato di ricerca

        // Per comande di workflow (POSA, COLLEGAMENTO_PARTENZA, COLLEGAMENTO_ARRIVO) provenienti da visualizza cavi,
        // apri l'inserimento dati specifico. Per altre comande, apri il dialog di modifica generale
        const isWorkflowCommand = ['POSA', 'COLLEGAMENTO_PARTENZA', 'COLLEGAMENTO_ARRIVO'].includes(comandaTrovata.tipo_comanda);

        if (isWorkflowCommand) {
          console.log(`📏 Apertura gestione per comanda ${comandaTrovata.tipo_comanda} da visualizza cavi:`, comandaParam);
          handleOpenInserimentoMetri(comandaTrovata);
        } else {
          console.log('✏️ Apertura modifica per comanda non-workflow:', comandaParam);
          handleOpenComandaDialog(comandaTrovata);
        }

        // Rimuovi il parametro dall'URL per evitare riaperture
        setTimeout(() => {
          setSearchParams(prev => {
            const newParams = new URLSearchParams(prev);
            newParams.delete('comanda');
            return newParams;
          });
        }, 100);
      } else {
        console.warn('⚠️ Comanda non trovata:', comandaParam);
        console.log('📋 Comande disponibili:');
        responsabili.forEach(resp => {
          const comande = comandePerResponsabile[resp.id_responsabile] || [];
          comande.forEach(cmd => {
            console.log(`  - ${cmd.codice_comanda} (${resp.nome_responsabile})`);
          });
        });

        // Se non troviamo la comanda, potrebbe essere che i dati non sono completi
        // Riprova dopo un breve delay
        if (!loading && !loadingResponsabili) {
          console.log('🔄 Tentativo di ricaricamento dati...');
          setTimeout(() => {
            loadResponsabili();
          }, 500);
        }
      }
    } else if (comandaParam) {
      console.log('⏳ Dati non ancora caricati, attendo...');

      // Se abbiamo un parametro ma i dati non sono caricati, forza il caricamento
      if (!loading && !loadingResponsabili && responsabili.length === 0) {
        console.log('🚀 Forzatura caricamento responsabili...');
        loadResponsabili();
      }
    }
  }, [searchParams, responsabili, comandePerResponsabile, loading, loadingResponsabili]); // eslint-disable-line react-hooks/exhaustive-deps

  const loadComandePerResponsabili = async (responsabiliList) => {
    try {
      const comandeMap = {};
      for (const responsabile of responsabiliList) {
        try {
          const response = await comandeService.getComandeByResponsabile(cantiereId, responsabile.nome_responsabile);
          // Assicurati che sia sempre un array
          let comande = [];
          if (response && Array.isArray(response)) {
            comande = response;
          } else if (response && response.comande && Array.isArray(response.comande)) {
            comande = response.comande;
          } else if (response && response.data && Array.isArray(response.data)) {
            comande = response.data;
          }
          comandeMap[responsabile.id_responsabile] = comande;
        } catch (err) {
          console.error(`Errore nel caricamento comande per ${responsabile.nome_responsabile}:`, err);
          comandeMap[responsabile.id_responsabile] = [];
        }
      }
      setComandePerResponsabile(comandeMap);
    } catch (err) {
      console.error('Errore nel caricamento delle comande:', err);
    }
  };

  // Gestione responsabili
  const handleOpenResponsabileDialog = (mode, responsabile = null) => {
    setDialogModeResponsabile(mode);
    setSelectedResponsabile(responsabile);
    
    if (mode === 'edit' && responsabile) {
      setFormDataResponsabile({
        nome_responsabile: responsabile.nome_responsabile || '',
        email: responsabile.email || '',
        telefono: responsabile.telefono || ''
      });
    } else {
      setFormDataResponsabile({
        nome_responsabile: '',
        email: '',
        telefono: ''
      });
    }
    
    setOpenResponsabileDialog(true);
  };

  const handleCloseResponsabileDialog = () => {
    setOpenResponsabileDialog(false);
    setSelectedResponsabile(null);
    setError(null);
  };

  const handleSubmitResponsabile = async () => {
    try {
      setError(null);
      
      if (!formDataResponsabile.nome_responsabile.trim()) {
        setError('Il nome del responsabile è obbligatorio');
        return;
      }
      
      if (!formDataResponsabile.email && !formDataResponsabile.telefono) {
        setError('Almeno uno tra email e telefono deve essere specificato');
        return;
      }

      if (dialogModeResponsabile === 'create') {
        await responsabiliService.createResponsabile(cantiereId, formDataResponsabile);
      } else if (dialogModeResponsabile === 'edit') {
        await responsabiliService.updateResponsabile(selectedResponsabile.id_responsabile, formDataResponsabile);
      }

      handleCloseResponsabileDialog();
      await loadResponsabili();
      await loadComande();
    } catch (err) {
      console.error('Errore nel salvataggio:', err);
      setError(err.detail || 'Errore nel salvataggio del responsabile');
    }
  };

  const handleDeleteResponsabile = async (idResponsabile) => {
    if (!window.confirm('Sei sicuro di voler eliminare questo responsabile?')) {
      return;
    }

    try {
      await responsabiliService.deleteResponsabile(idResponsabile);
      await loadResponsabili();
      await loadComande();
    } catch (err) {
      console.error('Errore nell\'eliminazione:', err);
      setError('Errore nell\'eliminazione del responsabile');
    }
  };

  // Gestione comande
  const handleOpenComandaDialog = (comanda) => {
    setSelectedComanda(comanda);

    if (comanda) {
      setFormDataComanda({
        tipo_comanda: comanda.tipo_comanda,
        descrizione: comanda.descrizione || '',
        responsabile: comanda.responsabile || '',
        data_scadenza: comanda.data_scadenza || '',
        note_capo_cantiere: comanda.note_capo_cantiere || ''
      });
    }

    setOpenComandaDialog(true);
  };

  const handleCloseComandaDialog = () => {
    setOpenComandaDialog(false);
    setSelectedComanda(null);
    setFormDataComanda({
      tipo_comanda: 'POSA',
      descrizione: '',
      responsabile: '',
      data_scadenza: '',
      note_capo_cantiere: ''
    });
  };

  const handleSubmitComanda = async () => {
    try {
      await comandeService.updateComanda(selectedComanda.codice_comanda, formDataComanda);
      handleCloseComandaDialog();
      await loadResponsabili(); // Ricarica per aggiornare le comande
      await loadComande();
      await loadStatistiche();
    } catch (err) {
      console.error('Errore nel salvataggio:', err);
      setError('Errore nel salvataggio della comanda');
    }
  };

  const handleDeleteComanda = async (codiceComanda) => {
    if (!window.confirm('Sei sicuro di voler eliminare questa comanda?')) {
      return;
    }

    try {
      await comandeService.deleteComanda(codiceComanda);
      await loadResponsabili(); // Ricarica per aggiornare le comande
      await loadComande();
      await loadStatistiche();
    } catch (err) {
      console.error('Errore nell\'eliminazione:', err);
      setError('Errore nell\'eliminazione della comanda');
    }
  };

  // Gestione inserimento metri
  const handleOpenInserimentoMetri = (comanda) => {
    setComandaPerMetri(comanda);
    setOpenInserimentoMetri(true);
  };

  const handleCloseInserimentoMetri = () => {
    setOpenInserimentoMetri(false);
    setComandaPerMetri(null);
  };

  const handleSuccessInserimentoMetri = async (message) => {
    console.log('✅ Successo inserimento metri:', message);
    setError(null);

    // Ricarica tutti i dati per aggiornare le statistiche
    await Promise.all([
      loadResponsabili(),
      loadComande(),
      loadStatistiche()
    ]);

    // Mostra un messaggio di successo (opzionale)
    // Potresti aggiungere uno stato per i messaggi di successo se necessario
  };

  // Gestione stampa comanda
  const handlePrintComanda = (comanda) => {
    console.log('🖨️ Stampa comanda:', comanda.codice_comanda);

    // Per ora mostra un dialog di selezione formato
    const formato = window.prompt(
      `Seleziona il formato di stampa per la comanda ${comanda.codice_comanda}:\n\n` +
      '1 - A4 (Formato standard)\n' +
      '2 - A3 (Formato esteso)\n\n' +
      'Inserisci 1 o 2:',
      '1'
    );

    if (formato === '1' || formato === '2') {
      const formatoNome = formato === '1' ? 'A4' : 'A3';
      console.log(`📄 Generazione comanda ${comanda.codice_comanda} in formato ${formatoNome}`);

      // TODO: Implementare la generazione del PDF
      alert(`Funzionalità in sviluppo!\n\nComanda: ${comanda.codice_comanda}\nFormato: ${formatoNome}\nTipo: ${comanda.tipo_comanda}\nResponsabile: ${comanda.responsabile}\nCavi: ${comanda.numero_cavi_assegnati || 0}`);
    }
  };

  const getTipoComandaLabel = (tipo) => {
    const labels = {
      'POSA': 'Posa',
      'COLLEGAMENTO_PARTENZA': 'Coll. Partenza',
      'COLLEGAMENTO_ARRIVO': 'Coll. Arrivo',
      'CERTIFICAZIONE': 'Certificazione',
      'TESTING': 'Testing/Certificazione'
    };
    return labels[tipo] || tipo;
  };

  const getStatoColor = (stato) => {
    const colors = {
      'CREATA': 'default',
      'ASSEGNATA': 'primary',
      'IN_CORSO': 'warning',
      'COMPLETATA': 'success',
      'ANNULLATA': 'error'
    };
    return colors[stato] || 'default';
  };



  if (loading || loadingComande) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box mb={3}>
        <Typography variant="h4" sx={{ fontWeight: 600, color: 'primary.main' }}>
          {cantiereName}
        </Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {searchingComanda && (
        <Alert severity="info" sx={{ mb: 2 }}>
          🔍 Ricerca comanda {searchingComanda} in corso...
        </Alert>
      )}

      {/* Statistiche in stile Visualizza Cavi */}
      {statistiche && (
        <Paper sx={{ p: 2, mb: 3, bgcolor: 'grey.50' }}>
          <Stack direction="row" spacing={4} alignItems="center" justifyContent="space-between" flexWrap="wrap">
            {/* Responsabili */}
            <Stack direction="row" alignItems="center" spacing={1}>
              <PersonIcon color="primary" fontSize="small" />
              <Box>
                <Typography variant="h6" fontWeight="bold" sx={{ lineHeight: 1 }}>
                  {statistiche.responsabili_attivi || 0}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Responsabili
                </Typography>
              </Box>
            </Stack>

            {/* Totale Comande */}
            <Stack direction="row" alignItems="center" spacing={1}>
              <AssignIcon color="info" fontSize="small" />
              <Box>
                <Typography variant="h6" fontWeight="bold" sx={{ lineHeight: 1 }}>
                  {statistiche.totale_comande || 0}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Totale
                </Typography>
              </Box>
            </Stack>

            {/* In Corso */}
            <Stack direction="row" alignItems="center" spacing={1}>
              <CheckCircleIcon color="warning" fontSize="small" />
              <Box>
                <Typography variant="h6" fontWeight="bold" sx={{ lineHeight: 1 }}>
                  {statistiche.comande_in_corso || 0}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  In Corso
                </Typography>
              </Box>
            </Stack>

            {/* Completate */}
            <Stack direction="row" alignItems="center" spacing={1}>
              <VerifiedIcon color="success" fontSize="small" />
              <Box>
                <Typography variant="h6" fontWeight="bold" sx={{ lineHeight: 1 }}>
                  {statistiche.comande_completate || 0}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Completate
                </Typography>
              </Box>
            </Stack>

            {/* Percentuale completamento */}
            <Stack direction="row" alignItems="center" spacing={1}>
              <Box sx={{
                width: 32,
                height: 32,
                borderRadius: '50%',
                bgcolor: (statistiche.comande_completate / (statistiche.totale_comande || 1)) >= 0.8 ? 'success.main' :
                         (statistiche.comande_completate / (statistiche.totale_comande || 1)) >= 0.5 ? 'warning.main' : 'error.main',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <Typography variant="caption" fontWeight="bold" color="white">
                  {statistiche.totale_comande > 0 ? Math.round((statistiche.comande_completate / statistiche.totale_comande) * 100) : 0}%
                </Typography>
              </Box>
              <Box>
                <Typography variant="body2" fontWeight="medium" sx={{ lineHeight: 1 }}>
                  Completamento
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {statistiche.comande_create || 0} create
                </Typography>
              </Box>
            </Stack>
          </Stack>
        </Paper>
      )}

      {/* Sezione Comande - Elemento Principale */}
      <Box>
        <Box>
          {/* Toolbar Comande */}
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
            <Typography variant="h5" sx={{ fontWeight: 500, color: 'text.primary' }}>
              Gestione Responsabili e Comande
            </Typography>
            <Box display="flex" gap={2}>
              <Button
                variant="outlined"
                startIcon={<PeopleIcon />}
                onClick={() => setOpenResponsabiliPopup(true)}
                sx={{
                  textTransform: 'none',
                  fontWeight: 500,
                  px: 3,
                  py: 1,
                  backgroundColor: '#f5f7fa',
                  color: '#2196f3',
                  border: '1px solid #2196f3',
                  '&:hover': {
                    backgroundColor: 'rgba(33, 150, 243, 0.1)',
                    borderColor: '#1976d2'
                  }
                }}
              >
                Lista Responsabili
              </Button>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => handleOpenResponsabileDialog('create')}
                sx={{
                  textTransform: 'none',
                  fontWeight: 500,
                  px: 3,
                  py: 1,
                  backgroundColor: '#2196f3',
                  color: 'white',
                  '&:hover': {
                    backgroundColor: '#1976d2'
                  }
                }}
              >
                Crea Responsabile
              </Button>
            </Box>
          </Box>

          {/* Lista Comande in stile tabella */}
          {loadingComande ? (
            <Box display="flex" justifyContent="center" py={4}>
              <CircularProgress />
            </Box>
          ) : allComande.length === 0 ? (
            <Paper
              elevation={0}
              sx={{
                p: 6,
                textAlign: 'center',
                backgroundColor: 'grey.50',
                border: '1px dashed',
                borderColor: 'grey.300'
              }}
            >
              <AssignIcon sx={{ fontSize: 48, color: 'grey.400', mb: 2 }} />
              <Typography variant="h6" color="text.secondary" gutterBottom>
                Nessuna comanda disponibile
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Crea la prima comanda per iniziare a gestire i lavori
              </Typography>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => setOpenCreaConCavi(true)}
                sx={{
                  textTransform: 'none',
                  backgroundColor: '#2196f3',
                  color: 'white',
                  '&:hover': {
                    backgroundColor: '#1976d2'
                  }
                }}
              >
                Crea Prima Comanda
              </Button>
            </Paper>
          ) : (
            <ComandeListTable
              comande={allComande}
              onEditComanda={handleOpenComandaDialog}
              onDeleteComanda={handleDeleteComanda}
              onInserimentoMetri={handleOpenInserimentoMetri}
              onPrintComanda={handlePrintComanda}
              loading={loadingComande}
            />
          )}
        </Box>
      </Box>

      {/* Dialog per creazione/modifica responsabile */}
      <Dialog
        open={openResponsabileDialog}
        onClose={handleCloseResponsabileDialog}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: { borderRadius: 2 }
        }}
      >
        <DialogTitle sx={{ pb: 1 }}>
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            {dialogModeResponsabile === 'create' ? 'Inserisci Responsabile' : 'Modifica Responsabile'}
          </Typography>
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <TextField
              fullWidth
              label="Nome Responsabile"
              value={formDataResponsabile.nome_responsabile}
              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, nome_responsabile: e.target.value })}
              margin="normal"
              required
              variant="outlined"
              sx={{ mb: 2 }}
            />

            <TextField
              fullWidth
              label="Email"
              type="email"
              value={formDataResponsabile.email}
              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, email: e.target.value })}
              margin="normal"
              variant="outlined"
              helperText="Email per notifiche (opzionale se inserisci telefono)"
              sx={{ mb: 2 }}
            />

            <TextField
              fullWidth
              label="Telefono"
              value={formDataResponsabile.telefono}
              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, telefono: e.target.value })}
              margin="normal"
              variant="outlined"
              helperText="Numero per SMS (opzionale se inserisci email)"
            />
          </Box>
        </DialogContent>
        <DialogActions sx={{ p: 3, pt: 2 }}>
          <Button
            onClick={handleCloseResponsabileDialog}
            sx={{ textTransform: 'none' }}
          >
            Annulla
          </Button>
          <Button
            onClick={handleSubmitResponsabile}
            variant="contained"
            sx={{
              textTransform: 'none',
              fontWeight: 500,
              px: 3
            }}
          >
            {dialogModeResponsabile === 'create' ? 'Crea' : 'Salva'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog per modifica comanda */}
      <Dialog
        open={openComandaDialog}
        onClose={handleCloseComandaDialog}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: { borderRadius: 2 }
        }}
      >
        <DialogTitle sx={{ pb: 1 }}>
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            Modifica Comanda {selectedComanda?.codice_comanda}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Stato: {selectedComanda?.stato} • Cavi assegnati: {selectedComanda?.numero_cavi_assegnati || 0}
          </Typography>
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <Alert severity="warning" sx={{ mb: 2 }}>
              <Typography variant="body2">
                <strong>Attenzione:</strong> Modificare il tipo di comanda riassegnerà automaticamente tutti i cavi al nuovo tipo.
                Assicurati che i cavi siano compatibili con il nuovo tipo di operazione.
              </Typography>
            </Alert>

            <TextField
              fullWidth
              select
              label="Tipo Comanda"
              value={formDataComanda.tipo_comanda}
              onChange={(e) => setFormDataComanda({ ...formDataComanda, tipo_comanda: e.target.value })}
              margin="normal"
              sx={{ mb: 2 }}
            >
              <MenuItem value="POSA">Posa</MenuItem>
              <MenuItem value="COLLEGAMENTO_PARTENZA">Collegamento Partenza</MenuItem>
              <MenuItem value="COLLEGAMENTO_ARRIVO">Collegamento Arrivo</MenuItem>
              <MenuItem value="CERTIFICAZIONE">Certificazione</MenuItem>
              <MenuItem value="TESTING">Testing</MenuItem>
            </TextField>

            <TextField
              fullWidth
              label="Descrizione"
              value={formDataComanda.descrizione}
              onChange={(e) => setFormDataComanda({ ...formDataComanda, descrizione: e.target.value })}
              margin="normal"
              multiline
              rows={3}
              sx={{ mb: 2 }}
            />

            <TextField
              fullWidth
              label="Responsabile"
              value={formDataComanda.responsabile}
              onChange={(e) => setFormDataComanda({ ...formDataComanda, responsabile: e.target.value })}
              margin="normal"
              required
              helperText="Chi eseguirà il lavoro (obbligatorio)"
              sx={{ mb: 2 }}
            />

            <TextField
              fullWidth
              label="Note Capo Cantiere"
              value={formDataComanda.note_capo_cantiere}
              onChange={(e) => setFormDataComanda({ ...formDataComanda, note_capo_cantiere: e.target.value })}
              margin="normal"
              multiline
              rows={2}
              helperText="Istruzioni specifiche per il responsabile"
              sx={{ mb: 2 }}
            />

            <TextField
              fullWidth
              label="Data Scadenza"
              type="date"
              value={formDataComanda.data_scadenza}
              onChange={(e) => setFormDataComanda({ ...formDataComanda, data_scadenza: e.target.value })}
              margin="normal"
              InputLabelProps={{
                shrink: true,
              }}
            />
          </Box>
        </DialogContent>
        <DialogActions sx={{ p: 3, pt: 2 }}>
          <Button
            onClick={handleCloseComandaDialog}
            sx={{ textTransform: 'none' }}
          >
            Annulla
          </Button>
          <Button
            onClick={handleSubmitComanda}
            variant="contained"
            sx={{
              textTransform: 'none',
              fontWeight: 500,
              px: 3
            }}
          >
            Salva Modifiche
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog CreaComandaConCavi */}
      <CreaComandaConCavi
        cantiereId={cantiereId}
        open={openCreaConCavi}
        onClose={() => setOpenCreaConCavi(false)}
        onSuccess={(response, successMessage) => {
          console.log('🎉 Comanda creata, aggiornamento interfaccia...');

          // Mostra messaggio di successo se fornito
          if (successMessage) {
            // Potresti aggiungere qui un toast/snackbar per mostrare il messaggio
            console.log('📢 Successo:', successMessage);
          }

          // Ricarica tutti i dati per aggiornare l'interfaccia
          loadComande();
          loadStatistiche();
          loadResponsabili();
          setOpenCreaConCavi(false);

          console.log('✅ Interfaccia aggiornata');
        }}
      />

      {/* Popup Lista Responsabili */}
      <ResponsabiliListPopup
        open={openResponsabiliPopup}
        onClose={() => setOpenResponsabiliPopup(false)}
        responsabili={responsabili}
        comandePerResponsabile={comandePerResponsabile}
        onEditResponsabile={(responsabile) => {
          setOpenResponsabiliPopup(false);
          handleOpenResponsabileDialog('edit', responsabile);
        }}
        onDeleteResponsabile={async (idResponsabile) => {
          await handleDeleteResponsabile(idResponsabile);
          setOpenResponsabiliPopup(false);
        }}
        loading={loadingResponsabili}
        error={error}
      />

      {/* Dialog Inserimento Metri */}
      <InserimentoMetriDialog
        open={openInserimentoMetri}
        onClose={handleCloseInserimentoMetri}
        comanda={comandaPerMetri}
        onSuccess={handleSuccessInserimentoMetri}
      />
    </Box>
  );
};

export default ComandeListRivoluzionato;
