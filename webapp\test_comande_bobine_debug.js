// Test per verificare il caricamento delle bobine nelle comande
// Questo script testa l'API per il caricamento delle bobine quando si inseriscono i metri

const axios = require('axios');

const API_BASE = 'http://localhost:8001/api';

// Funzione per ottenere il token di autenticazione
async function getAuthToken() {
  try {
    const response = await axios.post(`${API_BASE}/auth/login`, {
      username: 'a',
      password: 'a'
    });
    return response.data.access_token;
  } catch (error) {
    console.error('Errore nell\'autenticazione:', error.response?.data || error.message);
    throw error;
  }
}

// Funzione per testare il caricamento delle bobine disponibili
async function testBobineDisponibili(token, cantiereId) {
  try {
    console.log(`\n=== Test Caricamento Bobine Disponibili per Cantiere ${cantiereId} ===`);
    
    const response = await axios.get(`${API_BASE}/parco-cavi/${cantiereId}?disponibili_only=true`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log(`✅ Bobine caricate con successo: ${response.data.length} bobine`);
    
    if (response.data.length > 0) {
      console.log('\n📋 Prime 3 bobine disponibili:');
      response.data.slice(0, 3).forEach((bobina, index) => {
        console.log(`  ${index + 1}. ID: ${bobina.id_bobina}`);
        console.log(`     Tipologia: ${bobina.tipologia}`);
        console.log(`     Sezione: ${bobina.sezione}`);
        console.log(`     Metri residui: ${bobina.metri_residui}`);
        console.log(`     Stato: ${bobina.stato_bobina}`);
        console.log('');
      });
    } else {
      console.log('⚠️  Nessuna bobina disponibile trovata');
    }
    
    return response.data;
  } catch (error) {
    console.error('❌ Errore nel caricamento bobine:', error.response?.data || error.message);
    throw error;
  }
}

// Funzione per testare il caricamento dei cavi di una comanda
async function testCaviComanda(token, codiceComanda) {
  try {
    console.log(`\n=== Test Caricamento Cavi Comanda ${codiceComanda} ===`);
    
    const response = await axios.get(`${API_BASE}/comande/${codiceComanda}/cavi`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log(`✅ Cavi comanda caricati con successo: ${response.data.length} cavi`);
    
    if (response.data.length > 0) {
      console.log('\n📋 Cavi della comanda:');
      response.data.forEach((cavo, index) => {
        console.log(`  ${index + 1}. ID: ${cavo.id_cavo}`);
        console.log(`     Tipologia: ${cavo.tipologia}`);
        console.log(`     Sezione: ${cavo.sezione}`);
        console.log(`     Metri teorici: ${cavo.metratura_teorica}`);
        console.log(`     Metri reali: ${cavo.metratura_reale}`);
        console.log(`     Bobina attuale: ${cavo.id_bobina || 'Nessuna'}`);
        console.log(`     Stato: ${cavo.stato_installazione}`);
        console.log('');
      });
    } else {
      console.log('⚠️  Nessun cavo assegnato alla comanda');
    }
    
    return response.data;
  } catch (error) {
    console.error('❌ Errore nel caricamento cavi comanda:', error.response?.data || error.message);
    throw error;
  }
}

// Funzione per testare le comande disponibili
async function testComande(token, cantiereId) {
  try {
    console.log(`\n=== Test Caricamento Comande per Cantiere ${cantiereId} ===`);
    
    const response = await axios.get(`${API_BASE}/comande/cantiere/${cantiereId}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log(`✅ Comande caricate con successo: ${response.data.comande.length} comande`);
    
    if (response.data.comande.length > 0) {
      console.log('\n📋 Comande disponibili:');
      response.data.comande.forEach((comanda, index) => {
        console.log(`  ${index + 1}. Codice: ${comanda.codice_comanda}`);
        console.log(`     Tipo: ${comanda.tipo_comanda}`);
        console.log(`     Responsabile: ${comanda.responsabile}`);
        console.log(`     Stato: ${comanda.stato}`);
        console.log(`     Cavi assegnati: ${comanda.numero_cavi_assegnati || 0}`);
        console.log('');
      });
      
      // Trova una comanda di tipo POSA per testare
      const comandaPosa = response.data.comande.find(c => c.tipo_comanda === 'POSA');
      if (comandaPosa) {
        console.log(`🎯 Trovata comanda POSA: ${comandaPosa.codice_comanda}`);
        return comandaPosa;
      } else {
        console.log('⚠️  Nessuna comanda di tipo POSA trovata');
      }
    } else {
      console.log('⚠️  Nessuna comanda trovata');
    }
    
    return null;
  } catch (error) {
    console.error('❌ Errore nel caricamento comande:', error.response?.data || error.message);
    throw error;
  }
}

// Funzione principale di test
async function runTest() {
  try {
    console.log('🚀 Avvio test sistema comande-bobine...\n');
    
    // 1. Autenticazione
    console.log('1. Autenticazione...');
    const token = await getAuthToken();
    console.log('✅ Autenticazione riuscita');
    
    // 2. Test con cantiere ID 1 (default)
    const cantiereId = 1;
    
    // 3. Carica bobine disponibili
    const bobine = await testBobineDisponibili(token, cantiereId);
    
    // 4. Carica comande
    const comandaPosa = await testComande(token, cantiereId);
    
    // 5. Se c'è una comanda POSA, testa i suoi cavi
    if (comandaPosa) {
      const cavi = await testCaviComanda(token, comandaPosa.codice_comanda);
      
      // 6. Analisi compatibilità bobine-cavi
      if (bobine.length > 0 && cavi.length > 0) {
        console.log('\n=== Analisi Compatibilità Bobine-Cavi ===');
        
        cavi.forEach(cavo => {
          const bobineCompatibili = bobine.filter(bobina => 
            bobina.tipologia === cavo.tipologia && 
            bobina.sezione === cavo.sezione &&
            bobina.metri_residui > 0
          );
          
          console.log(`\n🔍 Cavo ${cavo.id_cavo} (${cavo.tipologia} ${cavo.sezione}):`);
          console.log(`   Bobine compatibili: ${bobineCompatibili.length}`);
          
          if (bobineCompatibili.length > 0) {
            bobineCompatibili.forEach(bobina => {
              console.log(`   - ${bobina.id_bobina}: ${bobina.metri_residui}m residui`);
            });
          } else {
            console.log('   ⚠️  Nessuna bobina compatibile - dovrà usare BOBINA_VUOTA');
          }
        });
      }
    }
    
    console.log('\n✅ Test completato con successo!');
    
  } catch (error) {
    console.error('\n❌ Test fallito:', error.message);
    process.exit(1);
  }
}

// Esegui il test
runTest();
