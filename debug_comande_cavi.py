#!/usr/bin/env python3
"""
Script per debuggare i problemi con il conteggio di responsabili e cavi nelle comande.
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from modules.database_pg import Database

def debug_comande_cavi():
    """Debug delle comande e dei cavi assegnati."""
    
    print("🔍 DEBUG COMANDE E CAVI")
    print("=" * 50)
    
    db = Database()
    
    try:
        with db.get_connection() as conn:
            cursor = conn.cursor()
            
            # 1. Verifica responsabili per cantiere
            print("\n📋 1. RESPONSABILI PER CANTIERE:")
            cursor.execute("""
                SELECT id_cantiere, COUNT(*) as count_responsabili
                FROM responsabili
                WHERE attivo = TRUE
                GROUP BY id_cantiere
                ORDER BY id_cantiere;
            """)
            
            for row in cursor.fetchall():
                cantiere_id, count = row
                print(f"  Cantiere {cantiere_id}: {count} responsabili attivi")
            
            # 2. Verifica comande per cantiere
            print("\n📋 2. COMANDE PER CANTIERE:")
            cursor.execute("""
                SELECT id_cantiere, COUNT(*) as count_comande, stato
                FROM comande
                GROUP BY id_cantiere, stato
                ORDER BY id_cantiere, stato;
            """)
            
            for row in cursor.fetchall():
                cantiere_id, count, stato = row
                print(f"  Cantiere {cantiere_id}: {count} comande in stato {stato}")
            
            # 3. Verifica cavi assegnati alle comande
            print("\n📋 3. CAVI ASSEGNATI ALLE COMANDE:")
            cursor.execute("""
                SELECT 
                    c.codice_comanda,
                    c.tipo_comanda,
                    c.responsabile,
                    c.stato,
                    COUNT(CASE WHEN cv.comanda_posa = c.codice_comanda THEN 1 END) as cavi_posa,
                    COUNT(CASE WHEN cv.comanda_partenza = c.codice_comanda THEN 1 END) as cavi_partenza,
                    COUNT(CASE WHEN cv.comanda_arrivo = c.codice_comanda THEN 1 END) as cavi_arrivo,
                    COUNT(CASE WHEN cv.comanda_certificazione = c.codice_comanda THEN 1 END) as cavi_certificazione
                FROM comande c
                LEFT JOIN cavi cv ON c.id_cantiere = cv.id_cantiere
                GROUP BY c.codice_comanda, c.tipo_comanda, c.responsabile, c.stato
                ORDER BY c.codice_comanda;
            """)
            
            for row in cursor.fetchall():
                codice, tipo, responsabile, stato, posa, partenza, arrivo, cert = row
                total_cavi = posa + partenza + arrivo + cert
                print(f"  {codice} ({tipo}): {total_cavi} cavi totali")
                print(f"    - Posa: {posa}, Partenza: {partenza}, Arrivo: {arrivo}, Certificazione: {cert}")
                print(f"    - Responsabile: {responsabile}, Stato: {stato}")
            
            # 4. Verifica dettagli specifici per le comande problematiche
            print("\n📋 4. DETTAGLI COMANDE SPECIFICHE:")
            cursor.execute("""
                SELECT codice_comanda, tipo_comanda, responsabile, stato, id_cantiere
                FROM comande
                WHERE codice_comanda IN ('POS007', 'POS008')
                ORDER BY codice_comanda;
            """)
            
            for row in cursor.fetchall():
                codice, tipo, responsabile, stato, cantiere = row
                print(f"\n  Comanda: {codice}")
                print(f"    Tipo: {tipo}, Responsabile: {responsabile}")
                print(f"    Stato: {stato}, Cantiere: {cantiere}")
                
                # Verifica cavi assegnati a questa comanda specifica
                if tipo == "POSA":
                    cursor.execute("""
                        SELECT id_cavo, stato_installazione, comanda_posa
                        FROM cavi
                        WHERE id_cantiere = %s AND comanda_posa = %s
                        LIMIT 5;
                    """, (cantiere, codice))
                    
                    cavi = cursor.fetchall()
                    print(f"    Cavi assegnati: {len(cavi)}")
                    for cavo in cavi:
                        print(f"      - {cavo[0]}: {cavo[1]} (comanda: {cavo[2]})")
            
            # 5. Verifica tabella comandedettaglio
            print("\n📋 5. TABELLA COMANDEDETTAGLIO:")
            cursor.execute("""
                SELECT codice_comanda, COUNT(*) as count_dettagli
                FROM comandedettaglio
                GROUP BY codice_comanda
                ORDER BY codice_comanda;
            """)
            
            dettagli = cursor.fetchall()
            if dettagli:
                for row in dettagli:
                    codice, count = row
                    print(f"  {codice}: {count} dettagli")
            else:
                print("  Nessun dettaglio trovato nella tabella comandedettaglio")
            
    except Exception as e:
        print(f"❌ Errore durante il debug: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_comande_cavi()
