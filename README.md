# CABLYS - Cable Installation Advance System

Sistema completo per la gestione cantieri con app mobile integrata, notifiche automatiche e workflow intelligente end-to-end.

## 🚀 Caratteristiche Principali

### ✅ Sistema Completo Implementato
- **Gestione Cantieri**: Dashboard avanzate con responsabili per cantiere specifico
- **Sistema Comande**: 4 tipi di comande con workflow automatizzato
- **App Mobile**: Simulatore completo con autenticazione sicura
- **Notifiche Automatiche**: Email e SMS multi-provider
- **Certificazioni**: Conformi CEI 64-8/IEC con PDF automatici
- **Reports Avanzati**: BOQ, progresso, IAP e analytics

### 🔄 Workflow Automatizzato
```
Creazione Comanda → Auto-inserimento Responsabile → Notifiche Email/SMS → App Mobile → Aggiornamento Real-time
```

### 📱 App Mobile Integrata
- **Simulatore Completo**: http://localhost:3001
- **Autenticazione**: Codice comanda + email/telefono
- **Gestione Real-time**: Visualizzazione e aggiornamento lavori
- **Sincronizzazione**: Automatica con sistema centrale

### 📧 Sistema Notifiche
- **Email**: SMTP configurabile (Gmail, Outlook, provider italiani)
- **SMS**: Multi-provider (Twilio, TextMagic, API custom)
- **Automatico**: Invio immediato alla creazione comande
- **Template**: Personalizzabili con variabili dinamiche

## 🏗️ Architettura

### Backend (FastAPI)
- **API RESTful**: Endpoint completi per tutti i moduli
- **Database**: PostgreSQL con schema ottimizzato
- **Autenticazione**: JWT + token sessione mobile
- **Notifiche**: Moduli email/SMS integrati

### Frontend (React)
- **Dashboard**: Interfacce moderne e responsive
- **UX Avanzata**: Pulsanti intelligenti e popup interattivi
- **Real-time**: Aggiornamenti automatici stato

### Mobile (Simulatore Web)
- **Progressive Web App**: Esperienza mobile nativa
- **Offline Ready**: Funzionalità base offline
- **Touch Optimized**: Interfaccia touch-friendly

## 🚀 Quick Start

### 1. Avvia Backend
```bash
cd webapp
python backend/main.py --port 8002
```

### 2. Avvia Frontend
```bash
cd webapp/frontend
npm start
```

### 3. Avvia Simulatore Mobile
```bash
cd mobile_simulator
python server.py
```

### 4. Accedi ai Sistemi
- **Sistema CMS**: http://localhost:3000
- **Simulatore Mobile**: http://localhost:3001
- **API Backend**: http://localhost:8002
- **Website**: Apri `WEBSITE/index.html`

## 📋 Moduli Implementati

### 1. Gestione Cantieri
- Cantieri con responsabili specifici
- Auto-inserimento responsabili
- Tracciabilità completa

### 2. Sistema Comande Avanzato
- **4 Tipi**: POSA, COLLEGAMENTO_PARTENZA, COLLEGAMENTO_ARRIVO, CERTIFICAZIONE
- **Workflow**: Selezione cavi → Creazione → Notifica → Mobile
- **Validazione**: Controlli automatici conflitti e prerequisiti

### 3. App Mobile
- **Login**: Codice comanda + contatto responsabile
- **Dashboard**: Dettagli comanda e progresso
- **Gestione**: Visualizzazione cavi e aggiornamento stato
- **Multi-comanda**: Lista tutte le comande responsabile

### 4. Sistema Notifiche
- **Email**: Configurazione SMTP flessibile
- **SMS**: Provider multipli con normalizzazione numeri
- **Automatico**: Invio alla creazione comande
- **Template**: Personalizzabili e professionali

### 5. Gestione Cavi
- Pulsanti intelligenti per stato
- Popup interattivi per modifiche
- Sistema BOBINA_VUOTA integrato

### 6. Certificazioni
- Workflow CEI 64-8/IEC completo
- Generazione PDF automatica
- Dati meteorologici integrati

### 7. Reports Avanzati
- BOQ con calcoli automatici
- Progresso con IAP (Indice Avanzamento Ponderato)
- Analytics e dashboard

## 🔧 Configurazione

### Database
```bash
# PostgreSQL con credenziali default
Host: localhost
Port: 5432
Database: cantieri
User: postgres
Password: Taranto
```

### Email (Opzionale)
```bash
# File .env
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>
```

### SMS (Opzionale)
```bash
# Twilio
SMS_PROVIDER=twilio
TWILIO_ACCOUNT_SID=your_account_sid
TWILIO_AUTH_TOKEN=your_auth_token
TWILIO_FROM_NUMBER=+**********

# TextMagic
SMS_PROVIDER=textmagic
TEXTMAGIC_USERNAME=your_username
TEXTMAGIC_API_KEY=your_api_key
```

## 🧪 Test del Sistema

### 1. Test Workflow Completo
```bash
# 1. Crea comanda dal sistema CMS
# 2. Inserisci responsabile con email/telefono
# 3. Sistema invia notifica automatica
# 4. Usa codice comanda nel simulatore mobile
# 5. Gestisci lavori da mobile
```

### 2. Test API Mobile
```bash
# Ping API
curl http://localhost:8002/api/mobile/ping

# Login mobile
curl -X POST http://localhost:8002/api/mobile/login \
  -H "Content-Type: application/json" \
  -d '{"codice_comanda":"POS_1_...", "contatto":"<EMAIL>"}'
```

## 📱 Simulatore Mobile

### Caratteristiche
- **Design Mobile-First**: Ottimizzato per smartphone
- **Autenticazione Sicura**: Token-based con validazione
- **Interfaccia Intuitiva**: Navigation touch-friendly
- **Real-time**: Sincronizzazione automatica

### Come Usare
1. **Crea Comanda**: Dal sistema CMS con responsabile
2. **Ricevi Codice**: Via email/SMS (se configurato)
3. **Accedi Mobile**: http://localhost:3001
4. **Inserisci Dati**: Codice comanda + email/telefono
5. **Gestisci Lavori**: Visualizza e aggiorna stato

## 🔒 Sicurezza

### Autenticazione
- **Sistema CMS**: JWT con refresh token
- **App Mobile**: Token sessione con validazione responsabile
- **API**: Rate limiting e validazione input

### Dati Sensibili
- **Configurazione**: Variabili d'ambiente
- **Database**: Connessioni sicure
- **Notifiche**: Credenziali protette

## 📊 Metriche e Analytics

### Dashboard Real-time
- Progresso cantieri con IAP
- Statistiche comande per tipo
- Performance responsabili
- Utilizzo app mobile

### Reports Automatici
- BOQ con calcoli procurement
- Avanzamento lavori ponderato
- Certificazioni completate
- Storico utilizzo bobine

## 🚀 Roadmap Futuro

### App Mobile Nativa
- React Native / Flutter
- Notifiche push
- Modalità offline
- Geolocalizzazione

### Integrazione IoT
- Sensori installazione
- Monitoraggio automatico
- Alerting intelligente

### AI/ML Features
- Predizione tempi lavoro
- Ottimizzazione assegnazioni
- Anomaly detection

## 📞 Supporto

### Documentazione
- **API**: http://localhost:8002/docs
- **Mobile**: `mobile_simulator/README.md`
- **Configurazione**: File di config in `config/`

### Troubleshooting
1. Verifica database PostgreSQL attivo
2. Controlla porte disponibili (3000, 8002, 3001)
3. Valida configurazione email/SMS
4. Consulta log applicazioni

## 📄 Licenza

CABLYS - Cable Installation Advance System
© 2025 - Sistema proprietario per gestione cantieri

---

**Sistema completo e funzionale pronto per produzione!** 🎉
