{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\comande\\\\CreaComandaConCavi.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Card, CardContent, Typography, Button, Dialog, DialogTitle, DialogContent, DialogActions, TextField, MenuItem, Alert, CircularProgress, Stepper, Step, StepLabel, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Checkbox, Chip, Grid, Autocomplete } from '@mui/material';\nimport { Add as AddIcon, Cable as CableIcon, Assignment as AssignmentIcon } from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport responsabiliService from '../../services/responsabiliService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CreaComandaConCavi = ({\n  cantiereId,\n  open,\n  onClose,\n  onSuccess,\n  tipoComandaPreselezionato = null,\n  caviPreselezionati = []\n}) => {\n  _s();\n  const [activeStep, setActiveStep] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  // Step 1: Selezione tipo comanda\n  const [tipoComanda, setTipoComanda] = useState(tipoComandaPreselezionato || 'POSA');\n\n  // Step 2: Selezione cavi\n  const [caviDisponibili, setCaviDisponibili] = useState([]);\n  const [caviSelezionati, setCaviSelezionati] = useState(caviPreselezionati || []);\n  const [loadingCavi, setLoadingCavi] = useState(false);\n\n  // Step 3: Dettagli comanda\n  const [formData, setFormData] = useState({\n    descrizione: '',\n    responsabile: '',\n    responsabile_email: '',\n    responsabile_telefono: '',\n    data_scadenza: '',\n    priorita: 'NORMALE',\n    note_capo_cantiere: ''\n  });\n\n  // Gestione responsabili\n  const [responsabiliDisponibili, setResponsabiliDisponibili] = useState([]);\n  const [loadingResponsabili, setLoadingResponsabili] = useState(false);\n  const steps = ['Tipo Comanda', 'Selezione Cavi', 'Dettagli Comanda'];\n\n  // Carica cavi disponibili quando cambia il tipo comanda\n  useEffect(() => {\n    if (open && tipoComanda) {\n      loadCaviDisponibili();\n    }\n  }, [open, tipoComanda]);\n\n  // Carica responsabili disponibili quando si apre il dialog\n  useEffect(() => {\n    if (open && cantiereId) {\n      loadResponsabiliDisponibili();\n    }\n  }, [open, cantiereId]);\n\n  // Se ci sono cavi preselezionati, salta al passo dei dettagli\n  useEffect(() => {\n    if (open && caviPreselezionati.length > 0 && tipoComandaPreselezionato) {\n      setActiveStep(2); // Vai direttamente ai dettagli\n    }\n  }, [open, caviPreselezionati, tipoComandaPreselezionato]);\n  const loadCaviDisponibili = async () => {\n    try {\n      setLoadingCavi(true);\n      const response = await comandeService.getCaviDisponibili(cantiereId, tipoComanda);\n      setCaviDisponibili(response.cavi_disponibili || []);\n      setCaviSelezionati([]); // Reset selezione\n      setError(null);\n    } catch (err) {\n      console.error('Errore nel caricamento dei cavi:', err);\n      setError('Errore nel caricamento dei cavi disponibili');\n    } finally {\n      setLoadingCavi(false);\n    }\n  };\n  const loadResponsabiliDisponibili = async () => {\n    try {\n      setLoadingResponsabili(true);\n      const responsabili = await responsabiliService.getResponsabiliCantiere(cantiereId);\n      setResponsabiliDisponibili(responsabili || []);\n    } catch (err) {\n      console.error('Errore nel caricamento dei responsabili:', err);\n      // Non mostrare errore per i responsabili, è opzionale\n    } finally {\n      setLoadingResponsabili(false);\n    }\n  };\n  const handleNext = () => {\n    if (activeStep === 1 && caviSelezionati.length === 0) {\n      setError('Seleziona almeno un cavo');\n      return;\n    }\n    if (activeStep === 2) {\n      if (!formData.responsabile.trim()) {\n        setError('Il nome del responsabile è obbligatorio');\n        return;\n      }\n\n      // Se è un responsabile nuovo (non nell'elenco), richiedi almeno email o telefono\n      const responsabileEsistente = responsabiliDisponibili.find(r => r.nome_responsabile === formData.responsabile);\n      if (!responsabileEsistente && !formData.responsabile_email && !formData.responsabile_telefono) {\n        setError('Per un nuovo responsabile, inserisci almeno email o telefono');\n        return;\n      }\n    }\n    setError(null);\n    setActiveStep(prevStep => prevStep + 1);\n  };\n  const handleBack = () => {\n    setActiveStep(prevStep => prevStep - 1);\n  };\n  const handleCavoToggle = cavo => {\n    const isSelected = caviSelezionati.some(c => c.id_cavo === cavo.id_cavo);\n    if (isSelected) {\n      setCaviSelezionati(caviSelezionati.filter(c => c.id_cavo !== cavo.id_cavo));\n    } else {\n      setCaviSelezionati([...caviSelezionati, cavo]);\n    }\n  };\n  const handleSubmit = async () => {\n    try {\n      setLoading(true);\n      const comandaData = {\n        tipo_comanda: tipoComanda,\n        descrizione: formData.descrizione,\n        responsabile: formData.responsabile,\n        responsabile_email: formData.responsabile_email || null,\n        responsabile_telefono: formData.responsabile_telefono || null,\n        data_scadenza: formData.data_scadenza || null,\n        priorita: formData.priorita,\n        note_capo_cantiere: formData.note_capo_cantiere\n      };\n      const listaIdCavi = caviSelezionati.map(c => c.id_cavo);\n      const response = await comandeService.createComandaConCavi(cantiereId, comandaData, listaIdCavi);\n      onSuccess && onSuccess(response);\n      handleClose();\n    } catch (err) {\n      console.error('Errore nella creazione:', err);\n      setError('Errore nella creazione della comanda');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleClose = () => {\n    setActiveStep(0);\n    setTipoComanda(tipoComandaPreselezionato || 'POSA');\n    setCaviDisponibili([]);\n    setCaviSelezionati(caviPreselezionati || []);\n    setFormData({\n      descrizione: '',\n      responsabile: '',\n      responsabile_email: '',\n      responsabile_telefono: '',\n      data_scadenza: '',\n      priorita: 'NORMALE',\n      note_capo_cantiere: ''\n    });\n    setError(null);\n    onClose && onClose();\n  };\n  const getTipoComandaLabel = tipo => {\n    switch (tipo) {\n      case 'POSA':\n        return 'Posa';\n      case 'COLLEGAMENTO_PARTENZA':\n        return 'Collegamento Partenza';\n      case 'COLLEGAMENTO_ARRIVO':\n        return 'Collegamento Arrivo';\n      case 'CERTIFICAZIONE':\n        return 'Certificazione';\n      case 'TESTING':\n        return 'Testing';\n      default:\n        return tipo;\n    }\n  };\n  const renderStepContent = () => {\n    switch (activeStep) {\n      case 0:\n        return /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Seleziona il tipo di comanda\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            select: true,\n            label: \"Tipo Comanda\",\n            value: tipoComanda,\n            onChange: e => setTipoComanda(e.target.value),\n            margin: \"normal\",\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"POSA\",\n              children: \"Posa\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"COLLEGAMENTO_PARTENZA\",\n              children: \"Collegamento Partenza\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"COLLEGAMENTO_ARRIVO\",\n              children: \"Collegamento Arrivo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"CERTIFICAZIONE\",\n              children: \"Certificazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"TESTING\",\n              children: \"Testing\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            sx: {\n              mt: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: getTipoComandaLabel(tipoComanda)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 15\n            }, this), \":\", tipoComanda === 'POSA' && ' Comanda per la posa fisica dei cavi', tipoComanda === 'COLLEGAMENTO_PARTENZA' && ' Comanda per il collegamento lato partenza', tipoComanda === 'COLLEGAMENTO_ARRIVO' && ' Comanda per il collegamento lato arrivo', tipoComanda === 'CERTIFICAZIONE' && ' Comanda per la certificazione dei cavi', tipoComanda === 'TESTING' && ' Comanda per test e certificazione']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 11\n        }, this);\n      case 1:\n        return /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: [\"Seleziona i cavi per la comanda di \", getTipoComandaLabel(tipoComanda)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 13\n          }, this), loadingCavi ? /*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            justifyContent: \"center\",\n            p: 3,\n            children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              mb: 2,\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                icon: /*#__PURE__*/_jsxDEV(CableIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 270,\n                  columnNumber: 27\n                }, this),\n                label: `${caviSelezionati.length} cavi selezionati di ${caviDisponibili.length} disponibili`,\n                color: caviSelezionati.length > 0 ? 'primary' : 'default'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n              component: Paper,\n              sx: {\n                maxHeight: 400\n              },\n              children: /*#__PURE__*/_jsxDEV(Table, {\n                stickyHeader: true,\n                children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                  children: /*#__PURE__*/_jsxDEV(TableRow, {\n                    children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                      padding: \"checkbox\",\n                      children: \"Sel.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 280,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"ID Cavo\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 281,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Tipologia\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 282,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Sezione\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 283,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Metri\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 284,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Partenza\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 285,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Arrivo\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 286,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 279,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                  children: caviDisponibili.map(cavo => {\n                    const isSelected = caviSelezionati.some(c => c.id_cavo === cavo.id_cavo);\n                    return /*#__PURE__*/_jsxDEV(TableRow, {\n                      hover: true,\n                      onClick: () => handleCavoToggle(cavo),\n                      sx: {\n                        cursor: 'pointer'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                        padding: \"checkbox\",\n                        children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                          checked: isSelected\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 300,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 299,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                        children: cavo.id_cavo\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 302,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                        children: cavo.tipologia\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 303,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                        children: cavo.sezione\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 304,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                        children: cavo.metri_teorici\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 305,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                        children: cavo.ubicazione_partenza\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 306,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                        children: cavo.ubicazione_arrivo\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 307,\n                        columnNumber: 29\n                      }, this)]\n                    }, cavo.id_cavo, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 293,\n                      columnNumber: 27\n                    }, this);\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 17\n            }, this), caviDisponibili.length === 0 && /*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"warning\",\n              sx: {\n                mt: 2\n              },\n              children: \"Nessun cavo disponibile per il tipo di comanda selezionato.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this);\n      case 2:\n        return /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Dettagli della comanda\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(Autocomplete, {\n                fullWidth: true,\n                freeSolo: true,\n                options: responsabiliDisponibili.map(r => r.nome_responsabile),\n                value: formData.responsabile,\n                onChange: (event, newValue) => {\n                  const responsabile = responsabiliDisponibili.find(r => r.nome_responsabile === newValue);\n                  if (responsabile) {\n                    setFormData({\n                      ...formData,\n                      responsabile: newValue || '',\n                      responsabile_email: responsabile.email || '',\n                      responsabile_telefono: responsabile.telefono || ''\n                    });\n                  } else {\n                    setFormData({\n                      ...formData,\n                      responsabile: newValue || ''\n                    });\n                  }\n                },\n                onInputChange: (event, newInputValue) => {\n                  setFormData({\n                    ...formData,\n                    responsabile: newInputValue\n                  });\n                },\n                renderInput: params => /*#__PURE__*/_jsxDEV(TextField, {\n                  ...params,\n                  label: \"Nome Responsabile\",\n                  margin: \"normal\",\n                  required: true,\n                  helperText: \"Chi eseguir\\xE0 il lavoro (obbligatorio)\",\n                  InputProps: {\n                    ...params.InputProps,\n                    endAdornment: /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [loadingResponsabili ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                        color: \"inherit\",\n                        size: 20\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 366,\n                        columnNumber: 52\n                      }, this) : null, params.InputProps.endAdornment]\n                    }, void 0, true)\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 356,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Email Responsabile\",\n                type: \"email\",\n                value: formData.responsabile_email,\n                onChange: e => setFormData({\n                  ...formData,\n                  responsabile_email: e.target.value\n                }),\n                margin: \"normal\",\n                helperText: \"Email per notifiche (opzionale)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 377,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Telefono Responsabile\",\n                value: formData.responsabile_telefono,\n                onChange: e => setFormData({\n                  ...formData,\n                  responsabile_telefono: e.target.value\n                }),\n                margin: \"normal\",\n                helperText: \"Numero per SMS (opzionale)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 389,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                select: true,\n                label: \"Priorit\\xE0\",\n                value: formData.priorita,\n                onChange: e => setFormData({\n                  ...formData,\n                  priorita: e.target.value\n                }),\n                margin: \"normal\",\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"BASSA\",\n                  children: \"Bassa\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 408,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"NORMALE\",\n                  children: \"Normale\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 409,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"ALTA\",\n                  children: \"Alta\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 410,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"URGENTE\",\n                  children: \"Urgente\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 411,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Data Scadenza\",\n                type: \"date\",\n                value: formData.data_scadenza,\n                onChange: e => setFormData({\n                  ...formData,\n                  data_scadenza: e.target.value\n                }),\n                margin: \"normal\",\n                InputLabelProps: {\n                  shrink: true\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Descrizione\",\n            value: formData.descrizione,\n            onChange: e => setFormData({\n              ...formData,\n              descrizione: e.target.value\n            }),\n            margin: \"normal\",\n            multiline: true,\n            rows: 2,\n            helperText: \"Descrizione della comanda (opzionale)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Note per il Responsabile\",\n            value: formData.note_capo_cantiere,\n            onChange: e => setFormData({\n              ...formData,\n              note_capo_cantiere: e.target.value\n            }),\n            margin: \"normal\",\n            multiline: true,\n            rows: 2,\n            helperText: \"Istruzioni specifiche per il responsabile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 439,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            sx: {\n              mt: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              children: \"Riepilogo Comanda:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 451,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [\"\\u2022 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Tipo:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 19\n              }, this), \" \", getTipoComandaLabel(tipoComanda), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 76\n              }, this), \"\\u2022 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Cavi selezionati:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 454,\n                columnNumber: 19\n              }, this), \" \", caviSelezionati.length, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 454,\n                columnNumber: 78\n              }, this), \"\\u2022 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Responsabile:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 455,\n                columnNumber: 19\n              }, this), \" \", formData.responsabile || 'Non specificato', /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 455,\n                columnNumber: 94\n              }, this), formData.responsabile_email && `• Email: ${formData.responsabile_email}`, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 456,\n                columnNumber: 91\n              }, this), formData.responsabile_telefono && `• Telefono: ${formData.responsabile_telefono}`]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 11\n        }, this);\n      default:\n        return null;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: handleClose,\n    maxWidth: \"lg\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        alignItems: \"center\",\n        gap: 1,\n        children: [/*#__PURE__*/_jsxDEV(AssignmentIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 472,\n          columnNumber: 11\n        }, this), \"Crea Nuova Comanda con Cavi\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 471,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 470,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          pt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Stepper, {\n          activeStep: activeStep,\n          sx: {\n            mb: 3\n          },\n          children: steps.map(label => /*#__PURE__*/_jsxDEV(Step, {\n            children: /*#__PURE__*/_jsxDEV(StepLabel, {\n              children: label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 482,\n              columnNumber: 17\n            }, this)\n          }, label, false, {\n            fileName: _jsxFileName,\n            lineNumber: 481,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 479,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"error\",\n          sx: {\n            mb: 2\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 488,\n          columnNumber: 13\n        }, this), renderStepContent()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 478,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 477,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      sx: {\n        p: 3,\n        gap: 2\n      },\n      children: activeStep < steps.length - 1 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleClose,\n          variant: \"outlined\",\n          sx: {\n            minWidth: 120\n          },\n          children: \"Chiudi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 500,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleNext,\n          variant: \"contained\",\n          disabled: activeStep === 1 && loadingCavi,\n          sx: {\n            minWidth: 120\n          },\n          children: \"Avanti\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 507,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleClose,\n          variant: \"outlined\",\n          sx: {\n            minWidth: 120\n          },\n          children: \"Chiudi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 518,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSubmit,\n          variant: \"contained\",\n          disabled: loading || caviSelezionati.length === 0 || !formData.responsabile.trim(),\n          startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 529,\n            columnNumber: 36\n          }, this) : /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 529,\n            columnNumber: 69\n          }, this),\n          sx: {\n            minWidth: 120\n          },\n          children: loading ? 'Creazione...' : 'Crea Comanda'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 525,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 497,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 469,\n    columnNumber: 5\n  }, this);\n};\n_s(CreaComandaConCavi, \"uqvnVxMXuiS8DatQuCSSYWSN9VU=\");\n_c = CreaComandaConCavi;\nexport default CreaComandaConCavi;\nvar _c;\n$RefreshReg$(_c, \"CreaComandaConCavi\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "MenuItem", "<PERSON><PERSON>", "CircularProgress", "Stepper", "Step", "<PERSON><PERSON><PERSON><PERSON>", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "Checkbox", "Chip", "Grid", "Autocomplete", "Add", "AddIcon", "Cable", "CableIcon", "Assignment", "AssignmentIcon", "comandeService", "responsabiliService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CreaComandaConCavi", "cantiereId", "open", "onClose", "onSuccess", "tipoComandaPreselezionato", "caviPreselezionati", "_s", "activeStep", "setActiveStep", "loading", "setLoading", "error", "setError", "tipoComanda", "setTipoComanda", "caviDisponibili", "setCaviDisponibili", "caviSelezionati", "setCaviSelezionati", "loadingCavi", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "formData", "setFormData", "descrizione", "responsabile", "responsabile_email", "responsabile_telefono", "data_scadenza", "priorita", "note_capo_cantiere", "responsabiliDisponibili", "setResponsabiliDisponibili", "loadingResponsabili", "setLoadingResponsabili", "steps", "loadCaviDisponibili", "loadResponsabiliDisponibili", "length", "response", "getCaviDisponibili", "cavi_disponibili", "err", "console", "responsabili", "getResponsabiliCantiere", "handleNext", "trim", "responsabileEsistente", "find", "r", "nome_responsabile", "prevStep", "handleBack", "handleCavoToggle", "cavo", "isSelected", "some", "c", "id_cavo", "filter", "handleSubmit", "comandaData", "tipo_comanda", "listaIdCavi", "map", "createComandaConCavi", "handleClose", "getTipoComandaLabel", "tipo", "renderStepContent", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fullWidth", "select", "label", "value", "onChange", "e", "target", "margin", "severity", "sx", "mt", "display", "justifyContent", "p", "mb", "icon", "color", "component", "maxHeight", "<PERSON><PERSON><PERSON><PERSON>", "padding", "hover", "onClick", "cursor", "checked", "tipologia", "sezione", "metri_te<PERSON>ci", "ubicazione_partenza", "ubicazione_arrivo", "container", "spacing", "item", "xs", "freeSolo", "options", "event", "newValue", "email", "telefono", "onInputChange", "newInputValue", "renderInput", "params", "required", "helperText", "InputProps", "endAdornment", "size", "sm", "type", "InputLabelProps", "shrink", "multiline", "rows", "max<PERSON><PERSON><PERSON>", "alignItems", "gap", "pt", "min<PERSON><PERSON><PERSON>", "disabled", "startIcon", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/comande/CreaComandaConCavi.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Card,\n  CardContent,\n  Typo<PERSON>,\n  Button,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  MenuItem,\n  Alert,\n  CircularProgress,\n  Stepper,\n  Step,\n  StepLabel,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  Checkbox,\n  Chip,\n  Grid,\n  Autocomplete\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Cable as CableIcon,\n  Assignment as AssignmentIcon\n} from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport responsabiliService from '../../services/responsabiliService';\n\nconst CreaComandaConCavi = ({\n  cantiereId,\n  open,\n  onClose,\n  onSuccess,\n  tipoComandaPreselezionato = null,\n  caviPreselezionati = []\n}) => {\n  const [activeStep, setActiveStep] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  \n  // Step 1: Selezione tipo comanda\n  const [tipoComanda, setTipoComanda] = useState(tipoComandaPreselezionato || 'POSA');\n  \n  // Step 2: Selezione cavi\n  const [caviDisponibili, setCaviDisponibili] = useState([]);\n  const [caviSelezionati, setCaviSelezionati] = useState(caviPreselezionati || []);\n  const [loadingCavi, setLoadingCavi] = useState(false);\n\n  // Step 3: Dettagli comanda\n  const [formData, setFormData] = useState({\n    descrizione: '',\n    responsabile: '',\n    responsabile_email: '',\n    responsabile_telefono: '',\n    data_scadenza: '',\n    priorita: 'NORMALE',\n    note_capo_cantiere: ''\n  });\n\n  // Gestione responsabili\n  const [responsabiliDisponibili, setResponsabiliDisponibili] = useState([]);\n  const [loadingResponsabili, setLoadingResponsabili] = useState(false);\n\n  const steps = ['Tipo Comanda', 'Selezione Cavi', 'Dettagli Comanda'];\n\n  // Carica cavi disponibili quando cambia il tipo comanda\n  useEffect(() => {\n    if (open && tipoComanda) {\n      loadCaviDisponibili();\n    }\n  }, [open, tipoComanda]);\n\n  // Carica responsabili disponibili quando si apre il dialog\n  useEffect(() => {\n    if (open && cantiereId) {\n      loadResponsabiliDisponibili();\n    }\n  }, [open, cantiereId]);\n\n  // Se ci sono cavi preselezionati, salta al passo dei dettagli\n  useEffect(() => {\n    if (open && caviPreselezionati.length > 0 && tipoComandaPreselezionato) {\n      setActiveStep(2); // Vai direttamente ai dettagli\n    }\n  }, [open, caviPreselezionati, tipoComandaPreselezionato]);\n\n  const loadCaviDisponibili = async () => {\n    try {\n      setLoadingCavi(true);\n      const response = await comandeService.getCaviDisponibili(cantiereId, tipoComanda);\n      setCaviDisponibili(response.cavi_disponibili || []);\n      setCaviSelezionati([]); // Reset selezione\n      setError(null);\n    } catch (err) {\n      console.error('Errore nel caricamento dei cavi:', err);\n      setError('Errore nel caricamento dei cavi disponibili');\n    } finally {\n      setLoadingCavi(false);\n    }\n  };\n\n  const loadResponsabiliDisponibili = async () => {\n    try {\n      setLoadingResponsabili(true);\n      const responsabili = await responsabiliService.getResponsabiliCantiere(cantiereId);\n      setResponsabiliDisponibili(responsabili || []);\n    } catch (err) {\n      console.error('Errore nel caricamento dei responsabili:', err);\n      // Non mostrare errore per i responsabili, è opzionale\n    } finally {\n      setLoadingResponsabili(false);\n    }\n  };\n\n  const handleNext = () => {\n    if (activeStep === 1 && caviSelezionati.length === 0) {\n      setError('Seleziona almeno un cavo');\n      return;\n    }\n    if (activeStep === 2) {\n      if (!formData.responsabile.trim()) {\n        setError('Il nome del responsabile è obbligatorio');\n        return;\n      }\n\n      // Se è un responsabile nuovo (non nell'elenco), richiedi almeno email o telefono\n      const responsabileEsistente = responsabiliDisponibili.find(r => r.nome_responsabile === formData.responsabile);\n      if (!responsabileEsistente && !formData.responsabile_email && !formData.responsabile_telefono) {\n        setError('Per un nuovo responsabile, inserisci almeno email o telefono');\n        return;\n      }\n    }\n    setError(null);\n    setActiveStep((prevStep) => prevStep + 1);\n  };\n\n  const handleBack = () => {\n    setActiveStep((prevStep) => prevStep - 1);\n  };\n\n  const handleCavoToggle = (cavo) => {\n    const isSelected = caviSelezionati.some(c => c.id_cavo === cavo.id_cavo);\n    if (isSelected) {\n      setCaviSelezionati(caviSelezionati.filter(c => c.id_cavo !== cavo.id_cavo));\n    } else {\n      setCaviSelezionati([...caviSelezionati, cavo]);\n    }\n  };\n\n  const handleSubmit = async () => {\n    try {\n      setLoading(true);\n      \n      const comandaData = {\n        tipo_comanda: tipoComanda,\n        descrizione: formData.descrizione,\n        responsabile: formData.responsabile,\n        responsabile_email: formData.responsabile_email || null,\n        responsabile_telefono: formData.responsabile_telefono || null,\n        data_scadenza: formData.data_scadenza || null,\n        priorita: formData.priorita,\n        note_capo_cantiere: formData.note_capo_cantiere\n      };\n\n      const listaIdCavi = caviSelezionati.map(c => c.id_cavo);\n\n      const response = await comandeService.createComandaConCavi(\n        cantiereId,\n        comandaData,\n        listaIdCavi\n      );\n\n      onSuccess && onSuccess(response);\n      handleClose();\n    } catch (err) {\n      console.error('Errore nella creazione:', err);\n      setError('Errore nella creazione della comanda');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleClose = () => {\n    setActiveStep(0);\n    setTipoComanda(tipoComandaPreselezionato || 'POSA');\n    setCaviDisponibili([]);\n    setCaviSelezionati(caviPreselezionati || []);\n    setFormData({\n      descrizione: '',\n      responsabile: '',\n      responsabile_email: '',\n      responsabile_telefono: '',\n      data_scadenza: '',\n      priorita: 'NORMALE',\n      note_capo_cantiere: ''\n    });\n    setError(null);\n    onClose && onClose();\n  };\n\n  const getTipoComandaLabel = (tipo) => {\n    switch (tipo) {\n      case 'POSA': return 'Posa';\n      case 'COLLEGAMENTO_PARTENZA': return 'Collegamento Partenza';\n      case 'COLLEGAMENTO_ARRIVO': return 'Collegamento Arrivo';\n      case 'CERTIFICAZIONE': return 'Certificazione';\n      case 'TESTING': return 'Testing';\n      default: return tipo;\n    }\n  };\n\n  const renderStepContent = () => {\n    switch (activeStep) {\n      case 0:\n        return (\n          <Box>\n            <Typography variant=\"h6\" gutterBottom>\n              Seleziona il tipo di comanda\n            </Typography>\n            <TextField\n              fullWidth\n              select\n              label=\"Tipo Comanda\"\n              value={tipoComanda}\n              onChange={(e) => setTipoComanda(e.target.value)}\n              margin=\"normal\"\n            >\n              <MenuItem value=\"POSA\">Posa</MenuItem>\n              <MenuItem value=\"COLLEGAMENTO_PARTENZA\">Collegamento Partenza</MenuItem>\n              <MenuItem value=\"COLLEGAMENTO_ARRIVO\">Collegamento Arrivo</MenuItem>\n              <MenuItem value=\"CERTIFICAZIONE\">Certificazione</MenuItem>\n              <MenuItem value=\"TESTING\">Testing</MenuItem>\n            </TextField>\n            <Alert severity=\"info\" sx={{ mt: 2 }}>\n              <strong>{getTipoComandaLabel(tipoComanda)}</strong>: \n              {tipoComanda === 'POSA' && ' Comanda per la posa fisica dei cavi'}\n              {tipoComanda === 'COLLEGAMENTO_PARTENZA' && ' Comanda per il collegamento lato partenza'}\n              {tipoComanda === 'COLLEGAMENTO_ARRIVO' && ' Comanda per il collegamento lato arrivo'}\n              {tipoComanda === 'CERTIFICAZIONE' && ' Comanda per la certificazione dei cavi'}\n              {tipoComanda === 'TESTING' && ' Comanda per test e certificazione'}\n            </Alert>\n          </Box>\n        );\n\n      case 1:\n        return (\n          <Box>\n            <Typography variant=\"h6\" gutterBottom>\n              Seleziona i cavi per la comanda di {getTipoComandaLabel(tipoComanda)}\n            </Typography>\n            \n            {loadingCavi ? (\n              <Box display=\"flex\" justifyContent=\"center\" p={3}>\n                <CircularProgress />\n              </Box>\n            ) : (\n              <>\n                <Box mb={2}>\n                  <Chip \n                    icon={<CableIcon />}\n                    label={`${caviSelezionati.length} cavi selezionati di ${caviDisponibili.length} disponibili`}\n                    color={caviSelezionati.length > 0 ? 'primary' : 'default'}\n                  />\n                </Box>\n                \n                <TableContainer component={Paper} sx={{ maxHeight: 400 }}>\n                  <Table stickyHeader>\n                    <TableHead>\n                      <TableRow>\n                        <TableCell padding=\"checkbox\">Sel.</TableCell>\n                        <TableCell>ID Cavo</TableCell>\n                        <TableCell>Tipologia</TableCell>\n                        <TableCell>Sezione</TableCell>\n                        <TableCell>Metri</TableCell>\n                        <TableCell>Partenza</TableCell>\n                        <TableCell>Arrivo</TableCell>\n                      </TableRow>\n                    </TableHead>\n                    <TableBody>\n                      {caviDisponibili.map((cavo) => {\n                        const isSelected = caviSelezionati.some(c => c.id_cavo === cavo.id_cavo);\n                        return (\n                          <TableRow \n                            key={cavo.id_cavo}\n                            hover\n                            onClick={() => handleCavoToggle(cavo)}\n                            sx={{ cursor: 'pointer' }}\n                          >\n                            <TableCell padding=\"checkbox\">\n                              <Checkbox checked={isSelected} />\n                            </TableCell>\n                            <TableCell>{cavo.id_cavo}</TableCell>\n                            <TableCell>{cavo.tipologia}</TableCell>\n                            <TableCell>{cavo.sezione}</TableCell>\n                            <TableCell>{cavo.metri_teorici}</TableCell>\n                            <TableCell>{cavo.ubicazione_partenza}</TableCell>\n                            <TableCell>{cavo.ubicazione_arrivo}</TableCell>\n                          </TableRow>\n                        );\n                      })}\n                    </TableBody>\n                  </Table>\n                </TableContainer>\n\n                {caviDisponibili.length === 0 && (\n                  <Alert severity=\"warning\" sx={{ mt: 2 }}>\n                    Nessun cavo disponibile per il tipo di comanda selezionato.\n                  </Alert>\n                )}\n              </>\n            )}\n          </Box>\n        );\n\n      case 2:\n        return (\n          <Box>\n            <Typography variant=\"h6\" gutterBottom>\n              Dettagli della comanda\n            </Typography>\n            \n            <Grid container spacing={2}>\n              <Grid item xs={12}>\n                <Autocomplete\n                  fullWidth\n                  freeSolo\n                  options={responsabiliDisponibili.map(r => r.nome_responsabile)}\n                  value={formData.responsabile}\n                  onChange={(event, newValue) => {\n                    const responsabile = responsabiliDisponibili.find(r => r.nome_responsabile === newValue);\n                    if (responsabile) {\n                      setFormData({\n                        ...formData,\n                        responsabile: newValue || '',\n                        responsabile_email: responsabile.email || '',\n                        responsabile_telefono: responsabile.telefono || ''\n                      });\n                    } else {\n                      setFormData({ ...formData, responsabile: newValue || '' });\n                    }\n                  }}\n                  onInputChange={(event, newInputValue) => {\n                    setFormData({ ...formData, responsabile: newInputValue });\n                  }}\n                  renderInput={(params) => (\n                    <TextField\n                      {...params}\n                      label=\"Nome Responsabile\"\n                      margin=\"normal\"\n                      required\n                      helperText=\"Chi eseguirà il lavoro (obbligatorio)\"\n                      InputProps={{\n                        ...params.InputProps,\n                        endAdornment: (\n                          <>\n                            {loadingResponsabili ? <CircularProgress color=\"inherit\" size={20} /> : null}\n                            {params.InputProps.endAdornment}\n                          </>\n                        ),\n                      }}\n                    />\n                  )}\n                />\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  fullWidth\n                  label=\"Email Responsabile\"\n                  type=\"email\"\n                  value={formData.responsabile_email}\n                  onChange={(e) => setFormData({ ...formData, responsabile_email: e.target.value })}\n                  margin=\"normal\"\n                  helperText=\"Email per notifiche (opzionale)\"\n                />\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  fullWidth\n                  label=\"Telefono Responsabile\"\n                  value={formData.responsabile_telefono}\n                  onChange={(e) => setFormData({ ...formData, responsabile_telefono: e.target.value })}\n                  margin=\"normal\"\n                  helperText=\"Numero per SMS (opzionale)\"\n                />\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  fullWidth\n                  select\n                  label=\"Priorità\"\n                  value={formData.priorita}\n                  onChange={(e) => setFormData({ ...formData, priorita: e.target.value })}\n                  margin=\"normal\"\n                >\n                  <MenuItem value=\"BASSA\">Bassa</MenuItem>\n                  <MenuItem value=\"NORMALE\">Normale</MenuItem>\n                  <MenuItem value=\"ALTA\">Alta</MenuItem>\n                  <MenuItem value=\"URGENTE\">Urgente</MenuItem>\n                </TextField>\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  fullWidth\n                  label=\"Data Scadenza\"\n                  type=\"date\"\n                  value={formData.data_scadenza}\n                  onChange={(e) => setFormData({ ...formData, data_scadenza: e.target.value })}\n                  margin=\"normal\"\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n            </Grid>\n\n            <TextField\n              fullWidth\n              label=\"Descrizione\"\n              value={formData.descrizione}\n              onChange={(e) => setFormData({ ...formData, descrizione: e.target.value })}\n              margin=\"normal\"\n              multiline\n              rows={2}\n              helperText=\"Descrizione della comanda (opzionale)\"\n            />\n\n            <TextField\n              fullWidth\n              label=\"Note per il Responsabile\"\n              value={formData.note_capo_cantiere}\n              onChange={(e) => setFormData({ ...formData, note_capo_cantiere: e.target.value })}\n              margin=\"normal\"\n              multiline\n              rows={2}\n              helperText=\"Istruzioni specifiche per il responsabile\"\n            />\n\n            <Alert severity=\"info\" sx={{ mt: 2 }}>\n              <Typography variant=\"subtitle2\">Riepilogo Comanda:</Typography>\n              <Typography variant=\"body2\">\n                • <strong>Tipo:</strong> {getTipoComandaLabel(tipoComanda)}<br/>\n                • <strong>Cavi selezionati:</strong> {caviSelezionati.length}<br/>\n                • <strong>Responsabile:</strong> {formData.responsabile || 'Non specificato'}<br/>\n                {formData.responsabile_email && `• Email: ${formData.responsabile_email}`}<br/>\n                {formData.responsabile_telefono && `• Telefono: ${formData.responsabile_telefono}`}\n              </Typography>\n            </Alert>\n          </Box>\n        );\n\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <Dialog open={open} onClose={handleClose} maxWidth=\"lg\" fullWidth>\n      <DialogTitle>\n        <Box display=\"flex\" alignItems=\"center\" gap={1}>\n          <AssignmentIcon />\n          Crea Nuova Comanda con Cavi\n        </Box>\n      </DialogTitle>\n      \n      <DialogContent>\n        <Box sx={{ pt: 2 }}>\n          <Stepper activeStep={activeStep} sx={{ mb: 3 }}>\n            {steps.map((label) => (\n              <Step key={label}>\n                <StepLabel>{label}</StepLabel>\n              </Step>\n            ))}\n          </Stepper>\n\n          {error && (\n            <Alert severity=\"error\" sx={{ mb: 2 }}>\n              {error}\n            </Alert>\n          )}\n\n          {renderStepContent()}\n        </Box>\n      </DialogContent>\n\n      <DialogActions sx={{ p: 3, gap: 2 }}>\n        {activeStep < steps.length - 1 ? (\n          <>\n            <Button\n              onClick={handleClose}\n              variant=\"outlined\"\n              sx={{ minWidth: 120 }}\n            >\n              Chiudi\n            </Button>\n            <Button\n              onClick={handleNext}\n              variant=\"contained\"\n              disabled={activeStep === 1 && loadingCavi}\n              sx={{ minWidth: 120 }}\n            >\n              Avanti\n            </Button>\n          </>\n        ) : (\n          <>\n            <Button\n              onClick={handleClose}\n              variant=\"outlined\"\n              sx={{ minWidth: 120 }}\n            >\n              Chiudi\n            </Button>\n            <Button\n              onClick={handleSubmit}\n              variant=\"contained\"\n              disabled={loading || caviSelezionati.length === 0 || !formData.responsabile.trim()}\n              startIcon={loading ? <CircularProgress size={20} /> : <AddIcon />}\n              sx={{ minWidth: 120 }}\n            >\n              {loading ? 'Creazione...' : 'Crea Comanda'}\n            </Button>\n          </>\n        )}\n      </DialogActions>\n    </Dialog>\n  );\n};\n\nexport default CreaComandaConCavi;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,gBAAgB,EAChBC,OAAO,EACPC,IAAI,EACJC,SAAS,EACTC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,QAAQ,EACRC,IAAI,EACJC,IAAI,EACJC,YAAY,QACP,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,KAAK,IAAIC,SAAS,EAClBC,UAAU,IAAIC,cAAc,QACvB,qBAAqB;AAC5B,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,mBAAmB,MAAM,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErE,MAAMC,kBAAkB,GAAGA,CAAC;EAC1BC,UAAU;EACVC,IAAI;EACJC,OAAO;EACPC,SAAS;EACTC,yBAAyB,GAAG,IAAI;EAChCC,kBAAkB,GAAG;AACvB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGlD,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACmD,OAAO,EAAEC,UAAU,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACqD,KAAK,EAAEC,QAAQ,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACA,MAAM,CAACuD,WAAW,EAAEC,cAAc,CAAC,GAAGxD,QAAQ,CAAC8C,yBAAyB,IAAI,MAAM,CAAC;;EAEnF;EACA,MAAM,CAACW,eAAe,EAAEC,kBAAkB,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC2D,eAAe,EAAEC,kBAAkB,CAAC,GAAG5D,QAAQ,CAAC+C,kBAAkB,IAAI,EAAE,CAAC;EAChF,MAAM,CAACc,WAAW,EAAEC,cAAc,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;;EAErD;EACA,MAAM,CAAC+D,QAAQ,EAAEC,WAAW,CAAC,GAAGhE,QAAQ,CAAC;IACvCiE,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE,EAAE;IAChBC,kBAAkB,EAAE,EAAE;IACtBC,qBAAqB,EAAE,EAAE;IACzBC,aAAa,EAAE,EAAE;IACjBC,QAAQ,EAAE,SAAS;IACnBC,kBAAkB,EAAE;EACtB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,uBAAuB,EAAEC,0BAA0B,CAAC,GAAGzE,QAAQ,CAAC,EAAE,CAAC;EAC1E,MAAM,CAAC0E,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG3E,QAAQ,CAAC,KAAK,CAAC;EAErE,MAAM4E,KAAK,GAAG,CAAC,cAAc,EAAE,gBAAgB,EAAE,kBAAkB,CAAC;;EAEpE;EACA3E,SAAS,CAAC,MAAM;IACd,IAAI0C,IAAI,IAAIY,WAAW,EAAE;MACvBsB,mBAAmB,CAAC,CAAC;IACvB;EACF,CAAC,EAAE,CAAClC,IAAI,EAAEY,WAAW,CAAC,CAAC;;EAEvB;EACAtD,SAAS,CAAC,MAAM;IACd,IAAI0C,IAAI,IAAID,UAAU,EAAE;MACtBoC,2BAA2B,CAAC,CAAC;IAC/B;EACF,CAAC,EAAE,CAACnC,IAAI,EAAED,UAAU,CAAC,CAAC;;EAEtB;EACAzC,SAAS,CAAC,MAAM;IACd,IAAI0C,IAAI,IAAII,kBAAkB,CAACgC,MAAM,GAAG,CAAC,IAAIjC,yBAAyB,EAAE;MACtEI,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAACP,IAAI,EAAEI,kBAAkB,EAAED,yBAAyB,CAAC,CAAC;EAEzD,MAAM+B,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACFf,cAAc,CAAC,IAAI,CAAC;MACpB,MAAMkB,QAAQ,GAAG,MAAM7C,cAAc,CAAC8C,kBAAkB,CAACvC,UAAU,EAAEa,WAAW,CAAC;MACjFG,kBAAkB,CAACsB,QAAQ,CAACE,gBAAgB,IAAI,EAAE,CAAC;MACnDtB,kBAAkB,CAAC,EAAE,CAAC,CAAC,CAAC;MACxBN,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAO6B,GAAG,EAAE;MACZC,OAAO,CAAC/B,KAAK,CAAC,kCAAkC,EAAE8B,GAAG,CAAC;MACtD7B,QAAQ,CAAC,6CAA6C,CAAC;IACzD,CAAC,SAAS;MACRQ,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAMgB,2BAA2B,GAAG,MAAAA,CAAA,KAAY;IAC9C,IAAI;MACFH,sBAAsB,CAAC,IAAI,CAAC;MAC5B,MAAMU,YAAY,GAAG,MAAMjD,mBAAmB,CAACkD,uBAAuB,CAAC5C,UAAU,CAAC;MAClF+B,0BAA0B,CAACY,YAAY,IAAI,EAAE,CAAC;IAChD,CAAC,CAAC,OAAOF,GAAG,EAAE;MACZC,OAAO,CAAC/B,KAAK,CAAC,0CAA0C,EAAE8B,GAAG,CAAC;MAC9D;IACF,CAAC,SAAS;MACRR,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;EAED,MAAMY,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAItC,UAAU,KAAK,CAAC,IAAIU,eAAe,CAACoB,MAAM,KAAK,CAAC,EAAE;MACpDzB,QAAQ,CAAC,0BAA0B,CAAC;MACpC;IACF;IACA,IAAIL,UAAU,KAAK,CAAC,EAAE;MACpB,IAAI,CAACc,QAAQ,CAACG,YAAY,CAACsB,IAAI,CAAC,CAAC,EAAE;QACjClC,QAAQ,CAAC,yCAAyC,CAAC;QACnD;MACF;;MAEA;MACA,MAAMmC,qBAAqB,GAAGjB,uBAAuB,CAACkB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,iBAAiB,KAAK7B,QAAQ,CAACG,YAAY,CAAC;MAC9G,IAAI,CAACuB,qBAAqB,IAAI,CAAC1B,QAAQ,CAACI,kBAAkB,IAAI,CAACJ,QAAQ,CAACK,qBAAqB,EAAE;QAC7Fd,QAAQ,CAAC,8DAA8D,CAAC;QACxE;MACF;IACF;IACAA,QAAQ,CAAC,IAAI,CAAC;IACdJ,aAAa,CAAE2C,QAAQ,IAAKA,QAAQ,GAAG,CAAC,CAAC;EAC3C,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB5C,aAAa,CAAE2C,QAAQ,IAAKA,QAAQ,GAAG,CAAC,CAAC;EAC3C,CAAC;EAED,MAAME,gBAAgB,GAAIC,IAAI,IAAK;IACjC,MAAMC,UAAU,GAAGtC,eAAe,CAACuC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAKJ,IAAI,CAACI,OAAO,CAAC;IACxE,IAAIH,UAAU,EAAE;MACdrC,kBAAkB,CAACD,eAAe,CAAC0C,MAAM,CAACF,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAKJ,IAAI,CAACI,OAAO,CAAC,CAAC;IAC7E,CAAC,MAAM;MACLxC,kBAAkB,CAAC,CAAC,GAAGD,eAAe,EAAEqC,IAAI,CAAC,CAAC;IAChD;EACF,CAAC;EAED,MAAMM,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFlD,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAMmD,WAAW,GAAG;QAClBC,YAAY,EAAEjD,WAAW;QACzBU,WAAW,EAAEF,QAAQ,CAACE,WAAW;QACjCC,YAAY,EAAEH,QAAQ,CAACG,YAAY;QACnCC,kBAAkB,EAAEJ,QAAQ,CAACI,kBAAkB,IAAI,IAAI;QACvDC,qBAAqB,EAAEL,QAAQ,CAACK,qBAAqB,IAAI,IAAI;QAC7DC,aAAa,EAAEN,QAAQ,CAACM,aAAa,IAAI,IAAI;QAC7CC,QAAQ,EAAEP,QAAQ,CAACO,QAAQ;QAC3BC,kBAAkB,EAAER,QAAQ,CAACQ;MAC/B,CAAC;MAED,MAAMkC,WAAW,GAAG9C,eAAe,CAAC+C,GAAG,CAACP,CAAC,IAAIA,CAAC,CAACC,OAAO,CAAC;MAEvD,MAAMpB,QAAQ,GAAG,MAAM7C,cAAc,CAACwE,oBAAoB,CACxDjE,UAAU,EACV6D,WAAW,EACXE,WACF,CAAC;MAED5D,SAAS,IAAIA,SAAS,CAACmC,QAAQ,CAAC;MAChC4B,WAAW,CAAC,CAAC;IACf,CAAC,CAAC,OAAOzB,GAAG,EAAE;MACZC,OAAO,CAAC/B,KAAK,CAAC,yBAAyB,EAAE8B,GAAG,CAAC;MAC7C7B,QAAQ,CAAC,sCAAsC,CAAC;IAClD,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMwD,WAAW,GAAGA,CAAA,KAAM;IACxB1D,aAAa,CAAC,CAAC,CAAC;IAChBM,cAAc,CAACV,yBAAyB,IAAI,MAAM,CAAC;IACnDY,kBAAkB,CAAC,EAAE,CAAC;IACtBE,kBAAkB,CAACb,kBAAkB,IAAI,EAAE,CAAC;IAC5CiB,WAAW,CAAC;MACVC,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,EAAE;MAChBC,kBAAkB,EAAE,EAAE;MACtBC,qBAAqB,EAAE,EAAE;MACzBC,aAAa,EAAE,EAAE;MACjBC,QAAQ,EAAE,SAAS;MACnBC,kBAAkB,EAAE;IACtB,CAAC,CAAC;IACFjB,QAAQ,CAAC,IAAI,CAAC;IACdV,OAAO,IAAIA,OAAO,CAAC,CAAC;EACtB,CAAC;EAED,MAAMiE,mBAAmB,GAAIC,IAAI,IAAK;IACpC,QAAQA,IAAI;MACV,KAAK,MAAM;QAAE,OAAO,MAAM;MAC1B,KAAK,uBAAuB;QAAE,OAAO,uBAAuB;MAC5D,KAAK,qBAAqB;QAAE,OAAO,qBAAqB;MACxD,KAAK,gBAAgB;QAAE,OAAO,gBAAgB;MAC9C,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC;QAAS,OAAOA,IAAI;IACtB;EACF,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,QAAQ9D,UAAU;MAChB,KAAK,CAAC;QACJ,oBACEX,OAAA,CAACpC,GAAG;UAAA8G,QAAA,gBACF1E,OAAA,CAACjC,UAAU;YAAC4G,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAF,QAAA,EAAC;UAEtC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbhF,OAAA,CAAC3B,SAAS;YACR4G,SAAS;YACTC,MAAM;YACNC,KAAK,EAAC,cAAc;YACpBC,KAAK,EAAEnE,WAAY;YACnBoE,QAAQ,EAAGC,CAAC,IAAKpE,cAAc,CAACoE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAChDI,MAAM,EAAC,QAAQ;YAAAd,QAAA,gBAEf1E,OAAA,CAAC1B,QAAQ;cAAC8G,KAAK,EAAC,MAAM;cAAAV,QAAA,EAAC;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACtChF,OAAA,CAAC1B,QAAQ;cAAC8G,KAAK,EAAC,uBAAuB;cAAAV,QAAA,EAAC;YAAqB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACxEhF,OAAA,CAAC1B,QAAQ;cAAC8G,KAAK,EAAC,qBAAqB;cAAAV,QAAA,EAAC;YAAmB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACpEhF,OAAA,CAAC1B,QAAQ;cAAC8G,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC1DhF,OAAA,CAAC1B,QAAQ;cAAC8G,KAAK,EAAC,SAAS;cAAAV,QAAA,EAAC;YAAO;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACZhF,OAAA,CAACzB,KAAK;YAACkH,QAAQ,EAAC,MAAM;YAACC,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAjB,QAAA,gBACnC1E,OAAA;cAAA0E,QAAA,EAASH,mBAAmB,CAACtD,WAAW;YAAC;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,KACnD,EAAC/D,WAAW,KAAK,MAAM,IAAI,sCAAsC,EAChEA,WAAW,KAAK,uBAAuB,IAAI,4CAA4C,EACvFA,WAAW,KAAK,qBAAqB,IAAI,0CAA0C,EACnFA,WAAW,KAAK,gBAAgB,IAAI,yCAAyC,EAC7EA,WAAW,KAAK,SAAS,IAAI,oCAAoC;UAAA;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAGV,KAAK,CAAC;QACJ,oBACEhF,OAAA,CAACpC,GAAG;UAAA8G,QAAA,gBACF1E,OAAA,CAACjC,UAAU;YAAC4G,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAF,QAAA,GAAC,qCACD,EAACH,mBAAmB,CAACtD,WAAW,CAAC;UAAA;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC,EAEZzD,WAAW,gBACVvB,OAAA,CAACpC,GAAG;YAACgI,OAAO,EAAC,MAAM;YAACC,cAAc,EAAC,QAAQ;YAACC,CAAC,EAAE,CAAE;YAAApB,QAAA,eAC/C1E,OAAA,CAACxB,gBAAgB;cAAAqG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,gBAENhF,OAAA,CAAAE,SAAA;YAAAwE,QAAA,gBACE1E,OAAA,CAACpC,GAAG;cAACmI,EAAE,EAAE,CAAE;cAAArB,QAAA,eACT1E,OAAA,CAACZ,IAAI;gBACH4G,IAAI,eAAEhG,OAAA,CAACN,SAAS;kBAAAmF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACpBG,KAAK,EAAE,GAAG9D,eAAe,CAACoB,MAAM,wBAAwBtB,eAAe,CAACsB,MAAM,cAAe;gBAC7FwD,KAAK,EAAE5E,eAAe,CAACoB,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG;cAAU;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENhF,OAAA,CAACjB,cAAc;cAACmH,SAAS,EAAEhH,KAAM;cAACwG,EAAE,EAAE;gBAAES,SAAS,EAAE;cAAI,CAAE;cAAAzB,QAAA,eACvD1E,OAAA,CAACpB,KAAK;gBAACwH,YAAY;gBAAA1B,QAAA,gBACjB1E,OAAA,CAAChB,SAAS;kBAAA0F,QAAA,eACR1E,OAAA,CAACf,QAAQ;oBAAAyF,QAAA,gBACP1E,OAAA,CAAClB,SAAS;sBAACuH,OAAO,EAAC,UAAU;sBAAA3B,QAAA,EAAC;oBAAI;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC9ChF,OAAA,CAAClB,SAAS;sBAAA4F,QAAA,EAAC;oBAAO;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC9BhF,OAAA,CAAClB,SAAS;sBAAA4F,QAAA,EAAC;oBAAS;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAChChF,OAAA,CAAClB,SAAS;sBAAA4F,QAAA,EAAC;oBAAO;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC9BhF,OAAA,CAAClB,SAAS;sBAAA4F,QAAA,EAAC;oBAAK;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC5BhF,OAAA,CAAClB,SAAS;sBAAA4F,QAAA,EAAC;oBAAQ;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC/BhF,OAAA,CAAClB,SAAS;sBAAA4F,QAAA,EAAC;oBAAM;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACZhF,OAAA,CAACnB,SAAS;kBAAA6F,QAAA,EACPvD,eAAe,CAACiD,GAAG,CAAEV,IAAI,IAAK;oBAC7B,MAAMC,UAAU,GAAGtC,eAAe,CAACuC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAKJ,IAAI,CAACI,OAAO,CAAC;oBACxE,oBACE9D,OAAA,CAACf,QAAQ;sBAEPqH,KAAK;sBACLC,OAAO,EAAEA,CAAA,KAAM9C,gBAAgB,CAACC,IAAI,CAAE;sBACtCgC,EAAE,EAAE;wBAAEc,MAAM,EAAE;sBAAU,CAAE;sBAAA9B,QAAA,gBAE1B1E,OAAA,CAAClB,SAAS;wBAACuH,OAAO,EAAC,UAAU;wBAAA3B,QAAA,eAC3B1E,OAAA,CAACb,QAAQ;0BAACsH,OAAO,EAAE9C;wBAAW;0BAAAkB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxB,CAAC,eACZhF,OAAA,CAAClB,SAAS;wBAAA4F,QAAA,EAAEhB,IAAI,CAACI;sBAAO;wBAAAe,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACrChF,OAAA,CAAClB,SAAS;wBAAA4F,QAAA,EAAEhB,IAAI,CAACgD;sBAAS;wBAAA7B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACvChF,OAAA,CAAClB,SAAS;wBAAA4F,QAAA,EAAEhB,IAAI,CAACiD;sBAAO;wBAAA9B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACrChF,OAAA,CAAClB,SAAS;wBAAA4F,QAAA,EAAEhB,IAAI,CAACkD;sBAAa;wBAAA/B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eAC3ChF,OAAA,CAAClB,SAAS;wBAAA4F,QAAA,EAAEhB,IAAI,CAACmD;sBAAmB;wBAAAhC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACjDhF,OAAA,CAAClB,SAAS;wBAAA4F,QAAA,EAAEhB,IAAI,CAACoD;sBAAiB;wBAAAjC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA,GAb1CtB,IAAI,CAACI,OAAO;sBAAAe,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAcT,CAAC;kBAEf,CAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,EAEhB7D,eAAe,CAACsB,MAAM,KAAK,CAAC,iBAC3BzC,OAAA,CAACzB,KAAK;cAACkH,QAAQ,EAAC,SAAS;cAACC,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAAC;YAEzC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CACR;UAAA,eACD,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAGV,KAAK,CAAC;QACJ,oBACEhF,OAAA,CAACpC,GAAG;UAAA8G,QAAA,gBACF1E,OAAA,CAACjC,UAAU;YAAC4G,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAF,QAAA,EAAC;UAEtC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAEbhF,OAAA,CAACX,IAAI;YAAC0H,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAtC,QAAA,gBACzB1E,OAAA,CAACX,IAAI;cAAC4H,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAxC,QAAA,eAChB1E,OAAA,CAACV,YAAY;gBACX2F,SAAS;gBACTkC,QAAQ;gBACRC,OAAO,EAAElF,uBAAuB,CAACkC,GAAG,CAACf,CAAC,IAAIA,CAAC,CAACC,iBAAiB,CAAE;gBAC/D8B,KAAK,EAAE3D,QAAQ,CAACG,YAAa;gBAC7ByD,QAAQ,EAAEA,CAACgC,KAAK,EAAEC,QAAQ,KAAK;kBAC7B,MAAM1F,YAAY,GAAGM,uBAAuB,CAACkB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,iBAAiB,KAAKgE,QAAQ,CAAC;kBACxF,IAAI1F,YAAY,EAAE;oBAChBF,WAAW,CAAC;sBACV,GAAGD,QAAQ;sBACXG,YAAY,EAAE0F,QAAQ,IAAI,EAAE;sBAC5BzF,kBAAkB,EAAED,YAAY,CAAC2F,KAAK,IAAI,EAAE;sBAC5CzF,qBAAqB,EAAEF,YAAY,CAAC4F,QAAQ,IAAI;oBAClD,CAAC,CAAC;kBACJ,CAAC,MAAM;oBACL9F,WAAW,CAAC;sBAAE,GAAGD,QAAQ;sBAAEG,YAAY,EAAE0F,QAAQ,IAAI;oBAAG,CAAC,CAAC;kBAC5D;gBACF,CAAE;gBACFG,aAAa,EAAEA,CAACJ,KAAK,EAAEK,aAAa,KAAK;kBACvChG,WAAW,CAAC;oBAAE,GAAGD,QAAQ;oBAAEG,YAAY,EAAE8F;kBAAc,CAAC,CAAC;gBAC3D,CAAE;gBACFC,WAAW,EAAGC,MAAM,iBAClB5H,OAAA,CAAC3B,SAAS;kBAAA,GACJuJ,MAAM;kBACVzC,KAAK,EAAC,mBAAmB;kBACzBK,MAAM,EAAC,QAAQ;kBACfqC,QAAQ;kBACRC,UAAU,EAAC,0CAAuC;kBAClDC,UAAU,EAAE;oBACV,GAAGH,MAAM,CAACG,UAAU;oBACpBC,YAAY,eACVhI,OAAA,CAAAE,SAAA;sBAAAwE,QAAA,GACGtC,mBAAmB,gBAAGpC,OAAA,CAACxB,gBAAgB;wBAACyH,KAAK,EAAC,SAAS;wBAACgC,IAAI,EAAE;sBAAG;wBAAApD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,GAAG,IAAI,EAC3E4C,MAAM,CAACG,UAAU,CAACC,YAAY;oBAAA,eAC/B;kBAEN;gBAAE;kBAAAnD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cACD;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEPhF,OAAA,CAACX,IAAI;cAAC4H,IAAI;cAACC,EAAE,EAAE,EAAG;cAACgB,EAAE,EAAE,CAAE;cAAAxD,QAAA,eACvB1E,OAAA,CAAC3B,SAAS;gBACR4G,SAAS;gBACTE,KAAK,EAAC,oBAAoB;gBAC1BgD,IAAI,EAAC,OAAO;gBACZ/C,KAAK,EAAE3D,QAAQ,CAACI,kBAAmB;gBACnCwD,QAAQ,EAAGC,CAAC,IAAK5D,WAAW,CAAC;kBAAE,GAAGD,QAAQ;kBAAEI,kBAAkB,EAAEyD,CAAC,CAACC,MAAM,CAACH;gBAAM,CAAC,CAAE;gBAClFI,MAAM,EAAC,QAAQ;gBACfsC,UAAU,EAAC;cAAiC;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEPhF,OAAA,CAACX,IAAI;cAAC4H,IAAI;cAACC,EAAE,EAAE,EAAG;cAACgB,EAAE,EAAE,CAAE;cAAAxD,QAAA,eACvB1E,OAAA,CAAC3B,SAAS;gBACR4G,SAAS;gBACTE,KAAK,EAAC,uBAAuB;gBAC7BC,KAAK,EAAE3D,QAAQ,CAACK,qBAAsB;gBACtCuD,QAAQ,EAAGC,CAAC,IAAK5D,WAAW,CAAC;kBAAE,GAAGD,QAAQ;kBAAEK,qBAAqB,EAAEwD,CAAC,CAACC,MAAM,CAACH;gBAAM,CAAC,CAAE;gBACrFI,MAAM,EAAC,QAAQ;gBACfsC,UAAU,EAAC;cAA4B;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEPhF,OAAA,CAACX,IAAI;cAAC4H,IAAI;cAACC,EAAE,EAAE,EAAG;cAACgB,EAAE,EAAE,CAAE;cAAAxD,QAAA,eACvB1E,OAAA,CAAC3B,SAAS;gBACR4G,SAAS;gBACTC,MAAM;gBACNC,KAAK,EAAC,aAAU;gBAChBC,KAAK,EAAE3D,QAAQ,CAACO,QAAS;gBACzBqD,QAAQ,EAAGC,CAAC,IAAK5D,WAAW,CAAC;kBAAE,GAAGD,QAAQ;kBAAEO,QAAQ,EAAEsD,CAAC,CAACC,MAAM,CAACH;gBAAM,CAAC,CAAE;gBACxEI,MAAM,EAAC,QAAQ;gBAAAd,QAAA,gBAEf1E,OAAA,CAAC1B,QAAQ;kBAAC8G,KAAK,EAAC,OAAO;kBAAAV,QAAA,EAAC;gBAAK;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACxChF,OAAA,CAAC1B,QAAQ;kBAAC8G,KAAK,EAAC,SAAS;kBAAAV,QAAA,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAC5ChF,OAAA,CAAC1B,QAAQ;kBAAC8G,KAAK,EAAC,MAAM;kBAAAV,QAAA,EAAC;gBAAI;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACtChF,OAAA,CAAC1B,QAAQ;kBAAC8G,KAAK,EAAC,SAAS;kBAAAV,QAAA,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAEPhF,OAAA,CAACX,IAAI;cAAC4H,IAAI;cAACC,EAAE,EAAE,EAAG;cAACgB,EAAE,EAAE,CAAE;cAAAxD,QAAA,eACvB1E,OAAA,CAAC3B,SAAS;gBACR4G,SAAS;gBACTE,KAAK,EAAC,eAAe;gBACrBgD,IAAI,EAAC,MAAM;gBACX/C,KAAK,EAAE3D,QAAQ,CAACM,aAAc;gBAC9BsD,QAAQ,EAAGC,CAAC,IAAK5D,WAAW,CAAC;kBAAE,GAAGD,QAAQ;kBAAEM,aAAa,EAAEuD,CAAC,CAACC,MAAM,CAACH;gBAAM,CAAC,CAAE;gBAC7EI,MAAM,EAAC,QAAQ;gBACf4C,eAAe,EAAE;kBAAEC,MAAM,EAAE;gBAAK;cAAE;gBAAAxD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEPhF,OAAA,CAAC3B,SAAS;YACR4G,SAAS;YACTE,KAAK,EAAC,aAAa;YACnBC,KAAK,EAAE3D,QAAQ,CAACE,WAAY;YAC5B0D,QAAQ,EAAGC,CAAC,IAAK5D,WAAW,CAAC;cAAE,GAAGD,QAAQ;cAAEE,WAAW,EAAE2D,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YAC3EI,MAAM,EAAC,QAAQ;YACf8C,SAAS;YACTC,IAAI,EAAE,CAAE;YACRT,UAAU,EAAC;UAAuC;YAAAjD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eAEFhF,OAAA,CAAC3B,SAAS;YACR4G,SAAS;YACTE,KAAK,EAAC,0BAA0B;YAChCC,KAAK,EAAE3D,QAAQ,CAACQ,kBAAmB;YACnCoD,QAAQ,EAAGC,CAAC,IAAK5D,WAAW,CAAC;cAAE,GAAGD,QAAQ;cAAEQ,kBAAkB,EAAEqD,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YAClFI,MAAM,EAAC,QAAQ;YACf8C,SAAS;YACTC,IAAI,EAAE,CAAE;YACRT,UAAU,EAAC;UAA2C;YAAAjD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC,eAEFhF,OAAA,CAACzB,KAAK;YAACkH,QAAQ,EAAC,MAAM;YAACC,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAjB,QAAA,gBACnC1E,OAAA,CAACjC,UAAU;cAAC4G,OAAO,EAAC,WAAW;cAAAD,QAAA,EAAC;YAAkB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/DhF,OAAA,CAACjC,UAAU;cAAC4G,OAAO,EAAC,OAAO;cAAAD,QAAA,GAAC,SACxB,eAAA1E,OAAA;gBAAA0E,QAAA,EAAQ;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACT,mBAAmB,CAACtD,WAAW,CAAC,eAACjB,OAAA;gBAAA6E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,WAC9D,eAAAhF,OAAA;gBAAA0E,QAAA,EAAQ;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC3D,eAAe,CAACoB,MAAM,eAACzC,OAAA;gBAAA6E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,WAChE,eAAAhF,OAAA;gBAAA0E,QAAA,EAAQ;cAAa;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACvD,QAAQ,CAACG,YAAY,IAAI,iBAAiB,eAAC5B,OAAA;gBAAA6E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EACjFvD,QAAQ,CAACI,kBAAkB,IAAI,YAAYJ,QAAQ,CAACI,kBAAkB,EAAE,eAAC7B,OAAA;gBAAA6E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EAC9EvD,QAAQ,CAACK,qBAAqB,IAAI,eAAeL,QAAQ,CAACK,qBAAqB,EAAE;YAAA;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAGV;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,oBACEhF,OAAA,CAAC/B,MAAM;IAACoC,IAAI,EAAEA,IAAK;IAACC,OAAO,EAAEgE,WAAY;IAACkE,QAAQ,EAAC,IAAI;IAACvD,SAAS;IAAAP,QAAA,gBAC/D1E,OAAA,CAAC9B,WAAW;MAAAwG,QAAA,eACV1E,OAAA,CAACpC,GAAG;QAACgI,OAAO,EAAC,MAAM;QAAC6C,UAAU,EAAC,QAAQ;QAACC,GAAG,EAAE,CAAE;QAAAhE,QAAA,gBAC7C1E,OAAA,CAACJ,cAAc;UAAAiF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,+BAEpB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAEdhF,OAAA,CAAC7B,aAAa;MAAAuG,QAAA,eACZ1E,OAAA,CAACpC,GAAG;QAAC8H,EAAE,EAAE;UAAEiD,EAAE,EAAE;QAAE,CAAE;QAAAjE,QAAA,gBACjB1E,OAAA,CAACvB,OAAO;UAACkC,UAAU,EAAEA,UAAW;UAAC+E,EAAE,EAAE;YAAEK,EAAE,EAAE;UAAE,CAAE;UAAArB,QAAA,EAC5CpC,KAAK,CAAC8B,GAAG,CAAEe,KAAK,iBACfnF,OAAA,CAACtB,IAAI;YAAAgG,QAAA,eACH1E,OAAA,CAACrB,SAAS;cAAA+F,QAAA,EAAES;YAAK;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC,GADrBG,KAAK;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEV,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,EAETjE,KAAK,iBACJf,OAAA,CAACzB,KAAK;UAACkH,QAAQ,EAAC,OAAO;UAACC,EAAE,EAAE;YAAEK,EAAE,EAAE;UAAE,CAAE;UAAArB,QAAA,EACnC3D;QAAK;UAAA8D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR,EAEAP,iBAAiB,CAAC,CAAC;MAAA;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAEhBhF,OAAA,CAAC5B,aAAa;MAACsH,EAAE,EAAE;QAAEI,CAAC,EAAE,CAAC;QAAE4C,GAAG,EAAE;MAAE,CAAE;MAAAhE,QAAA,EACjC/D,UAAU,GAAG2B,KAAK,CAACG,MAAM,GAAG,CAAC,gBAC5BzC,OAAA,CAAAE,SAAA;QAAAwE,QAAA,gBACE1E,OAAA,CAAChC,MAAM;UACLuI,OAAO,EAAEjC,WAAY;UACrBK,OAAO,EAAC,UAAU;UAClBe,EAAE,EAAE;YAAEkD,QAAQ,EAAE;UAAI,CAAE;UAAAlE,QAAA,EACvB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACThF,OAAA,CAAChC,MAAM;UACLuI,OAAO,EAAEtD,UAAW;UACpB0B,OAAO,EAAC,WAAW;UACnBkE,QAAQ,EAAElI,UAAU,KAAK,CAAC,IAAIY,WAAY;UAC1CmE,EAAE,EAAE;YAAEkD,QAAQ,EAAE;UAAI,CAAE;UAAAlE,QAAA,EACvB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA,eACT,CAAC,gBAEHhF,OAAA,CAAAE,SAAA;QAAAwE,QAAA,gBACE1E,OAAA,CAAChC,MAAM;UACLuI,OAAO,EAAEjC,WAAY;UACrBK,OAAO,EAAC,UAAU;UAClBe,EAAE,EAAE;YAAEkD,QAAQ,EAAE;UAAI,CAAE;UAAAlE,QAAA,EACvB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACThF,OAAA,CAAChC,MAAM;UACLuI,OAAO,EAAEvC,YAAa;UACtBW,OAAO,EAAC,WAAW;UACnBkE,QAAQ,EAAEhI,OAAO,IAAIQ,eAAe,CAACoB,MAAM,KAAK,CAAC,IAAI,CAAChB,QAAQ,CAACG,YAAY,CAACsB,IAAI,CAAC,CAAE;UACnF4F,SAAS,EAAEjI,OAAO,gBAAGb,OAAA,CAACxB,gBAAgB;YAACyJ,IAAI,EAAE;UAAG;YAAApD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGhF,OAAA,CAACR,OAAO;YAAAqF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAClEU,EAAE,EAAE;YAAEkD,QAAQ,EAAE;UAAI,CAAE;UAAAlE,QAAA,EAErB7D,OAAO,GAAG,cAAc,GAAG;QAAc;UAAAgE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA,eACT;IACH;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACY,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;AAACtE,EAAA,CApfIP,kBAAkB;AAAA4I,EAAA,GAAlB5I,kBAAkB;AAsfxB,eAAeA,kBAAkB;AAAC,IAAA4I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}