{"ast": null, "code": "var _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nconst CaviPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    user\n  } = useAuth();\n\n  // Reindirizza alla pagina di visualizzazione cavi\n  useEffect(() => {\n    console.log('CaviPage - Inizializzazione');\n\n    // Verifica se l'utente è un utente cantiere\n    const isCantieresUser = (user === null || user === void 0 ? void 0 : user.role) === 'cantieri_user';\n    console.log('Utente cantiere:', isCantieresUser);\n\n    // Verifica se c'è un cantiere selezionato nel localStorage\n    const selectedCantiereId = localStorage.getItem('selectedCantiereId');\n    const selectedCantiereName = localStorage.getItem('selectedCantiereName');\n    console.log('Cantiere selezionato:', {\n      selectedCantiereId,\n      selectedCantiereName\n    });\n\n    // Se l'utente è un utente cantiere ma non c'è un cantiere selezionato,\n    // potrebbe essere necessario recuperare l'ID del cantiere dal token\n    if (isCantieresUser && !selectedCantiereId && user !== null && user !== void 0 && user.id) {\n      console.log('Utente cantiere senza cantiere selezionato, tentativo di recupero dal token');\n      // Qui potremmo fare una chiamata API per ottenere i dettagli del cantiere\n      // Per ora, reindirizza comunque alla pagina di visualizzazione cavi\n    }\n\n    // Recupera il tab index dal localStorage\n    const savedTabIndex = localStorage.getItem('caviTabIndex');\n    const tabIndex = savedTabIndex ? parseInt(savedTabIndex, 10) : 0;\n    console.log('Tab index:', tabIndex);\n\n    // Reindirizza alla pagina appropriata in base al tab index\n    switch (tabIndex) {\n      case 0:\n        console.log('Reindirizzamento a /dashboard/cavi/visualizza');\n        navigate('/dashboard/cavi/visualizza');\n        break;\n      case 1:\n        navigate('/dashboard/cavi/posa');\n        break;\n      case 2:\n        navigate('/dashboard/cavi/parco');\n        break;\n      case 3:\n        navigate('/dashboard/cavi/excel');\n        break;\n      case 4:\n        navigate('/dashboard/cavi/report');\n        break;\n      case 5:\n        navigate('/dashboard/cavi/comande');\n        break;\n      default:\n        console.log('Tab index non valido, reindirizzamento a /dashboard/cavi/visualizza');\n        navigate('/dashboard/cavi/visualizza');\n        break;\n    }\n  }, [navigate, user]);\n\n  // Rendering di un componente vuoto, poiché il reindirizzamento avviene nell'useEffect\n  return null;\n};\n_s(CaviPage, \"GwzTXwGJTNc+l8b0AV8xiWqnHsk=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = CaviPage;\nexport default CaviPage;\nvar _c;\n$RefreshReg$(_c, \"CaviPage\");", "map": {"version": 3, "names": ["React", "useEffect", "useNavigate", "useAuth", "CaviPage", "_s", "navigate", "user", "console", "log", "isCantieresUser", "role", "selectedCantiereId", "localStorage", "getItem", "selectedCantiereName", "id", "savedTabIndex", "tabIndex", "parseInt", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/pages/CaviPage.js"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\n\nconst CaviPage = () => {\n  const navigate = useNavigate();\n  const { user } = useAuth();\n\n  // Reindirizza alla pagina di visualizzazione cavi\n  useEffect(() => {\n    console.log('CaviPage - Inizializzazione');\n\n    // Verifica se l'utente è un utente cantiere\n    const isCantieresUser = user?.role === 'cantieri_user';\n    console.log('Utente cantiere:', isCantieresUser);\n\n    // Verifica se c'è un cantiere selezionato nel localStorage\n    const selectedCantiereId = localStorage.getItem('selectedCantiereId');\n    const selectedCantiereName = localStorage.getItem('selectedCantiereName');\n    console.log('Cantiere selezionato:', { selectedCantiereId, selectedCantiereName });\n\n    // Se l'utente è un utente cantiere ma non c'è un cantiere selezionato,\n    // potrebbe essere necessario recuperare l'ID del cantiere dal token\n    if (isCantieresUser && !selectedCantiereId && user?.id) {\n      console.log('Utente cantiere senza cantiere selezionato, tentativo di recupero dal token');\n      // Qui potremmo fare una chiamata API per ottenere i dettagli del cantiere\n      // Per ora, reindirizza comunque alla pagina di visualizzazione cavi\n    }\n\n    // Recupera il tab index dal localStorage\n    const savedTabIndex = localStorage.getItem('caviTabIndex');\n    const tabIndex = savedTabIndex ? parseInt(savedTabIndex, 10) : 0;\n    console.log('Tab index:', tabIndex);\n\n    // Reindirizza alla pagina appropriata in base al tab index\n    switch (tabIndex) {\n      case 0:\n        console.log('Reindirizzamento a /dashboard/cavi/visualizza');\n        navigate('/dashboard/cavi/visualizza');\n        break;\n      case 1:\n        navigate('/dashboard/cavi/posa');\n        break;\n      case 2:\n        navigate('/dashboard/cavi/parco');\n        break;\n      case 3:\n        navigate('/dashboard/cavi/excel');\n        break;\n      case 4:\n        navigate('/dashboard/cavi/report');\n        break;\n      case 5:\n        navigate('/dashboard/cavi/comande');\n        break;\n\n      default:\n        console.log('Tab index non valido, reindirizzamento a /dashboard/cavi/visualizza');\n        navigate('/dashboard/cavi/visualizza');\n        break;\n    }\n  }, [navigate, user]);\n\n  // Rendering di un componente vuoto, poiché il reindirizzamento avviene nell'useEffect\n  return null;\n};\n\nexport default CaviPage;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,wBAAwB;AAEhD,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAMC,QAAQ,GAAGJ,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEK;EAAK,CAAC,GAAGJ,OAAO,CAAC,CAAC;;EAE1B;EACAF,SAAS,CAAC,MAAM;IACdO,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;;IAE1C;IACA,MAAMC,eAAe,GAAG,CAAAH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,IAAI,MAAK,eAAe;IACtDH,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEC,eAAe,CAAC;;IAEhD;IACA,MAAME,kBAAkB,GAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;IACrE,MAAMC,oBAAoB,GAAGF,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;IACzEN,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;MAAEG,kBAAkB;MAAEG;IAAqB,CAAC,CAAC;;IAElF;IACA;IACA,IAAIL,eAAe,IAAI,CAACE,kBAAkB,IAAIL,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAES,EAAE,EAAE;MACtDR,OAAO,CAACC,GAAG,CAAC,6EAA6E,CAAC;MAC1F;MACA;IACF;;IAEA;IACA,MAAMQ,aAAa,GAAGJ,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IAC1D,MAAMI,QAAQ,GAAGD,aAAa,GAAGE,QAAQ,CAACF,aAAa,EAAE,EAAE,CAAC,GAAG,CAAC;IAChET,OAAO,CAACC,GAAG,CAAC,YAAY,EAAES,QAAQ,CAAC;;IAEnC;IACA,QAAQA,QAAQ;MACd,KAAK,CAAC;QACJV,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;QAC5DH,QAAQ,CAAC,4BAA4B,CAAC;QACtC;MACF,KAAK,CAAC;QACJA,QAAQ,CAAC,sBAAsB,CAAC;QAChC;MACF,KAAK,CAAC;QACJA,QAAQ,CAAC,uBAAuB,CAAC;QACjC;MACF,KAAK,CAAC;QACJA,QAAQ,CAAC,uBAAuB,CAAC;QACjC;MACF,KAAK,CAAC;QACJA,QAAQ,CAAC,wBAAwB,CAAC;QAClC;MACF,KAAK,CAAC;QACJA,QAAQ,CAAC,yBAAyB,CAAC;QACnC;MAEF;QACEE,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC;QAClFH,QAAQ,CAAC,4BAA4B,CAAC;QACtC;IACJ;EACF,CAAC,EAAE,CAACA,QAAQ,EAAEC,IAAI,CAAC,CAAC;;EAEpB;EACA,OAAO,IAAI;AACb,CAAC;AAACF,EAAA,CA7DID,QAAQ;EAAA,QACKF,WAAW,EACXC,OAAO;AAAA;AAAAiB,EAAA,GAFpBhB,QAAQ;AA+Dd,eAAeA,QAAQ;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}