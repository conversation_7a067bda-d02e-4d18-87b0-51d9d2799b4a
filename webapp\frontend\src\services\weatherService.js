import axiosInstance from './axiosConfig';

/**
 * Servizio per il recupero di dati meteorologici
 */
const weatherService = {
  /**
   * Ottiene i dati meteorologici per un cantiere specifico
   * @param {number} cantiereId - ID del cantiere
   * @returns {Promise<Object>} Dati meteorologici
   */
  getWeatherForCantiere: async (cantiereId) => {
    try {
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.get(`/cantieri/${cantiereIdNum}/weather`);
      return response.data;
    } catch (error) {
      console.error('Errore nel recupero dati meteorologici:', error);
      
      // Restituisci dati demo in caso di errore
      return {
        success: false,
        data: {
          temperature: 22.5,
          humidity: 65,
          pressure: 1013,
          description: 'Dati non disponibili',
          city: 'N/D',
          country: 'IT',
          timestamp: new Date(),
          is_demo: true
        },
        message: 'Errore nel recupero dati meteorologici, utilizzando valori demo',
        error: true
      };
    }
  },

  /**
   * Formatta i dati meteorologici per la visualizzazione
   * @param {Object} weatherData - Dati meteorologici grezzi
   * @returns {Object} Dati formattati
   */
  formatWeatherData: (weatherData) => {
    if (!weatherData || !weatherData.data) {
      return {
        temperature: 'N/D',
        humidity: 'N/D',
        displayText: 'Dati meteorologici non disponibili',
        isDemo: true,
        success: false
      };
    }

    const data = weatherData.data;
    const temp = data.temperature ? `${data.temperature}°C` : 'N/D';
    const humidity = data.humidity ? `${data.humidity}%` : 'N/D';
    const city = data.city || 'N/D';

    let displayText = `${temp}, ${humidity}`;
    if (city !== 'N/D') {
      displayText += ` (${city})`;
    }

    // Gestisci diversi tipi di sorgenti dati
    let statusText = '';
    if (data.source === 'cantiere_database') {
      statusText = ' - Dati cantiere';
    } else if (data.is_demo) {
      statusText = ' - Dati demo';
    } else {
      statusText = ' - Dati live';
    }

    displayText += statusText;

    return {
      temperature: data.temperature,
      humidity: data.humidity,
      pressure: data.pressure,
      description: data.description,
      city: data.city,
      country: data.country,
      timestamp: data.timestamp,
      displayText,
      isDemo: data.is_demo || false,
      success: weatherData.success || false,
      source: data.source || 'unknown'
    };
  },

  /**
   * Ottiene dati meteorologici formattati per un cantiere
   * @param {number} cantiereId - ID del cantiere
   * @returns {Promise<Object>} Dati meteorologici formattati
   */
  getFormattedWeatherForCantiere: async (cantiereId) => {
    try {
      const rawData = await weatherService.getWeatherForCantiere(cantiereId);
      return weatherService.formatWeatherData(rawData);
    } catch (error) {
      console.error('Errore nel recupero dati meteorologici formattati:', error);
      return {
        temperature: 'N/D',
        humidity: 'N/D',
        displayText: 'Errore nel recupero dati meteorologici',
        isDemo: true,
        success: false
      };
    }
  }
};

export default weatherService;
