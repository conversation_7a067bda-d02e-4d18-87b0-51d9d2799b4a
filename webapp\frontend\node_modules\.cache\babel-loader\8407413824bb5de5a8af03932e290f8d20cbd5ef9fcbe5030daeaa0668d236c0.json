{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\VisualizzaCaviPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useRef } from 'react';\nimport { Box, Typography, Paper, Button, Grid, Card, CardContent, Alert, IconButton, Chip, CircularProgress, LinearProgress, Dialog, DialogTitle, DialogContent, DialogActions, Snackbar, FormControl, InputLabel, Select, MenuItem, Stack } from '@mui/material';\nimport InfoIcon from '@mui/icons-material/Info';\nimport { Cable as CableIcon, CheckCircle as CheckCircleIcon, Schedule as ScheduleIcon, Link as LinkIcon, LinkOff as LinkOffIcon, Timeline as TimelineIcon, CheckBox as CheckBoxIcon, CheckBoxOutlineBlank as CheckBoxOutlineBlankIcon, Visibility as VisibilityIcon, Edit as EditIcon, Delete as DeleteIcon, Add as AddIcon, SelectAll as SelectAllIcon, ContentCopy as CopyIcon, Settings as SettingsIcon, Verified as VerifiedIcon, Build as BuildIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport { useGlobalContext } from '../../context/GlobalContext';\n// import PosaCaviCollegamenti from '../../components/cavi/PosaCaviCollegamenti'; // OBSOLETO: Componente eliminato\nimport caviService from '../../services/caviService';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport CavoForm from '../../components/cavi/CavoForm';\nimport { normalizeInstallationStatus } from '../../utils/validationUtils';\nimport CaviFilterableTable from '../../components/cavi/CaviFilterableTable';\nimport InserisciMetriDialogCompleto from '../../components/cavi/InserisciMetriDialogCompleto';\nimport ModificaBobinaDialogCompleto from '../../components/cavi/ModificaBobinaDialogCompleto';\nimport CollegamentiCavo from '../../components/cavi/CollegamentiCavo';\nimport CertificazioneCaviImproved from '../../components/cavi/CertificazioneCaviImproved';\nimport CreaComandaMultipla from '../../components/comande/CreaComandaMultipla';\n// import comandeValidationService from '../../services/comandeValidationService';\n\nimport './CaviPage.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VisualizzaCaviPage = () => {\n  _s();\n  var _caviAttivi$, _caviAttivi$2, _caviAttivi$3;\n  const {\n    isImpersonating,\n    user\n  } = useAuth();\n  const {\n    openEliminaCavoDialog,\n    setOpenEliminaCavoDialog,\n    openModificaCavoDialog,\n    setOpenModificaCavoDialog,\n    openAggiungiCavoDialog,\n    setOpenAggiungiCavoDialog\n  } = useGlobalContext();\n  const navigate = useNavigate();\n  const [cantiereId, setCantiereId] = useState(null);\n  const [cantiereName, setCantiereName] = useState('');\n  const [caviAttivi, setCaviAttivi] = useState([]);\n  const [caviSpare, setCaviSpare] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  // Stato per le notifiche\n  const [notification, setNotification] = useState({\n    open: false,\n    message: '',\n    severity: 'success'\n  });\n  // Rimosso stato viewMode\n\n  // Stato per il dialogo dei dettagli del cavo\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);\n\n  // Stati per la selezione dei cavi\n  const [selectionEnabled, setSelectionEnabled] = useState(false);\n  const [selectedCaviAttivi, setSelectedCaviAttivi] = useState([]);\n  const [selectedCaviSpare, setSelectedCaviSpare] = useState([]);\n\n  // Stati per i dialoghi di azione sui pulsanti stato\n  const [inserisciMetriDialog, setInserisciMetriDialog] = useState({\n    open: false,\n    cavo: null,\n    loading: false\n  });\n  const [collegamentiDialog, setCollegamentiDialog] = useState({\n    open: false,\n    cavo: null,\n    loading: false\n  });\n  const [modificaBobinaDialog, setModificaBobinaDialog] = useState({\n    open: false,\n    cavo: null,\n    loading: false\n  });\n\n  // Ref per il componente CertificazioneCavi\n  const certificazioneRef = useRef(null);\n\n  // Stati per il dialog di creazione comande multiple\n  const [createCommandDialog, setCreateCommandDialog] = useState({\n    open: false,\n    tipoComanda: '',\n    caviSelezionati: [],\n    loading: false\n  });\n\n  // Stati per statistiche avanzate\n  const [statistics, setStatistics] = useState({\n    totaleCavi: 0,\n    caviInstallati: 0,\n    caviDaInstallare: 0,\n    caviInCorso: 0,\n    caviCollegati: 0,\n    caviNonCollegati: 0,\n    caviCertificati: 0,\n    caviNonCertificati: 0,\n    percentualeInstallazione: 0,\n    percentualeCollegamento: 0,\n    percentualeCertificazione: 0,\n    iap: 0,\n    // Indice di Avanzamento Ponderato\n    metriTotali: 0,\n    metriInstallati: 0,\n    metriRimanenti: 0\n  });\n\n  // Stato per la gestione delle revisioni\n  const [revisioniDisponibili, setRevisioniDisponibili] = useState([]);\n  const [revisioneSelezionata, setRevisioneSelezionata] = useState('');\n  const [revisioneCorrente, setRevisioneCorrente] = useState('');\n\n  // Rimosso stato per il debug\n\n  // Funzione per calcolare l'Indice di Avanzamento Ponderato (IAP)\n  const calculateIAP = (nTot, nInst, nColl, nCert) => {\n    // Pesi per le fasi del progetto\n    const Wp = 2.0; // Peso fase Posa\n    const Wc = 1.5; // Peso fase Collegamento\n    const Wz = 0.5; // Peso fase Certificazione\n\n    // Se non ci sono cavi, ritorna 0\n    if (nTot === 0) return 0;\n\n    // Calcolo del numeratore (Sforzo Completato)\n    const sforzoSoloInstallati = (nInst - nColl) * Wp;\n    const sforzoSoloCollegati = (nColl - nCert) * (Wp + Wc);\n    const sforzoCertificati = nCert * (Wp + Wc + Wz);\n    const numeratore = sforzoSoloInstallati + sforzoSoloCollegati + sforzoCertificati;\n\n    // Calcolo del denominatore (Sforzo Massimo Previsto)\n    const denominatore = nTot * (Wp + Wc + Wz);\n\n    // Calcolo finale dell'IAP in percentuale\n    const iap = numeratore / denominatore * 100;\n    console.log('Calcolo IAP:', {\n      nTot,\n      nInst,\n      nColl,\n      nCert,\n      pesi: {\n        Wp,\n        Wc,\n        Wz\n      },\n      sforzoSoloInstallati,\n      sforzoSoloCollegati,\n      sforzoCertificati,\n      numeratore,\n      denominatore,\n      iap: Math.round(iap * 100) / 100\n    });\n    return Math.round(iap * 100) / 100; // Arrotonda a 2 decimali\n  };\n\n  // Funzione per calcolare le statistiche avanzate\n  const calculateStatistics = (caviAttiviData, caviSpareData) => {\n    const tuttiCavi = [...(caviAttiviData || []), ...(caviSpareData || [])];\n    if (tuttiCavi.length === 0) {\n      console.log('Nessun cavo disponibile per il calcolo delle statistiche');\n      return;\n    }\n    console.log('Calcolo statistiche con dati:', {\n      caviAttivi: (caviAttiviData === null || caviAttiviData === void 0 ? void 0 : caviAttiviData.length) || 0,\n      caviSpare: (caviSpareData === null || caviSpareData === void 0 ? void 0 : caviSpareData.length) || 0,\n      totale: tuttiCavi.length\n    });\n    const totaleCavi = tuttiCavi.length;\n\n    // Calcola stati di installazione\n    const caviInstallati = tuttiCavi.filter(cavo => cavo.stato_installazione === 'Installato' || cavo.stato_installazione === 'INSTALLATO' || cavo.stato_installazione === 'POSATO').length;\n    const caviDaInstallare = tuttiCavi.filter(cavo => cavo.stato_installazione === 'Da installare' || cavo.stato_installazione === 'DA_INSTALLARE').length;\n    const caviInCorso = tuttiCavi.filter(cavo => cavo.stato_installazione === 'In corso' || cavo.stato_installazione === 'IN_CORSO').length;\n\n    // Calcola stati di collegamento\n    const caviCollegati = tuttiCavi.filter(cavo => cavo.collegamenti === 3 && cavo.responsabile_partenza && cavo.responsabile_arrivo).length;\n    const caviNonCollegati = totaleCavi - caviCollegati;\n\n    // Calcola certificazioni basandosi sul campo 'certificato' dei cavi\n    const caviCertificati = tuttiCavi.filter(cavo => cavo.certificato === true || cavo.certificato === 'true').length;\n\n    // Calcola l'Indice di Avanzamento Ponderato (IAP)\n    const iap = calculateIAP(totaleCavi, caviInstallati, caviCollegati, caviCertificati);\n\n    // Calcola percentuali tradizionali per confronto\n    const percentualeInstallazione = iap; // Sostituito con IAP\n    const percentualeCollegamento = totaleCavi > 0 ? Math.round(caviCollegati / totaleCavi * 100) : 0;\n\n    // Calcola metri\n    const metriTotali = tuttiCavi.reduce((sum, cavo) => sum + (parseFloat(cavo.metri_teorici) || 0), 0);\n    const metriInstallati = tuttiCavi.filter(cavo => cavo.stato_installazione === 'Installato' || cavo.stato_installazione === 'INSTALLATO' || cavo.stato_installazione === 'POSATO').reduce((sum, cavo) => sum + (parseFloat(cavo.metratura_reale) || parseFloat(cavo.metri_teorici) || 0), 0);\n    const metriRimanenti = metriTotali - metriInstallati;\n    const caviNonCertificati = totaleCavi - caviCertificati;\n    const percentualeCertificazione = totaleCavi > 0 ? Math.round(caviCertificati / totaleCavi * 100) : 0;\n    const newStatistics = {\n      totaleCavi,\n      caviInstallati,\n      caviDaInstallare,\n      caviInCorso,\n      caviCollegati,\n      caviNonCollegati,\n      caviCertificati,\n      caviNonCertificati,\n      percentualeInstallazione,\n      percentualeCollegamento,\n      percentualeCertificazione,\n      iap,\n      // Indice di Avanzamento Ponderato\n      metriTotali: Math.round(metriTotali),\n      metriInstallati: Math.round(metriInstallati),\n      metriRimanenti: Math.round(metriRimanenti)\n    };\n    console.log('Nuove statistiche calcolate:', newStatistics);\n    setStatistics(newStatistics);\n  };\n\n  // Funzione per caricare gli stati di installazione disponibili\n  const loadStatiInstallazione = () => {\n    // Usa i valori dell'enum StatoInstallazione\n    setStatiInstallazione(['Installato', 'Da installare', 'In corso']);\n  };\n\n  // Funzione per caricare le revisioni disponibili\n  const loadRevisioni = async cantiereIdToUse => {\n    try {\n      console.log('Caricamento revisioni per cantiere:', cantiereIdToUse);\n\n      // Carica la revisione corrente\n      const revisioneCorrenteData = await caviService.getRevisioneCorrente(cantiereIdToUse);\n      console.log('Revisione corrente:', revisioneCorrenteData);\n      setRevisioneCorrente(revisioneCorrenteData.revisione_corrente);\n\n      // Carica tutte le revisioni disponibili\n      const revisioniData = await caviService.getRevisioniDisponibili(cantiereIdToUse);\n      console.log('Revisioni disponibili:', revisioniData);\n      setRevisioniDisponibili(revisioniData.revisioni || []);\n\n      // LOGICA REVISIONI: La revisione corrente è quella di default\n      // Non impostiamo una revisione selezionata, così il sistema usa automaticamente la corrente\n      console.log('Logica revisioni: usando revisione corrente di default');\n    } catch (error) {\n      console.error('Errore nel caricamento delle revisioni:', error);\n    }\n  };\n\n  // Funzione per gestire il cambio di revisione\n  const handleRevisioneChange = event => {\n    const nuovaRevisione = event.target.value;\n\n    // LOGICA REVISIONI:\n    // - Se vuoto o \"corrente\" -> usa revisione corrente (non specificare parametro)\n    // - Se specifica -> usa quella revisione per visualizzazione storica\n    if (nuovaRevisione === '' || nuovaRevisione === 'corrente') {\n      setRevisioneSelezionata('');\n      console.log('Passaggio a revisione corrente (default)');\n    } else {\n      setRevisioneSelezionata(nuovaRevisione);\n      console.log('Passaggio a revisione storica:', nuovaRevisione);\n    }\n  };\n\n  // Stato per filtri e ordinamento\n  const [filters, setFilters] = useState({\n    stato_installazione: '',\n    tipologia: '',\n    sort_by: '',\n    sort_order: 'asc'\n  });\n\n  // Opzioni per i filtri\n  const [statiInstallazione, setStatiInstallazione] = useState([]);\n  const [tipologieCavi, setTipologieCavi] = useState([]);\n\n  // Rimossa funzione di debug\n\n  // Funzione per caricare i cavi\n  // Il parametro silentLoading permette di evitare di mostrare lo stato di caricamento\n  const fetchCavi = async (silentLoading = false) => {\n    try {\n      if (!silentLoading) {\n        setLoading(true);\n      }\n      console.log('Caricamento cavi per cantiere:', cantiereId);\n\n      // Verifica che cantiereId sia valido\n      if (!cantiereId) {\n        console.error('fetchCavi: cantiereId non valido:', cantiereId);\n        setError('ID cantiere non valido o mancante. Ricarica la pagina.');\n        setLoading(false);\n        return;\n      }\n\n      // Recupera il cantiereId dal localStorage come fallback\n      let cantiereIdToUse = cantiereId;\n      if (!cantiereIdToUse) {\n        cantiereIdToUse = localStorage.getItem('selectedCantiereId');\n        console.log('Usando cantiereId dal localStorage:', cantiereIdToUse);\n        if (!cantiereIdToUse) {\n          console.error('Impossibile trovare un ID cantiere valido');\n          setError('ID cantiere non trovato. Ricarica la pagina.');\n          setLoading(false);\n          return;\n        }\n      }\n\n      // Carica i cavi attivi\n      console.log('Caricamento cavi attivi (tipo_cavo=0)...');\n      let attivi = [];\n      try {\n        attivi = await caviService.getCavi(cantiereIdToUse, 0, filters);\n        console.log('Cavi attivi caricati:', attivi ? attivi.length : 0);\n      } catch (attiviError) {\n        console.error('Errore nel caricamento dei cavi attivi:', attiviError);\n        // Continua con un array vuoto\n        attivi = [];\n      }\n\n      // Verifica se ci sono cavi con modificato_manualmente = 3 tra i cavi attivi\n      if (attivi && attivi.length > 0) {\n        const caviSpareTraAttivi = attivi.filter(cavo => cavo.modificato_manualmente === 3);\n        if (caviSpareTraAttivi.length > 0) {\n          console.error('ERRORE: Trovati cavi con modificato_manualmente = 3 tra i cavi attivi:', caviSpareTraAttivi);\n        }\n      }\n      setCaviAttivi(attivi || []);\n\n      // Carica i cavi SPARE con la nuova funzione dedicata\n      let spare = [];\n      try {\n        console.log('Caricamento cavi SPARE con funzione dedicata...');\n        spare = await caviService.getCaviSpare(cantiereIdToUse);\n        console.log('Cavi SPARE caricati con funzione dedicata:', spare ? spare.length : 0);\n        if (spare && spare.length > 0) {\n          console.log('Primo cavo SPARE:', spare[0]);\n        }\n      } catch (spareError) {\n        console.error('Errore nel caricamento dei cavi SPARE con funzione dedicata:', spareError);\n        // Se fallisce, prova con il metodo standard\n        try {\n          console.log('Tentativo con metodo standard...');\n          spare = await caviService.getCavi(cantiereIdToUse, 3);\n          console.log('Cavi SPARE caricati con metodo standard:', spare ? spare.length : 0);\n        } catch (standardError) {\n          console.error('Errore anche con metodo standard:', standardError);\n          // Continua con un array vuoto\n          spare = [];\n        }\n      }\n      setCaviSpare(spare || []);\n\n      // Se siamo arrivati qui, rimuovi eventuali messaggi di errore precedenti\n      setError('');\n    } catch (error) {\n      console.error('Errore generale nel caricamento dei cavi:', error);\n      setError(`Errore nel caricamento dei cavi: ${error.message || 'Errore sconosciuto'}`);\n\n      // Prova a ricaricare la pagina dopo un ritardo se l'errore persiste\n      setTimeout(() => {\n        // Verifica se siamo ancora in errore\n        if (document.body.textContent.includes('Errore nel caricamento dei cavi')) {\n          console.log('Errore persistente, tentativo di ricaricamento della pagina...');\n          window.location.reload();\n        }\n      }, 5000); // 5 secondi di ritardo\n    } finally {\n      if (!silentLoading) {\n        setLoading(false);\n      }\n    }\n  };\n\n  // Carica i dati del cantiere e dei cavi\n  useEffect(() => {\n    // Carica gli stati di installazione all'avvio\n    loadStatiInstallazione();\n    const fetchData = async () => {\n      try {\n        console.log('Inizializzazione VisualizzaCaviPage...');\n\n        // Verifica che l'utente sia autenticato\n        const token = localStorage.getItem('token');\n        console.log('Token presente:', !!token);\n        if (!token) {\n          setError('Sessione scaduta. Effettua nuovamente il login.');\n          setLoading(false);\n          return;\n        }\n\n        // Recupera l'ID del cantiere selezionato dal localStorage\n        let selectedCantiereId = localStorage.getItem('selectedCantiereId');\n        let selectedCantiereName = localStorage.getItem('selectedCantiereName');\n        console.log('Cantiere selezionato dal localStorage:', {\n          selectedCantiereId,\n          selectedCantiereName\n        });\n        console.log('Dati utente:', user);\n\n        // Stampa tutti i dati nel localStorage per debug\n        console.log('DEBUG - Tutti i dati nel localStorage:');\n        for (let i = 0; i < localStorage.length; i++) {\n          const key = localStorage.key(i);\n          console.log(`${key}: ${localStorage.getItem(key)}`);\n        }\n\n        // SOLUZIONE DIRETTA: Ottieni l'ID del cantiere direttamente dal token JWT\n        if ((user === null || user === void 0 ? void 0 : user.role) === 'cantieri_user') {\n          console.log('Utente cantiere rilevato, tentativo di recupero ID cantiere dai dati utente');\n\n          // Verifica se l'utente ha un ID cantiere nei dati utente\n          if (user.cantiere_id) {\n            console.log('Trovato ID cantiere nei dati utente:', user.cantiere_id);\n            selectedCantiereId = user.cantiere_id.toString();\n            selectedCantiereName = user.cantiere_name || `Cantiere ${user.cantiere_id}`;\n\n            // Salva l'ID e il nome del cantiere nel localStorage\n            localStorage.setItem('selectedCantiereId', selectedCantiereId);\n            localStorage.setItem('selectedCantiereName', selectedCantiereName);\n            console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n          } else {\n            // Tentativo di recupero dal token JWT\n            try {\n              console.log('Tentativo di decodifica del token JWT per recuperare l\\'ID cantiere');\n              const token = localStorage.getItem('token');\n              if (token) {\n                // Decodifica il token JWT (senza verifica della firma)\n                const base64Url = token.split('.')[1];\n                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n                const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => {\n                  return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n                }).join(''));\n                const payload = JSON.parse(jsonPayload);\n                console.log('Payload del token JWT:', payload);\n                if (payload.cantiere_id) {\n                  console.log('Trovato ID cantiere nel token JWT:', payload.cantiere_id);\n                  selectedCantiereId = payload.cantiere_id.toString();\n                  // Usa un nome generico se non disponibile\n                  selectedCantiereName = `Cantiere ${payload.cantiere_id}`;\n\n                  // Salva l'ID e il nome del cantiere nel localStorage\n                  localStorage.setItem('selectedCantiereId', selectedCantiereId);\n                  localStorage.setItem('selectedCantiereName', selectedCantiereName);\n                  console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n                }\n              }\n            } catch (e) {\n              console.error('Errore durante la decodifica del token JWT:', e);\n            }\n          }\n        }\n\n        // SOLUZIONE TEMPORANEA: Se ancora non abbiamo un ID cantiere, usa un valore hardcoded per debug\n        if (!selectedCantiereId || selectedCantiereId === 'undefined' || selectedCantiereId === 'null') {\n          console.warn('ATTENZIONE: Nessun ID cantiere trovato, utilizzo valore hardcoded per debug');\n          // Usa il primo cantiere disponibile (questo è solo per debug)\n          selectedCantiereId = '1'; // Sostituisci con un ID cantiere valido nel tuo database\n          selectedCantiereName = 'Cantiere Debug';\n\n          // Salva l'ID e il nome del cantiere nel localStorage\n          localStorage.setItem('selectedCantiereId', selectedCantiereId);\n          localStorage.setItem('selectedCantiereName', selectedCantiereName);\n          console.log('Salvato ID cantiere hardcoded nel localStorage:', selectedCantiereId);\n        }\n\n        // Verifica finale\n        if (!selectedCantiereId) {\n          setError('Nessun cantiere selezionato. Torna alla pagina dei cantieri.');\n          setLoading(false);\n          return;\n        }\n\n        // Verifica che l'ID del cantiere sia un numero valido\n        const cantiereIdNum = parseInt(selectedCantiereId, 10);\n        console.log('ID cantiere convertito a numero:', cantiereIdNum);\n        if (isNaN(cantiereIdNum)) {\n          setError(`ID cantiere non valido: ${selectedCantiereId}. Torna alla pagina dei cantieri.`);\n          setLoading(false);\n          return;\n        }\n\n        // Usa il numero convertito, non la stringa\n        setCantiereId(cantiereIdNum);\n        setCantiereName(selectedCantiereName || `Cantiere ${cantiereIdNum}`);\n\n        // Carica le revisioni disponibili\n        await loadRevisioni(cantiereIdNum);\n\n        // Carica i cavi attivi con gestione degli errori migliorata\n        console.log('Caricamento cavi attivi per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi attivi')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza e applica i filtri\n          console.log('Iniziando chiamata API per cavi attivi con filtri:', filters);\n          const caviPromise = caviService.getCavi(cantiereIdNum, 0, filters);\n          const attivi = await Promise.race([caviPromise, timeoutPromise]);\n          console.log('Cavi attivi caricati:', attivi);\n          console.log('Numero di cavi attivi trovati:', attivi ? attivi.length : 0);\n          if (attivi && attivi.length > 0) {\n            console.log('Primo cavo attivo:', attivi[0]);\n          } else {\n            console.warn('Nessun cavo attivo trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviAttivi(attivi || []);\n\n          // Calcola le statistiche dopo aver caricato i cavi attivi\n          calculateStatistics(attivi || [], caviSpare);\n        } catch (caviError) {\n          console.error('Errore nel caricamento dei cavi attivi:', caviError);\n          console.error('Dettagli errore cavi attivi:', {\n            message: caviError.message,\n            status: caviError.status,\n            data: caviError.data,\n            stack: caviError.stack,\n            code: caviError.code,\n            name: caviError.name,\n            response: caviError.response ? {\n              status: caviError.response.status,\n              statusText: caviError.response.statusText,\n              data: caviError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, continua con i cavi spare\n          setCaviAttivi([]);\n          console.warn('Continuazione del flusso dopo errore nei cavi attivi');\n\n          // Aggiungi un messaggio di errore visibile all'utente\n          setError(`Errore nel caricamento dei cavi attivi: ${caviError.message}. Controlla la console per maggiori dettagli.`);\n        }\n\n        // Carica i cavi spare con gestione degli errori migliorata\n        console.log('Caricamento cavi spare per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi spare')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza\n          console.log('Iniziando chiamata API per cavi spare...');\n          // Non applichiamo i filtri ai cavi spare, solo agli attivi\n          const sparePromise = caviService.getCavi(cantiereIdNum, 3);\n          const spare = await Promise.race([sparePromise, timeoutPromise]);\n          console.log('Cavi spare caricati:', spare);\n          console.log('Numero di cavi spare trovati:', spare ? spare.length : 0);\n          if (spare && spare.length > 0) {\n            console.log('Primo cavo spare:', spare[0]);\n          } else {\n            console.warn('Nessun cavo spare trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviSpare(spare || []);\n\n          // Calcola le statistiche dopo aver caricato i cavi spare\n          calculateStatistics(caviAttivi, spare || []);\n        } catch (spareError) {\n          console.error('Errore nel caricamento dei cavi spare:', spareError);\n          console.error('Dettagli errore cavi spare:', {\n            message: spareError.message,\n            status: spareError.status,\n            data: spareError.data,\n            stack: spareError.stack,\n            code: spareError.code,\n            name: spareError.name,\n            response: spareError.response ? {\n              status: spareError.response.status,\n              statusText: spareError.response.statusText,\n              data: spareError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, imposta un array vuoto\n          setCaviSpare([]);\n\n          // Aggiungi un messaggio di errore visibile all'utente se non c'è già un errore per i cavi attivi\n          if (!error) {\n            setError(`Errore nel caricamento dei cavi spare: ${spareError.message}. Controlla la console per maggiori dettagli.`);\n          }\n        }\n\n        // Se siamo arrivati qui, almeno abbiamo caricato l'interfaccia di base\n        setLoading(false);\n      } catch (err) {\n        var _err$response, _err$response2, _err$response3, _err$response4, _err$response5, _err$response5$data;\n        console.error('Errore nel caricamento dei cavi:', err);\n        console.error('Dettagli errore generale:', {\n          message: err.message,\n          status: err.status || ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.status),\n          data: err.data || ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.data),\n          stack: err.stack\n        });\n\n        // Estrai il messaggio di errore dettagliato\n        let errorMessage = 'Errore sconosciuto';\n        if (err.message && err.message.includes('ID cantiere non valido')) {\n          errorMessage = err.message;\n        } else if (err.status === 401 || err.status === 403 || ((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : _err$response3.status) === 401 || ((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : _err$response4.status) === 403) {\n          errorMessage = 'Sessione scaduta o non autorizzata. Effettua nuovamente il login.';\n        } else if ((_err$response5 = err.response) !== null && _err$response5 !== void 0 && (_err$response5$data = _err$response5.data) !== null && _err$response5$data !== void 0 && _err$response5$data.detail) {\n          // Estrai il messaggio di errore dettagliato dall'API\n          errorMessage = `Errore API: ${err.response.data.detail}`;\n        } else if (err.code === 'ERR_NETWORK') {\n          // Errore di rete\n          errorMessage = 'Network Error. Verifica che il backend sia in esecuzione e accessibile.';\n        } else if (err.message) {\n          errorMessage = err.message;\n        }\n        setError(`Impossibile caricare i cavi: ${errorMessage}. Riprova più tardi.`);\n\n        // Imposta array vuoti per evitare errori di rendering\n        setCaviAttivi([]);\n        setCaviSpare([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, [filters]); // Ricarica i dati quando cambiano i filtri\n\n  // I filtri sono ora gestiti dal componente CaviFilterableTable\n\n  // Funzione per aprire il dialogo dei dettagli del cavo\n  const handleOpenDetails = cavo => {\n    setSelectedCavo(cavo);\n    setDetailsDialogOpen(true);\n  };\n\n  // Funzione per chiudere il dialogo dei dettagli del cavo\n  const handleCloseDetails = () => {\n    setDetailsDialogOpen(false);\n    setSelectedCavo(null);\n  };\n\n  // Funzione per chiudere la notifica\n  const handleCloseNotification = () => {\n    setNotification(prev => ({\n      ...prev,\n      open: false\n    }));\n  };\n\n  // Funzione per mostrare una notifica\n  const showNotification = (message, severity = 'success') => {\n    setNotification({\n      open: true,\n      message,\n      severity\n    });\n  };\n\n  // Funzioni per gestire la selezione dei cavi\n  const handleSelectionToggle = () => {\n    setSelectionEnabled(!selectionEnabled);\n    // Pulisci le selezioni quando si disabilita la modalità selezione\n    if (selectionEnabled) {\n      setSelectedCaviAttivi([]);\n      setSelectedCaviSpare([]);\n    }\n  };\n  const handleCaviAttiviSelectionChange = selectedIds => {\n    setSelectedCaviAttivi(selectedIds);\n  };\n  const handleCaviSpareSelectionChange = selectedIds => {\n    setSelectedCaviSpare(selectedIds);\n  };\n\n  // Funzione per ottenere tutti i cavi selezionati\n  const getAllSelectedCavi = () => {\n    const selectedAttiviCavi = caviAttivi.filter(cavo => selectedCaviAttivi.includes(cavo.id_cavo));\n    const selectedSpareCavi = caviSpare.filter(cavo => selectedCaviSpare.includes(cavo.id_cavo));\n    return [...selectedAttiviCavi, ...selectedSpareCavi];\n  };\n\n  // Funzione per ottenere il conteggio totale dei cavi selezionati\n  const getTotalSelectedCount = () => {\n    return selectedCaviAttivi.length + selectedCaviSpare.length;\n  };\n\n  // Funzioni per gestire le azioni del menu contestuale\n  const handleContextMenuAction = (cavo, action) => {\n    console.log('Azione menu contestuale:', action, 'per cavo:', cavo);\n    switch (action) {\n      case 'view_details':\n        handleOpenDetails(cavo);\n        break;\n      case 'edit':\n        // Funzionalità di modifica cavo non ancora implementata\n        showNotification('Funzionalità di modifica cavo in sviluppo', 'info');\n        break;\n      case 'delete':\n        // Funzionalità di eliminazione cavo non ancora implementata\n        showNotification('Funzionalità di eliminazione cavo in sviluppo', 'info');\n        break;\n      case 'select':\n        if (caviAttivi.some(c => c.id_cavo === cavo.id_cavo)) {\n          // È un cavo attivo\n          const isSelected = selectedCaviAttivi.includes(cavo.id_cavo);\n          if (isSelected) {\n            setSelectedCaviAttivi(prev => prev.filter(id => id !== cavo.id_cavo));\n          } else {\n            setSelectedCaviAttivi(prev => [...prev, cavo.id_cavo]);\n          }\n        } else {\n          // È un cavo spare\n          const isSelected = selectedCaviSpare.includes(cavo.id_cavo);\n          if (isSelected) {\n            setSelectedCaviSpare(prev => prev.filter(id => id !== cavo.id_cavo));\n          } else {\n            setSelectedCaviSpare(prev => [...prev, cavo.id_cavo]);\n          }\n        }\n        // Abilita automaticamente la modalità selezione se non è già attiva\n        if (!selectionEnabled) {\n          setSelectionEnabled(true);\n        }\n        break;\n      case 'copy_id':\n        const totalSelectedCount = getTotalSelectedCount();\n        if (totalSelectedCount > 1) {\n          // Copia tutti gli ID dei cavi selezionati\n          const allSelectedCavi = getAllSelectedCavi();\n          const allIds = allSelectedCavi.map(c => c.id_cavo).join(', ');\n          navigator.clipboard.writeText(allIds);\n          showNotification(`${totalSelectedCount} IDs cavi copiati negli appunti`, 'success');\n        } else {\n          navigator.clipboard.writeText(cavo.id_cavo);\n          showNotification(`ID cavo ${cavo.id_cavo} copiato negli appunti`, 'success');\n        }\n        break;\n      case 'copy_details':\n        const totalSelected = getTotalSelectedCount();\n        if (totalSelected > 1) {\n          // Copia dettagli di tutti i cavi selezionati\n          const allSelectedCavi = getAllSelectedCavi();\n          const allDetails = allSelectedCavi.map(c => `ID: ${c.id_cavo}\\nTipologia: ${c.tipologia}\\nSezione: ${c.sezione}\\nMetri: ${c.metri_teorici}`).join('\\n\\n');\n          navigator.clipboard.writeText(allDetails);\n          showNotification(`Dettagli di ${totalSelected} cavi copiati negli appunti`, 'success');\n        } else {\n          const details = `ID: ${cavo.id_cavo}\\nTipologia: ${cavo.tipologia}\\nSezione: ${cavo.sezione}\\nMetri: ${cavo.metri_teorici}`;\n          navigator.clipboard.writeText(details);\n          showNotification('Dettagli cavo copiati negli appunti', 'success');\n        }\n        break;\n      case 'add_new':\n        // Funzionalità di aggiunta cavo non ancora implementata\n        showNotification('Funzionalità di aggiunta cavo in sviluppo', 'info');\n        break;\n      // Nuove azioni per la creazione di comande multiple\n      case 'create_command_posa':\n        handleCreateMultipleCommand('POSA');\n        break;\n      case 'create_command_collegamento_partenza':\n        handleCreateMultipleCommand('COLLEGAMENTO_PARTENZA');\n        break;\n      case 'create_command_collegamento_arrivo':\n        handleCreateMultipleCommand('COLLEGAMENTO_ARRIVO');\n        break;\n      case 'create_command_certificazione':\n        handleCreateMultipleCommand('CERTIFICAZIONE');\n        break;\n      default:\n        console.warn('Azione non riconosciuta:', action);\n    }\n  };\n\n  // Funzione per controllare rapidamente la validazione di un tipo di comanda\n  const getQuickValidationStatus = tipoComanda => {\n    // Temporaneamente disabilitato per debug - restituisce sempre valido\n    return {\n      valid: true,\n      issues: 0,\n      errors: 0,\n      warnings: 0\n    };\n  };\n\n  // Funzione per gestire la creazione di comande multiple\n  const handleCreateMultipleCommand = tipoComanda => {\n    const allSelectedCavi = getAllSelectedCavi();\n    if (allSelectedCavi.length === 0) {\n      showNotification('Nessun cavo selezionato per la creazione della comanda', 'warning');\n      return;\n    }\n    console.log(`Creazione comanda ${tipoComanda} per ${allSelectedCavi.length} cavi:`, allSelectedCavi.map(c => c.id_cavo));\n\n    // Apri il dialog di creazione comanda con i cavi preselezionati\n    setCreateCommandDialog({\n      open: true,\n      tipoComanda: tipoComanda,\n      caviSelezionati: allSelectedCavi,\n      loading: false\n    });\n  };\n\n  // Funzione per chiudere il dialog di creazione comande\n  const handleCloseCreateCommand = () => {\n    setCreateCommandDialog({\n      open: false,\n      tipoComanda: '',\n      caviSelezionati: [],\n      loading: false\n    });\n  };\n\n  // Funzione per gestire il successo della creazione comanda\n  const handleCreateCommandSuccess = response => {\n    showNotification(`Comanda ${response.codice_comanda} creata con successo!`, 'success');\n\n    // Deseleziona tutti i cavi\n    setSelectedCaviAttivi([]);\n    setSelectedCaviSpare([]);\n\n    // Chiudi il dialog\n    handleCloseCreateCommand();\n\n    // Ricarica i dati per aggiornare lo stato\n    setTimeout(() => fetchCavi(true), 500);\n  };\n\n  // Funzione per gestire gli errori nella creazione comanda\n  const handleCreateCommandError = error => {\n    console.error('Errore nella creazione della comanda:', error);\n    showNotification('Errore nella creazione della comanda', 'error');\n  };\n\n  // Definizione degli elementi del menu contestuale\n  const getContextMenuItems = cavo => {\n    const isSelected = caviAttivi.some(c => c.id_cavo === (cavo === null || cavo === void 0 ? void 0 : cavo.id_cavo)) ? selectedCaviAttivi.includes(cavo === null || cavo === void 0 ? void 0 : cavo.id_cavo) : selectedCaviSpare.includes(cavo === null || cavo === void 0 ? void 0 : cavo.id_cavo);\n    const totalSelectedCount = getTotalSelectedCount();\n    const hasMultipleSelection = totalSelectedCount > 1;\n\n    // Menu base per singolo cavo\n    const baseMenuItems = [{\n      type: 'header',\n      label: hasMultipleSelection ? `${totalSelectedCount} cavi selezionati` : `Cavo ${(cavo === null || cavo === void 0 ? void 0 : cavo.id_cavo) || ''}`\n    },\n    // Sezione comande multiple (solo se ci sono più cavi selezionati)\n    ...(hasMultipleSelection ? [{\n      type: 'header',\n      label: `📋 Crea Comande Multiple (${totalSelectedCount} cavi)`\n    }, {\n      id: 'create_command_posa',\n      label: 'Comanda Posa',\n      icon: /*#__PURE__*/_jsxDEV(BuildIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 908,\n        columnNumber: 17\n      }, this),\n      action: 'create_command_posa',\n      onClick: handleContextMenuAction,\n      color: (() => {\n        const validation = getQuickValidationStatus('POSA');\n        return validation.errors > 0 ? 'error' : validation.warnings > 0 ? 'warning' : 'primary';\n      })(),\n      description: (() => {\n        const validation = getQuickValidationStatus('POSA');\n        let desc = `Crea comanda posa per ${totalSelectedCount} cavi`;\n        if (validation.issues > 0) {\n          desc += ` (${validation.errors} errori, ${validation.warnings} avvisi)`;\n        }\n        return desc;\n      })()\n    }, {\n      id: 'create_command_collegamento_partenza',\n      label: 'Comanda Collegamento Partenza',\n      icon: /*#__PURE__*/_jsxDEV(LinkIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 927,\n        columnNumber: 17\n      }, this),\n      action: 'create_command_collegamento_partenza',\n      onClick: handleContextMenuAction,\n      color: (() => {\n        const validation = getQuickValidationStatus('COLLEGAMENTO_PARTENZA');\n        return validation.errors > 0 ? 'error' : validation.warnings > 0 ? 'warning' : 'primary';\n      })(),\n      description: (() => {\n        const validation = getQuickValidationStatus('COLLEGAMENTO_PARTENZA');\n        let desc = `Crea comanda collegamento partenza per ${totalSelectedCount} cavi`;\n        if (validation.issues > 0) {\n          desc += ` (${validation.errors} errori, ${validation.warnings} avvisi)`;\n        }\n        return desc;\n      })()\n    }, {\n      id: 'create_command_collegamento_arrivo',\n      label: 'Comanda Collegamento Arrivo',\n      icon: /*#__PURE__*/_jsxDEV(LinkIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 946,\n        columnNumber: 17\n      }, this),\n      action: 'create_command_collegamento_arrivo',\n      onClick: handleContextMenuAction,\n      color: (() => {\n        const validation = getQuickValidationStatus('COLLEGAMENTO_ARRIVO');\n        return validation.errors > 0 ? 'error' : validation.warnings > 0 ? 'warning' : 'primary';\n      })(),\n      description: (() => {\n        const validation = getQuickValidationStatus('COLLEGAMENTO_ARRIVO');\n        let desc = `Crea comanda collegamento arrivo per ${totalSelectedCount} cavi`;\n        if (validation.issues > 0) {\n          desc += ` (${validation.errors} errori, ${validation.warnings} avvisi)`;\n        }\n        return desc;\n      })()\n    }, {\n      id: 'create_command_certificazione',\n      label: 'Comanda Certificazione',\n      icon: /*#__PURE__*/_jsxDEV(VerifiedIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 965,\n        columnNumber: 17\n      }, this),\n      action: 'create_command_certificazione',\n      onClick: handleContextMenuAction,\n      color: (() => {\n        const validation = getQuickValidationStatus('CERTIFICAZIONE');\n        return validation.errors > 0 ? 'error' : validation.warnings > 0 ? 'warning' : 'primary';\n      })(),\n      description: (() => {\n        const validation = getQuickValidationStatus('CERTIFICAZIONE');\n        let desc = `Crea comanda certificazione per ${totalSelectedCount} cavi`;\n        if (validation.issues > 0) {\n          desc += ` (${validation.errors} errori, ${validation.warnings} avvisi)`;\n        }\n        return desc;\n      })()\n    }, {\n      type: 'divider'\n    }] : []),\n    // Azioni singolo cavo (solo se non c'è selezione multipla)\n    ...(!hasMultipleSelection ? [{\n      id: 'view_details',\n      label: 'Visualizza Dettagli',\n      icon: /*#__PURE__*/_jsxDEV(VisibilityIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 990,\n        columnNumber: 17\n      }, this),\n      action: 'view_details',\n      onClick: handleContextMenuAction\n    }, {\n      type: 'divider'\n    }, {\n      id: 'edit',\n      label: 'Modifica',\n      icon: /*#__PURE__*/_jsxDEV(EditIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1000,\n        columnNumber: 17\n      }, this),\n      action: 'edit',\n      onClick: handleContextMenuAction,\n      color: 'primary'\n    }, {\n      id: 'delete',\n      label: 'Elimina',\n      icon: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1008,\n        columnNumber: 17\n      }, this),\n      action: 'delete',\n      onClick: handleContextMenuAction,\n      color: 'error'\n    }, {\n      type: 'divider'\n    }, {\n      id: 'add_new',\n      label: 'Aggiungi nuovo cavo',\n      icon: /*#__PURE__*/_jsxDEV(AddIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1019,\n        columnNumber: 17\n      }, this),\n      action: 'add_new',\n      onClick: handleContextMenuAction,\n      color: 'success'\n    }, {\n      type: 'divider'\n    }] : []),\n    // Azioni di selezione (sempre presenti)\n    {\n      id: 'select',\n      label: isSelected ? 'Deseleziona' : 'Seleziona',\n      icon: /*#__PURE__*/_jsxDEV(SelectAllIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1032,\n        columnNumber: 15\n      }, this),\n      action: 'select',\n      onClick: handleContextMenuAction,\n      color: isSelected ? 'warning' : 'success'\n    },\n    // Azioni di copia (sempre presenti)\n    {\n      type: 'divider'\n    }, {\n      id: 'copy_id',\n      label: hasMultipleSelection ? 'Copia IDs Selezionati' : 'Copia ID',\n      icon: /*#__PURE__*/_jsxDEV(CopyIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1044,\n        columnNumber: 15\n      }, this),\n      action: 'copy_id',\n      onClick: handleContextMenuAction,\n      shortcut: 'Ctrl+C'\n    }, {\n      id: 'copy_details',\n      label: hasMultipleSelection ? 'Copia Dettagli Selezionati' : 'Copia Dettagli',\n      icon: /*#__PURE__*/_jsxDEV(CopyIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1052,\n        columnNumber: 15\n      }, this),\n      action: 'copy_details',\n      onClick: handleContextMenuAction,\n      description: hasMultipleSelection ? 'Copia dettagli di tutti i cavi selezionati' : 'Copia ID, tipologia, sezione e metri'\n    }];\n    return baseMenuItems;\n  };\n\n  // Funzioni per gestire le azioni sui pulsanti stato\n  const handleStatusAction = async (cavo, actionType, actionLabel) => {\n    console.log('🎯 CLICK PULSANTE STATO RILEVATO!');\n    console.log('Azione pulsante stato:', actionType, 'per cavo:', cavo);\n    console.log('Action label:', actionLabel);\n    if (actionType === 'insert_meters') {\n      // Apri il dialogo per inserire i metri posati\n      setInserisciMetriDialog({\n        open: true,\n        cavo: cavo,\n        loading: false\n      });\n    } else if (actionType === 'modify_reel') {\n      // Apri il dialog completo per modificare la bobina con cavo preselezionato\n      console.log('Apertura dialog modifica bobina per cavo:', cavo.id_cavo);\n      setModificaBobinaDialog({\n        open: true,\n        cavo: cavo,\n        loading: false\n      });\n    } else if (actionType === 'connect_cable' || actionType === 'connect_arrival' || actionType === 'connect_departure' || actionType === 'disconnect_cable' || actionType === 'manage_connections') {\n      // Verifica se il cavo è installato\n      if (cavo.stato_installazione !== 'Installato') {\n        console.log('Azione collegamenti ignorata per cavo non installato:', cavo.id_cavo);\n        showNotification('I collegamenti sono disponibili solo per cavi installati', 'info');\n        return;\n      }\n\n      // Cavo installato - apri il popup per gestire i collegamenti\n      console.log('Apertura popup collegamenti per cavo installato:', cavo.id_cavo, 'azione:', actionType);\n\n      // Usa setTimeout per evitare conflitti di stato\n      setTimeout(() => {\n        setCollegamentiDialog({\n          open: true,\n          cavo: cavo,\n          loading: false\n        });\n      }, 50);\n    } else if (actionType === 'create_certificate') {\n      // Apri il dialog per creare una certificazione usando il componente esistente\n      console.log('Apertura dialog creazione certificazione per cavo:', cavo.id_cavo);\n\n      // Usa il componente CertificazioneCavi esistente con tutti i prerequisiti\n      if (certificazioneRef.current) {\n        certificazioneRef.current.createCertificationForCavo(cavo);\n      }\n    } else if (actionType === 'view_certificate') {\n      // Apri il dialog per visualizzare la certificazione esistente\n      console.log('Apertura dialog visualizzazione certificazione per cavo:', cavo.id_cavo);\n\n      // Usa il componente CertificazioneCavi esistente per visualizzare\n      if (certificazioneRef.current) {\n        certificazioneRef.current.viewCertificationForCavo(cavo);\n      }\n    } else if (actionType === 'generate_pdf') {\n      // Genera il PDF del certificato per il cavo\n      console.log('Generazione PDF certificato per cavo:', cavo.id_cavo);\n\n      // Usa il componente CertificazioneCavi esistente per generare il PDF\n      if (certificazioneRef.current) {\n        certificazioneRef.current.generatePdfForCavo(cavo);\n      }\n    }\n  };\n\n  // Funzioni per gestire i callback del dialog modifica bobina\n  const handleModificaBobinaSuccess = message => {\n    showNotification(message, 'success');\n    // Ricarica i dati per aggiornare lo stato\n    setTimeout(() => fetchCavi(true), 500);\n  };\n  const handleModificaBobinaError = message => {\n    showNotification(message, 'error');\n  };\n\n  // Funzioni per chiudere i dialoghi\n  const handleCloseInserisciMetri = () => {\n    if (!inserisciMetriDialog.loading) {\n      setInserisciMetriDialog({\n        open: false,\n        cavo: null,\n        loading: false\n      });\n    }\n  };\n  const handleCloseModificaBobina = () => {\n    if (!modificaBobinaDialog.loading) {\n      setModificaBobinaDialog({\n        open: false,\n        cavo: null,\n        loading: false\n      });\n    }\n  };\n  const handleCloseCollegamenti = useCallback(() => {\n    if (!collegamentiDialog.loading) {\n      setCollegamentiDialog(prev => ({\n        ...prev,\n        open: false\n      }));\n    }\n  }, [collegamentiDialog.loading]);\n\n  // Nessuna funzione di navigazione necessaria, tutto è gestito dal menu principale\n\n  // Dashboard minimal con statistiche essenziali per visualizzazione cavi\n  const renderDashboard = () => /*#__PURE__*/_jsxDEV(Paper, {\n    sx: {\n      p: 2,\n      mb: 3,\n      bgcolor: 'grey.50'\n    },\n    children: /*#__PURE__*/_jsxDEV(Stack, {\n      direction: \"row\",\n      spacing: 4,\n      alignItems: \"center\",\n      justifyContent: \"space-between\",\n      flexWrap: \"wrap\",\n      children: [/*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        alignItems: \"center\",\n        spacing: 1,\n        children: [/*#__PURE__*/_jsxDEV(CableIcon, {\n          color: \"primary\",\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1177,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            fontWeight: \"bold\",\n            sx: {\n              lineHeight: 1\n            },\n            children: statistics.totaleCavi\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1179,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"Totale\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1182,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1178,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1176,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        alignItems: \"center\",\n        spacing: 1,\n        children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n          color: \"success\",\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1189,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            fontWeight: \"bold\",\n            sx: {\n              lineHeight: 1\n            },\n            children: statistics.caviInstallati\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1191,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"Installati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1194,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1190,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1188,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        alignItems: \"center\",\n        spacing: 1,\n        children: [/*#__PURE__*/_jsxDEV(LinkIcon, {\n          color: \"info\",\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1201,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            fontWeight: \"bold\",\n            sx: {\n              lineHeight: 1\n            },\n            children: statistics.caviCollegati\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1203,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"Collegati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1206,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1202,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1200,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        alignItems: \"center\",\n        spacing: 1,\n        children: [/*#__PURE__*/_jsxDEV(VerifiedIcon, {\n          color: \"warning\",\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1213,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            fontWeight: \"bold\",\n            sx: {\n              lineHeight: 1\n            },\n            children: statistics.caviCertificati\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1215,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"Certificati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1218,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1214,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1212,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        alignItems: \"center\",\n        spacing: 1,\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: 32,\n            height: 32,\n            borderRadius: '50%',\n            bgcolor: statistics.iap >= 80 ? 'success.main' : statistics.iap >= 50 ? 'warning.main' : 'error.main',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            fontWeight: \"bold\",\n            color: \"white\",\n            children: [statistics.iap, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1235,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1225,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            fontWeight: \"medium\",\n            sx: {\n              lineHeight: 1\n            },\n            children: \"IAP (Avanzamento Ponderato)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1240,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: [statistics.metriInstallati, \"m installati\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1243,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1239,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1224,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1174,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 1173,\n    columnNumber: 5\n  }, this);\n\n  // La visualizzazione dei cavi è ora gestita dal componente CaviFilterableTable\n\n  // Rimossa funzione handleViewModeChange\n\n  // Renderizza il dialogo dei dettagli del cavo\n  const renderDetailsDialog = () => {\n    if (!selectedCavo) return null;\n    return /*#__PURE__*/_jsxDEV(Dialog, {\n      open: detailsDialogOpen,\n      onClose: handleCloseDetails,\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: [\"Dettagli Cavo: \", selectedCavo.id_cavo]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1262,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Informazioni Generali\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1268,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Sistema:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1270,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.sistema || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1270,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Utility:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1271,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.utility || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1271,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Tipologia:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1272,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.tipologia || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1272,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Colore:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1273,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.colore_cavo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1273,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Formazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1275,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.sezione || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1275,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1269,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Partenza\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1279,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Ubicazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1281,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.ubicazione_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1281,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Utenza:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1282,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.utenza_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1282,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Descrizione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1283,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.descrizione_utenza_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1283,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Responsabile:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1284,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.responsabile_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1284,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Comanda:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1285,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.comanda_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1285,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1280,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1267,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Arrivo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1290,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Ubicazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1292,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.ubicazione_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1292,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Utenza:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1293,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.utenza_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1293,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Descrizione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1294,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.descrizione_utenza_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1294,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Responsabile:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1295,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.responsabile_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1295,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Comanda:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1296,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.comanda_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1296,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1291,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Installazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1299,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri Teorici:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1301,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.metri_teorici || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1301,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metratura Reale:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1302,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.metratura_reale || '0']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1302,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Stato:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1303,\n                  columnNumber: 45\n                }, this), \" \", normalizeInstallationStatus(selectedCavo.stato_installazione)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1303,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Collegamenti:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1304,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.collegamenti || '0']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1304,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Bobina:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1305,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.id_bobina || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1305,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Responsabile Posa:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1306,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.responsabile_posa || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1306,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Comanda Posa:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1307,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.comanda_posa || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1307,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Ultimo Aggiornamento:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1308,\n                  columnNumber: 45\n                }, this), \" \", new Date(selectedCavo.timestamp).toLocaleString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1308,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1300,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1289,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1266,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1265,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseDetails,\n          children: \"Chiudi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1314,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1313,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1261,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Il pannello dei filtri è ora gestito dal componente CaviFilterableTable\n\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: \"cavi-page\",\n    children: loading ? /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        mt: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n        size: 40\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1328,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          mt: 2\n        },\n        children: \"Caricamento cavi...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1329,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        color: \"primary\",\n        onClick: () => window.location.reload(),\n        sx: {\n          mt: 2\n        },\n        children: \"Ricarica la pagina\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1330,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1327,\n      columnNumber: 9\n    }, this) : error ? /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: [error, error.includes('Network Error') && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            mt: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Suggerimento:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1345,\n            columnNumber: 17\n          }, this), \" Verifica che il server backend sia in esecuzione sulla porta 8001.\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1346,\n            columnNumber: 17\n          }, this), \"Puoi avviare il backend eseguendo il file \", /*#__PURE__*/_jsxDEV(\"code\", {\n            children: \"run_system.py\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1347,\n            columnNumber: 59\n          }, this), \" nella cartella principale del progetto.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1344,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1341,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          className: \"primary-button\",\n          onClick: () => window.location.reload(),\n          children: \"Ricarica la pagina\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1352,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1351,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1340,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Box, {\n      children: [revisioniDisponibili.length > 0 && /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2,\n          mb: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Visualizzazione:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1367,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n            size: \"small\",\n            sx: {\n              minWidth: 250\n            },\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Revisione da Visualizzare\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1369,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: revisioneSelezionata || 'corrente',\n              onChange: handleRevisioneChange,\n              label: \"Revisione da Visualizzare\",\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"corrente\",\n                children: [\"\\uD83D\\uDCCB Revisione Corrente \", revisioneCorrente && `(${revisioneCorrente})`]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1375,\n                columnNumber: 21\n              }, this), revisioniDisponibili.map(rev => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: rev.revisione,\n                children: [\"\\uD83D\\uDCDA \", rev.revisione, \" (\", rev.cavi_count, \" cavi)\", rev.revisione === revisioneCorrente && ' - Attuale']\n              }, rev.revisione, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1379,\n                columnNumber: 23\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1370,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1368,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Chip, {\n            label: revisioneSelezionata ? `Storico: ${revisioneSelezionata}` : `Corrente: ${revisioneCorrente || 'N/A'}`,\n            color: revisioneSelezionata ? \"secondary\" : \"primary\",\n            variant: \"outlined\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1386,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1366,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1365,\n        columnNumber: 13\n      }, this), renderDashboard(), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 4\n        },\n        children: [selectionEnabled && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mb: 2\n          },\n          action: /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            size: \"small\",\n            onClick: () => setSelectionEnabled(false),\n            children: \"Disabilita\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1410,\n            columnNumber: 19\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Modalit\\xE0 Selezione Attiva\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1420,\n              columnNumber: 19\n            }, this), \" - Clicca sui cavi per selezionarli, poi usa il \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"tasto destro\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1421,\n              columnNumber: 30\n            }, this), \" per creare comande multiple.\", getTotalSelectedCount() > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [getTotalSelectedCount(), \" cavi selezionati\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1423,\n                columnNumber: 28\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1423,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1419,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1406,\n          columnNumber: 15\n        }, this), selectionEnabled && getTotalSelectedCount() > 0 && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Chip, {\n            label: `${getTotalSelectedCount()} cavi selezionati`,\n            color: \"primary\",\n            variant: \"filled\",\n            size: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1432,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1431,\n          columnNumber: 15\n        }, this), process.env.NODE_ENV === 'development' && caviAttivi.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 2,\n            p: 1,\n            bgcolor: '#f0f0f0',\n            borderRadius: 1,\n            fontSize: '0.8rem',\n            fontFamily: 'monospace',\n            display: 'none'\n          },\n          children: Object.keys(caviAttivi[0]).map(key => /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [key, \": \", JSON.stringify(caviAttivi[0][key])]\n          }, key, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1445,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1443,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(CaviFilterableTable, {\n          cavi: caviAttivi,\n          loading: loading,\n          onFilteredDataChange: filteredData => console.log('Cavi attivi filtrati:', filteredData.length),\n          revisioneCorrente: ((_caviAttivi$ = caviAttivi[0]) === null || _caviAttivi$ === void 0 ? void 0 : _caviAttivi$.revisione_ufficiale) || ((_caviAttivi$2 = caviAttivi[0]) === null || _caviAttivi$2 === void 0 ? void 0 : _caviAttivi$2.revisione) || ((_caviAttivi$3 = caviAttivi[0]) === null || _caviAttivi$3 === void 0 ? void 0 : _caviAttivi$3.rev),\n          selectionEnabled: selectionEnabled,\n          selectedCavi: selectedCaviAttivi,\n          onSelectionChange: handleCaviAttiviSelectionChange,\n          onSelectionToggle: handleSelectionToggle,\n          contextMenuItems: getContextMenuItems,\n          onContextMenuAction: handleContextMenuAction,\n          onStatusAction: handleStatusAction\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1450,\n          columnNumber: 13\n        }, this), caviAttivi.length === 0 && !loading && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 2\n          },\n          children: \"Nessun cavo attivo trovato. I cavi attivi appariranno qui.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1464,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1403,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            mb: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            children: [\"Cavi Spare \", caviSpare.length > 0 ? `(${caviSpare.length})` : '']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1473,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1472,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(CaviFilterableTable, {\n          cavi: caviSpare,\n          loading: loading,\n          onFilteredDataChange: filteredData => console.log('Cavi spare filtrati:', filteredData.length),\n          selectionEnabled: selectionEnabled,\n          selectedCavi: selectedCaviSpare,\n          onSelectionChange: handleCaviSpareSelectionChange,\n          onSelectionToggle: handleSelectionToggle,\n          contextMenuItems: getContextMenuItems,\n          onContextMenuAction: handleContextMenuAction,\n          onStatusAction: handleStatusAction\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1477,\n          columnNumber: 13\n        }, this), caviSpare.length === 0 && !loading && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 2\n          },\n          children: \"Nessun cavo SPARE trovato. I cavi marcati come SPARE appariranno qui.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1490,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1471,\n        columnNumber: 11\n      }, this), renderDetailsDialog(), /*#__PURE__*/_jsxDEV(InserisciMetriDialogCompleto, {\n        open: inserisciMetriDialog.open,\n        onClose: handleCloseInserisciMetri,\n        cavo: inserisciMetriDialog.cavo,\n        cantiereId: cantiereId,\n        onSuccess: message => {\n          showNotification(message, 'success');\n          // Ricarica i dati per aggiornare lo stato\n          setTimeout(() => fetchCavi(true), 500);\n        },\n        onError: message => {\n          showNotification(message, 'error');\n        },\n        loading: inserisciMetriDialog.loading\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1508,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(ModificaBobinaDialogCompleto, {\n        open: modificaBobinaDialog.open,\n        onClose: handleCloseModificaBobina,\n        cavo: modificaBobinaDialog.cavo,\n        cantiereId: cantiereId,\n        onSuccess: handleModificaBobinaSuccess,\n        onError: handleModificaBobinaError\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1524,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: collegamentiDialog.open,\n        onClose: handleCloseCollegamenti,\n        maxWidth: \"md\",\n        fullWidth: true,\n        disableEscapeKeyDown: false,\n        keepMounted: false,\n        disablePortal: false,\n        disableScrollLock: true,\n        hideBackdrop: false,\n        disableAutoFocus: true,\n        disableEnforceFocus: true,\n        disableRestoreFocus: true,\n        transitionDuration: 0,\n        TransitionProps: {\n          timeout: 0,\n          appear: false,\n          enter: false,\n          exit: false\n        },\n        PaperProps: {\n          style: {\n            transition: 'none',\n            transform: 'none'\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: collegamentiDialog.cavo && /*#__PURE__*/_jsxDEV(CollegamentiCavo, {\n            cantiereId: cantiereId,\n            selectedCavo: collegamentiDialog.cavo,\n            onSuccess: message => {\n              if (message) {\n                showNotification(message, 'success');\n                // Chiudi il dialog immediatamente\n                setCollegamentiDialog(prev => ({\n                  ...prev,\n                  open: false\n                }));\n                // Ricarica i dati per aggiornare lo stato dei collegamenti\n                setTimeout(() => fetchCavi(true), 300);\n              }\n              // Non chiudere il dialog se message è null (annullamento)\n            },\n            onError: message => {\n              showNotification(message, 'error');\n            },\n            onClose: handleCloseCollegamenti\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1563,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1561,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1534,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(CreaComandaMultipla, {\n        open: createCommandDialog.open,\n        onClose: handleCloseCreateCommand,\n        onSuccess: handleCreateCommandSuccess,\n        onError: handleCreateCommandError,\n        tipoComanda: createCommandDialog.tipoComanda,\n        caviSelezionati: createCommandDialog.caviSelezionati,\n        cantiereId: cantiereId\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1586,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'none'\n        },\n        children: /*#__PURE__*/_jsxDEV(CertificazioneCaviImproved, {\n          ref: certificazioneRef,\n          cantiereId: cantiereId,\n          onSuccess: message => {\n            showNotification(message, 'success');\n            // Ricarica i dati per aggiornare lo stato di certificazione\n            setTimeout(() => fetchCavi(true), 500);\n          },\n          onError: message => {\n            showNotification(message, 'error');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1598,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1597,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n        open: notification.open,\n        autoHideDuration: 4000,\n        onClose: handleCloseNotification,\n        anchorOrigin: {\n          vertical: 'bottom',\n          horizontal: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          onClose: handleCloseNotification,\n          severity: notification.severity,\n          sx: {\n            width: '100%'\n          },\n          children: notification.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1619,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1613,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1362,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 1325,\n    columnNumber: 5\n  }, this);\n};\n_s(VisualizzaCaviPage, \"vsv/9L5sMq6yUWPSlH5iIRPD0v4=\", false, function () {\n  return [useAuth, useGlobalContext, useNavigate];\n});\n_c = VisualizzaCaviPage;\nexport default VisualizzaCaviPage;\nvar _c;\n$RefreshReg$(_c, \"VisualizzaCaviPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useRef", "Box", "Typography", "Paper", "<PERSON><PERSON>", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "IconButton", "Chip", "CircularProgress", "LinearProgress", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "Snackbar", "FormControl", "InputLabel", "Select", "MenuItem", "<PERSON><PERSON>", "InfoIcon", "Cable", "CableIcon", "CheckCircle", "CheckCircleIcon", "Schedule", "ScheduleIcon", "Link", "LinkIcon", "<PERSON><PERSON><PERSON>", "LinkOffIcon", "Timeline", "TimelineIcon", "CheckBox", "CheckBoxIcon", "CheckBoxOutlineBlank", "CheckBoxOutlineBlankIcon", "Visibility", "VisibilityIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "Add", "AddIcon", "SelectAll", "SelectAllIcon", "ContentCopy", "CopyIcon", "Settings", "SettingsIcon", "Verified", "VerifiedIcon", "Build", "BuildIcon", "useNavigate", "useAuth", "useGlobalContext", "caviService", "parcoCaviService", "CavoForm", "normalizeInstallationStatus", "CaviFilterableTable", "InserisciMetriDialogCompleto", "ModificaBobinaDialogCompleto", "CollegamentiCavo", "CertificazioneCaviImproved", "CreaComandaMultipla", "jsxDEV", "_jsxDEV", "VisualizzaCaviPage", "_s", "_caviAttivi$", "_caviAttivi$2", "_caviAttivi$3", "isImpersonating", "user", "openEliminaCavoDialog", "setOpenEliminaCavoDialog", "openModificaCavoDialog", "setOpenModificaCavoDialog", "openAggiungiCavoDialog", "setOpenAggiungiCavoDialog", "navigate", "cantiereId", "setCantiereId", "cantiereName", "setCantiereName", "caviAttivi", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "caviSpare", "setCaviSpare", "loading", "setLoading", "error", "setError", "notification", "setNotification", "open", "message", "severity", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "detailsDialogOpen", "setDetailsDialogOpen", "selectionEnabled", "setSelectionEnabled", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setSele<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectedCaviSpare", "setSelectedCaviSpare", "inserisciMetriDialog", "setInserisciMetriDialog", "cavo", "collegamentiDialog", "setCollegamentiDialog", "modificaBobinaDialog", "setModificaBobinaDialog", "certificazioneRef", "createCommandDialog", "setCreateCommandDialog", "tipoComanda", "caviSelezionati", "statistics", "setStatistics", "totaleCavi", "caviInstallati", "caviDaInstallare", "caviInCorso", "caviCollegati", "caviNonCollegati", "caviCertificati", "caviNonCertificati", "percentualeInstallazione", "percentualeCollegamento", "percentualeCertificazione", "iap", "metriTotali", "metriInstallati", "metriR<PERSON><PERSON><PERSON>", "revisioniDisponibili", "setRevisioniDisponibili", "revisioneSelezionata", "setRevisioneSelezionata", "revisioneCorrente", "setRevisioneCorrente", "calculateIAP", "nTot", "nInst", "nColl", "nCert", "Wp", "Wc", "Wz", "sforzoSoloInstallati", "sforzoSoloCollegati", "sforzoCertificati", "numeratore", "denominatore", "console", "log", "pesi", "Math", "round", "calculateStatistics", "caviAttiviData", "caviSpareData", "tuttiCavi", "length", "totale", "filter", "stato_installazione", "colle<PERSON>nti", "responsabile_partenza", "responsabile_arrivo", "certificato", "reduce", "sum", "parseFloat", "metri_te<PERSON>ci", "metratura_reale", "newStatistics", "loadStatiInstallazione", "setStatiInstallazione", "loadRevisioni", "cantiereIdToUse", "revisioneCorrenteData", "getRevisioneCorrente", "revisione_corrente", "revisioniData", "getRevisioniDisponibili", "revisioni", "handleRevisioneChange", "event", "nuovaRevisione", "target", "value", "filters", "setFilters", "tipologia", "sort_by", "sort_order", "statiInstallazione", "tipologieCavi", "setTipologieCavi", "<PERSON><PERSON><PERSON>", "silentLoading", "localStorage", "getItem", "attivi", "get<PERSON><PERSON>", "attiviError", "caviSpareTra<PERSON>ttivi", "modificato_manualmente", "spare", "getCaviSpare", "spareError", "standardError", "setTimeout", "document", "body", "textContent", "includes", "window", "location", "reload", "fetchData", "token", "selectedCantiereId", "selectedCantiereName", "i", "key", "role", "cantiere_id", "toString", "cantiere_name", "setItem", "base64Url", "split", "base64", "replace", "jsonPayload", "decodeURIComponent", "atob", "map", "c", "charCodeAt", "slice", "join", "payload", "JSON", "parse", "e", "warn", "cantiereIdNum", "parseInt", "isNaN", "timeoutPromise", "Promise", "_", "reject", "Error", "caviPromise", "race", "caviError", "status", "data", "stack", "code", "name", "response", "statusText", "sparePromise", "err", "_err$response", "_err$response2", "_err$response3", "_err$response4", "_err$response5", "_err$response5$data", "errorMessage", "detail", "handleOpenDetails", "handleCloseDetails", "handleCloseNotification", "prev", "showNotification", "handleSelectionToggle", "handleCaviAttiviSelectionChange", "selectedIds", "handleCaviSpareSelectionChange", "getAllSelectedCavi", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "id_cavo", "selected<PERSON><PERSON><PERSON><PERSON><PERSON>", "getTotalSelectedCount", "handleContextMenuAction", "action", "some", "isSelected", "id", "totalSelectedCount", "allSelectedCavi", "allIds", "navigator", "clipboard", "writeText", "totalSelected", "allDetails", "sezione", "details", "handleCreateMultipleCommand", "getQuickValidationStatus", "valid", "issues", "errors", "warnings", "handleCloseCreateCommand", "handleCreateCommandSuccess", "codice_comanda", "handleCreateCommandError", "getContextMenuItems", "hasMultipleSelection", "baseMenuItems", "type", "label", "icon", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "color", "validation", "description", "desc", "shortcut", "handleStatusAction", "actionType", "actionLabel", "current", "createCertificationForCavo", "viewCertificationForCavo", "generatePdfForCavo", "handleModificaBobinaSuccess", "handleModificaBobinaError", "handleCloseInserisciMetri", "handleCloseModificaBobina", "handleCloseCollegamenti", "renderDashboard", "sx", "p", "mb", "bgcolor", "children", "direction", "spacing", "alignItems", "justifyContent", "flexWrap", "variant", "fontWeight", "lineHeight", "width", "height", "borderRadius", "display", "renderDetailsDialog", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "dividers", "container", "item", "xs", "md", "gutterBottom", "sistema", "utility", "colore_cavo", "ubicazione_partenza", "utenza_partenza", "descrizione_utenza_partenza", "comanda_partenza", "ubicazione_arrivo", "utenza_arrivo", "descrizione_utenza_arrivo", "comanda_arrivo", "id_bobina", "responsabile_posa", "comanda_posa", "Date", "timestamp", "toLocaleString", "className", "flexDirection", "mt", "size", "gap", "min<PERSON><PERSON><PERSON>", "onChange", "rev", "revisione", "cavi_count", "process", "env", "NODE_ENV", "fontFamily", "Object", "keys", "stringify", "cavi", "onFilteredDataChange", "filteredData", "revisione_ufficiale", "<PERSON><PERSON><PERSON>", "onSelectionChange", "onSelectionToggle", "contextMenuItems", "onContextMenuAction", "onStatusAction", "onSuccess", "onError", "disableEscapeKeyDown", "keepMounted", "disable<PERSON><PERSON><PERSON>", "disableScrollLock", "hideBackdrop", "disableAutoFocus", "disableEnforceFocus", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transitionDuration", "TransitionProps", "timeout", "appear", "enter", "exit", "PaperProps", "style", "transition", "transform", "ref", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/pages/cavi/VisualizzaCaviPage.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useRef } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  Grid,\n  Card,\n  CardContent,\n  Alert,\n  IconButton,\n  Chip,\n  CircularProgress,\n  LinearProgress,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Snackbar,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Stack\n} from '@mui/material';\nimport InfoIcon from '@mui/icons-material/Info';\nimport {\n  Cable as CableIcon,\n  CheckCircle as CheckCircleIcon,\n  Schedule as ScheduleIcon,\n  Link as LinkIcon,\n  LinkOff as LinkOffIcon,\n  Timeline as TimelineIcon,\n  CheckBox as CheckBoxIcon,\n  CheckBoxOutlineBlank as CheckBoxOutlineBlankIcon,\n  Visibility as VisibilityIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  Add as AddIcon,\n  SelectAll as SelectAllIcon,\n  ContentCopy as CopyIcon,\n  Settings as SettingsIcon,\n  Verified as VerifiedIcon,\n  Build as BuildIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport { useGlobalContext } from '../../context/GlobalContext';\n// import PosaCaviCollegamenti from '../../components/cavi/PosaCaviCollegamenti'; // OBSOLETO: Componente eliminato\nimport caviService from '../../services/caviService';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport CavoForm from '../../components/cavi/CavoForm';\nimport { normalizeInstallationStatus } from '../../utils/validationUtils';\nimport CaviFilterableTable from '../../components/cavi/CaviFilterableTable';\nimport InserisciMetriDialogCompleto from '../../components/cavi/InserisciMetriDialogCompleto';\nimport ModificaBobinaDialogCompleto from '../../components/cavi/ModificaBobinaDialogCompleto';\nimport CollegamentiCavo from '../../components/cavi/CollegamentiCavo';\nimport CertificazioneCaviImproved from '../../components/cavi/CertificazioneCaviImproved';\nimport CreaComandaMultipla from '../../components/comande/CreaComandaMultipla';\n// import comandeValidationService from '../../services/comandeValidationService';\n\nimport './CaviPage.css';\n\nconst VisualizzaCaviPage = () => {\n  const { isImpersonating, user } = useAuth();\n  const { openEliminaCavoDialog, setOpenEliminaCavoDialog, openModificaCavoDialog, setOpenModificaCavoDialog, openAggiungiCavoDialog, setOpenAggiungiCavoDialog } = useGlobalContext();\n  const navigate = useNavigate();\n  const [cantiereId, setCantiereId] = useState(null);\n  const [cantiereName, setCantiereName] = useState('');\n  const [caviAttivi, setCaviAttivi] = useState([]);\n  const [caviSpare, setCaviSpare] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  // Stato per le notifiche\n  const [notification, setNotification] = useState({ open: false, message: '', severity: 'success' });\n  // Rimosso stato viewMode\n\n  // Stato per il dialogo dei dettagli del cavo\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);\n\n  // Stati per la selezione dei cavi\n  const [selectionEnabled, setSelectionEnabled] = useState(false);\n  const [selectedCaviAttivi, setSelectedCaviAttivi] = useState([]);\n  const [selectedCaviSpare, setSelectedCaviSpare] = useState([]);\n\n  // Stati per i dialoghi di azione sui pulsanti stato\n  const [inserisciMetriDialog, setInserisciMetriDialog] = useState({\n    open: false,\n    cavo: null,\n    loading: false\n  });\n\n  const [collegamentiDialog, setCollegamentiDialog] = useState({\n    open: false,\n    cavo: null,\n    loading: false\n  });\n  const [modificaBobinaDialog, setModificaBobinaDialog] = useState({\n    open: false,\n    cavo: null,\n    loading: false\n  });\n\n  // Ref per il componente CertificazioneCavi\n  const certificazioneRef = useRef(null);\n\n  // Stati per il dialog di creazione comande multiple\n  const [createCommandDialog, setCreateCommandDialog] = useState({\n    open: false,\n    tipoComanda: '',\n    caviSelezionati: [],\n    loading: false\n  });\n\n\n\n  // Stati per statistiche avanzate\n  const [statistics, setStatistics] = useState({\n    totaleCavi: 0,\n    caviInstallati: 0,\n    caviDaInstallare: 0,\n    caviInCorso: 0,\n    caviCollegati: 0,\n    caviNonCollegati: 0,\n    caviCertificati: 0,\n    caviNonCertificati: 0,\n    percentualeInstallazione: 0,\n    percentualeCollegamento: 0,\n    percentualeCertificazione: 0,\n    iap: 0, // Indice di Avanzamento Ponderato\n    metriTotali: 0,\n    metriInstallati: 0,\n    metriRimanenti: 0\n  });\n\n\n\n  // Stato per la gestione delle revisioni\n  const [revisioniDisponibili, setRevisioniDisponibili] = useState([]);\n  const [revisioneSelezionata, setRevisioneSelezionata] = useState('');\n  const [revisioneCorrente, setRevisioneCorrente] = useState('');\n\n  // Rimosso stato per il debug\n\n  // Funzione per calcolare l'Indice di Avanzamento Ponderato (IAP)\n  const calculateIAP = (nTot, nInst, nColl, nCert) => {\n    // Pesi per le fasi del progetto\n    const Wp = 2.0;  // Peso fase Posa\n    const Wc = 1.5;  // Peso fase Collegamento\n    const Wz = 0.5;  // Peso fase Certificazione\n\n    // Se non ci sono cavi, ritorna 0\n    if (nTot === 0) return 0;\n\n    // Calcolo del numeratore (Sforzo Completato)\n    const sforzoSoloInstallati = (nInst - nColl) * Wp;\n    const sforzoSoloCollegati = (nColl - nCert) * (Wp + Wc);\n    const sforzoCertificati = nCert * (Wp + Wc + Wz);\n    const numeratore = sforzoSoloInstallati + sforzoSoloCollegati + sforzoCertificati;\n\n    // Calcolo del denominatore (Sforzo Massimo Previsto)\n    const denominatore = nTot * (Wp + Wc + Wz);\n\n    // Calcolo finale dell'IAP in percentuale\n    const iap = (numeratore / denominatore) * 100;\n\n    console.log('Calcolo IAP:', {\n      nTot, nInst, nColl, nCert,\n      pesi: { Wp, Wc, Wz },\n      sforzoSoloInstallati,\n      sforzoSoloCollegati,\n      sforzoCertificati,\n      numeratore,\n      denominatore,\n      iap: Math.round(iap * 100) / 100\n    });\n\n    return Math.round(iap * 100) / 100; // Arrotonda a 2 decimali\n  };\n\n  // Funzione per calcolare le statistiche avanzate\n  const calculateStatistics = (caviAttiviData, caviSpareData) => {\n    const tuttiCavi = [...(caviAttiviData || []), ...(caviSpareData || [])];\n\n    if (tuttiCavi.length === 0) {\n      console.log('Nessun cavo disponibile per il calcolo delle statistiche');\n      return;\n    }\n\n    console.log('Calcolo statistiche con dati:', {\n      caviAttivi: caviAttiviData?.length || 0,\n      caviSpare: caviSpareData?.length || 0,\n      totale: tuttiCavi.length\n    });\n\n    const totaleCavi = tuttiCavi.length;\n\n    // Calcola stati di installazione\n    const caviInstallati = tuttiCavi.filter(cavo =>\n      cavo.stato_installazione === 'Installato' ||\n      cavo.stato_installazione === 'INSTALLATO' ||\n      cavo.stato_installazione === 'POSATO'\n    ).length;\n\n    const caviDaInstallare = tuttiCavi.filter(cavo =>\n      cavo.stato_installazione === 'Da installare' ||\n      cavo.stato_installazione === 'DA_INSTALLARE'\n    ).length;\n\n    const caviInCorso = tuttiCavi.filter(cavo =>\n      cavo.stato_installazione === 'In corso' ||\n      cavo.stato_installazione === 'IN_CORSO'\n    ).length;\n\n    // Calcola stati di collegamento\n    const caviCollegati = tuttiCavi.filter(cavo =>\n      cavo.collegamenti === 3 &&\n      cavo.responsabile_partenza &&\n      cavo.responsabile_arrivo\n    ).length;\n\n    const caviNonCollegati = totaleCavi - caviCollegati;\n\n    // Calcola certificazioni basandosi sul campo 'certificato' dei cavi\n    const caviCertificati = tuttiCavi.filter(cavo => cavo.certificato === true || cavo.certificato === 'true').length;\n\n    // Calcola l'Indice di Avanzamento Ponderato (IAP)\n    const iap = calculateIAP(totaleCavi, caviInstallati, caviCollegati, caviCertificati);\n\n    // Calcola percentuali tradizionali per confronto\n    const percentualeInstallazione = iap; // Sostituito con IAP\n    const percentualeCollegamento = totaleCavi > 0 ? Math.round((caviCollegati / totaleCavi) * 100) : 0;\n\n    // Calcola metri\n    const metriTotali = tuttiCavi.reduce((sum, cavo) => sum + (parseFloat(cavo.metri_teorici) || 0), 0);\n    const metriInstallati = tuttiCavi\n      .filter(cavo => cavo.stato_installazione === 'Installato' || cavo.stato_installazione === 'INSTALLATO' || cavo.stato_installazione === 'POSATO')\n      .reduce((sum, cavo) => sum + (parseFloat(cavo.metratura_reale) || parseFloat(cavo.metri_teorici) || 0), 0);\n    const metriRimanenti = metriTotali - metriInstallati;\n    const caviNonCertificati = totaleCavi - caviCertificati;\n    const percentualeCertificazione = totaleCavi > 0 ? Math.round((caviCertificati / totaleCavi) * 100) : 0;\n\n    const newStatistics = {\n      totaleCavi,\n      caviInstallati,\n      caviDaInstallare,\n      caviInCorso,\n      caviCollegati,\n      caviNonCollegati,\n      caviCertificati,\n      caviNonCertificati,\n      percentualeInstallazione,\n      percentualeCollegamento,\n      percentualeCertificazione,\n      iap, // Indice di Avanzamento Ponderato\n      metriTotali: Math.round(metriTotali),\n      metriInstallati: Math.round(metriInstallati),\n      metriRimanenti: Math.round(metriRimanenti)\n    };\n\n    console.log('Nuove statistiche calcolate:', newStatistics);\n    setStatistics(newStatistics);\n  };\n\n  // Funzione per caricare gli stati di installazione disponibili\n  const loadStatiInstallazione = () => {\n    // Usa i valori dell'enum StatoInstallazione\n    setStatiInstallazione(['Installato', 'Da installare', 'In corso']);\n  };\n\n  // Funzione per caricare le revisioni disponibili\n  const loadRevisioni = async (cantiereIdToUse) => {\n    try {\n      console.log('Caricamento revisioni per cantiere:', cantiereIdToUse);\n\n      // Carica la revisione corrente\n      const revisioneCorrenteData = await caviService.getRevisioneCorrente(cantiereIdToUse);\n      console.log('Revisione corrente:', revisioneCorrenteData);\n      setRevisioneCorrente(revisioneCorrenteData.revisione_corrente);\n\n      // Carica tutte le revisioni disponibili\n      const revisioniData = await caviService.getRevisioniDisponibili(cantiereIdToUse);\n      console.log('Revisioni disponibili:', revisioniData);\n      setRevisioniDisponibili(revisioniData.revisioni || []);\n\n      // LOGICA REVISIONI: La revisione corrente è quella di default\n      // Non impostiamo una revisione selezionata, così il sistema usa automaticamente la corrente\n      console.log('Logica revisioni: usando revisione corrente di default');\n    } catch (error) {\n      console.error('Errore nel caricamento delle revisioni:', error);\n    }\n  };\n\n  // Funzione per gestire il cambio di revisione\n  const handleRevisioneChange = (event) => {\n    const nuovaRevisione = event.target.value;\n\n    // LOGICA REVISIONI:\n    // - Se vuoto o \"corrente\" -> usa revisione corrente (non specificare parametro)\n    // - Se specifica -> usa quella revisione per visualizzazione storica\n    if (nuovaRevisione === '' || nuovaRevisione === 'corrente') {\n      setRevisioneSelezionata('');\n      console.log('Passaggio a revisione corrente (default)');\n    } else {\n      setRevisioneSelezionata(nuovaRevisione);\n      console.log('Passaggio a revisione storica:', nuovaRevisione);\n    }\n  };\n\n  // Stato per filtri e ordinamento\n  const [filters, setFilters] = useState({\n    stato_installazione: '',\n    tipologia: '',\n    sort_by: '',\n    sort_order: 'asc'\n  });\n\n  // Opzioni per i filtri\n  const [statiInstallazione, setStatiInstallazione] = useState([]);\n  const [tipologieCavi, setTipologieCavi] = useState([]);\n\n  // Rimossa funzione di debug\n\n  // Funzione per caricare i cavi\n  // Il parametro silentLoading permette di evitare di mostrare lo stato di caricamento\n  const fetchCavi = async (silentLoading = false) => {\n    try {\n      if (!silentLoading) {\n        setLoading(true);\n      }\n      console.log('Caricamento cavi per cantiere:', cantiereId);\n\n      // Verifica che cantiereId sia valido\n      if (!cantiereId) {\n        console.error('fetchCavi: cantiereId non valido:', cantiereId);\n        setError('ID cantiere non valido o mancante. Ricarica la pagina.');\n        setLoading(false);\n        return;\n      }\n\n      // Recupera il cantiereId dal localStorage come fallback\n      let cantiereIdToUse = cantiereId;\n      if (!cantiereIdToUse) {\n        cantiereIdToUse = localStorage.getItem('selectedCantiereId');\n        console.log('Usando cantiereId dal localStorage:', cantiereIdToUse);\n        if (!cantiereIdToUse) {\n          console.error('Impossibile trovare un ID cantiere valido');\n          setError('ID cantiere non trovato. Ricarica la pagina.');\n          setLoading(false);\n          return;\n        }\n      }\n\n      // Carica i cavi attivi\n      console.log('Caricamento cavi attivi (tipo_cavo=0)...');\n      let attivi = [];\n      try {\n        attivi = await caviService.getCavi(cantiereIdToUse, 0, filters);\n        console.log('Cavi attivi caricati:', attivi ? attivi.length : 0);\n      } catch (attiviError) {\n        console.error('Errore nel caricamento dei cavi attivi:', attiviError);\n        // Continua con un array vuoto\n        attivi = [];\n      }\n\n      // Verifica se ci sono cavi con modificato_manualmente = 3 tra i cavi attivi\n      if (attivi && attivi.length > 0) {\n        const caviSpareTraAttivi = attivi.filter(cavo => cavo.modificato_manualmente === 3);\n        if (caviSpareTraAttivi.length > 0) {\n          console.error('ERRORE: Trovati cavi con modificato_manualmente = 3 tra i cavi attivi:', caviSpareTraAttivi);\n        }\n      }\n\n      setCaviAttivi(attivi || []);\n\n      // Carica i cavi SPARE con la nuova funzione dedicata\n      let spare = [];\n      try {\n        console.log('Caricamento cavi SPARE con funzione dedicata...');\n        spare = await caviService.getCaviSpare(cantiereIdToUse);\n        console.log('Cavi SPARE caricati con funzione dedicata:', spare ? spare.length : 0);\n        if (spare && spare.length > 0) {\n          console.log('Primo cavo SPARE:', spare[0]);\n        }\n      } catch (spareError) {\n        console.error('Errore nel caricamento dei cavi SPARE con funzione dedicata:', spareError);\n        // Se fallisce, prova con il metodo standard\n        try {\n          console.log('Tentativo con metodo standard...');\n          spare = await caviService.getCavi(cantiereIdToUse, 3);\n          console.log('Cavi SPARE caricati con metodo standard:', spare ? spare.length : 0);\n        } catch (standardError) {\n          console.error('Errore anche con metodo standard:', standardError);\n          // Continua con un array vuoto\n          spare = [];\n        }\n      }\n      setCaviSpare(spare || []);\n\n\n\n      // Se siamo arrivati qui, rimuovi eventuali messaggi di errore precedenti\n      setError('');\n    } catch (error) {\n      console.error('Errore generale nel caricamento dei cavi:', error);\n      setError(`Errore nel caricamento dei cavi: ${error.message || 'Errore sconosciuto'}`);\n\n      // Prova a ricaricare la pagina dopo un ritardo se l'errore persiste\n      setTimeout(() => {\n        // Verifica se siamo ancora in errore\n        if (document.body.textContent.includes('Errore nel caricamento dei cavi')) {\n          console.log('Errore persistente, tentativo di ricaricamento della pagina...');\n          window.location.reload();\n        }\n      }, 5000); // 5 secondi di ritardo\n    } finally {\n      if (!silentLoading) {\n        setLoading(false);\n      }\n    }\n  };\n\n  // Carica i dati del cantiere e dei cavi\n  useEffect(() => {\n    // Carica gli stati di installazione all'avvio\n    loadStatiInstallazione();\n\n    const fetchData = async () => {\n      try {\n        console.log('Inizializzazione VisualizzaCaviPage...');\n\n        // Verifica che l'utente sia autenticato\n        const token = localStorage.getItem('token');\n        console.log('Token presente:', !!token);\n        if (!token) {\n          setError('Sessione scaduta. Effettua nuovamente il login.');\n          setLoading(false);\n          return;\n        }\n\n        // Recupera l'ID del cantiere selezionato dal localStorage\n        let selectedCantiereId = localStorage.getItem('selectedCantiereId');\n        let selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n        console.log('Cantiere selezionato dal localStorage:', { selectedCantiereId, selectedCantiereName });\n        console.log('Dati utente:', user);\n\n        // Stampa tutti i dati nel localStorage per debug\n        console.log('DEBUG - Tutti i dati nel localStorage:');\n        for (let i = 0; i < localStorage.length; i++) {\n          const key = localStorage.key(i);\n          console.log(`${key}: ${localStorage.getItem(key)}`);\n        }\n\n        // SOLUZIONE DIRETTA: Ottieni l'ID del cantiere direttamente dal token JWT\n        if (user?.role === 'cantieri_user') {\n          console.log('Utente cantiere rilevato, tentativo di recupero ID cantiere dai dati utente');\n\n          // Verifica se l'utente ha un ID cantiere nei dati utente\n          if (user.cantiere_id) {\n            console.log('Trovato ID cantiere nei dati utente:', user.cantiere_id);\n            selectedCantiereId = user.cantiere_id.toString();\n            selectedCantiereName = user.cantiere_name || `Cantiere ${user.cantiere_id}`;\n\n            // Salva l'ID e il nome del cantiere nel localStorage\n            localStorage.setItem('selectedCantiereId', selectedCantiereId);\n            localStorage.setItem('selectedCantiereName', selectedCantiereName);\n            console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n          } else {\n            // Tentativo di recupero dal token JWT\n            try {\n              console.log('Tentativo di decodifica del token JWT per recuperare l\\'ID cantiere');\n              const token = localStorage.getItem('token');\n              if (token) {\n                // Decodifica il token JWT (senza verifica della firma)\n                const base64Url = token.split('.')[1];\n                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n                const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => {\n                  return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n                }).join(''));\n\n                const payload = JSON.parse(jsonPayload);\n                console.log('Payload del token JWT:', payload);\n\n                if (payload.cantiere_id) {\n                  console.log('Trovato ID cantiere nel token JWT:', payload.cantiere_id);\n                  selectedCantiereId = payload.cantiere_id.toString();\n                  // Usa un nome generico se non disponibile\n                  selectedCantiereName = `Cantiere ${payload.cantiere_id}`;\n\n                  // Salva l'ID e il nome del cantiere nel localStorage\n                  localStorage.setItem('selectedCantiereId', selectedCantiereId);\n                  localStorage.setItem('selectedCantiereName', selectedCantiereName);\n                  console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n                }\n              }\n            } catch (e) {\n              console.error('Errore durante la decodifica del token JWT:', e);\n            }\n          }\n        }\n\n        // SOLUZIONE TEMPORANEA: Se ancora non abbiamo un ID cantiere, usa un valore hardcoded per debug\n        if (!selectedCantiereId || selectedCantiereId === 'undefined' || selectedCantiereId === 'null') {\n          console.warn('ATTENZIONE: Nessun ID cantiere trovato, utilizzo valore hardcoded per debug');\n          // Usa il primo cantiere disponibile (questo è solo per debug)\n          selectedCantiereId = '1'; // Sostituisci con un ID cantiere valido nel tuo database\n          selectedCantiereName = 'Cantiere Debug';\n\n          // Salva l'ID e il nome del cantiere nel localStorage\n          localStorage.setItem('selectedCantiereId', selectedCantiereId);\n          localStorage.setItem('selectedCantiereName', selectedCantiereName);\n          console.log('Salvato ID cantiere hardcoded nel localStorage:', selectedCantiereId);\n        }\n\n        // Verifica finale\n        if (!selectedCantiereId) {\n          setError('Nessun cantiere selezionato. Torna alla pagina dei cantieri.');\n          setLoading(false);\n          return;\n        }\n\n        // Verifica che l'ID del cantiere sia un numero valido\n        const cantiereIdNum = parseInt(selectedCantiereId, 10);\n        console.log('ID cantiere convertito a numero:', cantiereIdNum);\n        if (isNaN(cantiereIdNum)) {\n          setError(`ID cantiere non valido: ${selectedCantiereId}. Torna alla pagina dei cantieri.`);\n          setLoading(false);\n          return;\n        }\n\n        // Usa il numero convertito, non la stringa\n        setCantiereId(cantiereIdNum);\n        setCantiereName(selectedCantiereName || `Cantiere ${cantiereIdNum}`);\n\n        // Carica le revisioni disponibili\n        await loadRevisioni(cantiereIdNum);\n\n\n\n        // Carica i cavi attivi con gestione degli errori migliorata\n        console.log('Caricamento cavi attivi per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi attivi')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza e applica i filtri\n          console.log('Iniziando chiamata API per cavi attivi con filtri:', filters);\n          const caviPromise = caviService.getCavi(cantiereIdNum, 0, filters);\n          const attivi = await Promise.race([caviPromise, timeoutPromise]);\n\n          console.log('Cavi attivi caricati:', attivi);\n          console.log('Numero di cavi attivi trovati:', attivi ? attivi.length : 0);\n          if (attivi && attivi.length > 0) {\n            console.log('Primo cavo attivo:', attivi[0]);\n          } else {\n            console.warn('Nessun cavo attivo trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviAttivi(attivi || []);\n\n          // Calcola le statistiche dopo aver caricato i cavi attivi\n          calculateStatistics(attivi || [], caviSpare);\n        } catch (caviError) {\n          console.error('Errore nel caricamento dei cavi attivi:', caviError);\n          console.error('Dettagli errore cavi attivi:', {\n            message: caviError.message,\n            status: caviError.status,\n            data: caviError.data,\n            stack: caviError.stack,\n            code: caviError.code,\n            name: caviError.name,\n            response: caviError.response ? {\n              status: caviError.response.status,\n              statusText: caviError.response.statusText,\n              data: caviError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, continua con i cavi spare\n          setCaviAttivi([]);\n          console.warn('Continuazione del flusso dopo errore nei cavi attivi');\n\n          // Aggiungi un messaggio di errore visibile all'utente\n          setError(`Errore nel caricamento dei cavi attivi: ${caviError.message}. Controlla la console per maggiori dettagli.`);\n        }\n\n        // Carica i cavi spare con gestione degli errori migliorata\n        console.log('Caricamento cavi spare per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi spare')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza\n          console.log('Iniziando chiamata API per cavi spare...');\n          // Non applichiamo i filtri ai cavi spare, solo agli attivi\n          const sparePromise = caviService.getCavi(cantiereIdNum, 3);\n          const spare = await Promise.race([sparePromise, timeoutPromise]);\n\n          console.log('Cavi spare caricati:', spare);\n          console.log('Numero di cavi spare trovati:', spare ? spare.length : 0);\n          if (spare && spare.length > 0) {\n            console.log('Primo cavo spare:', spare[0]);\n          } else {\n            console.warn('Nessun cavo spare trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviSpare(spare || []);\n\n          // Calcola le statistiche dopo aver caricato i cavi spare\n          calculateStatistics(caviAttivi, spare || []);\n        } catch (spareError) {\n          console.error('Errore nel caricamento dei cavi spare:', spareError);\n          console.error('Dettagli errore cavi spare:', {\n            message: spareError.message,\n            status: spareError.status,\n            data: spareError.data,\n            stack: spareError.stack,\n            code: spareError.code,\n            name: spareError.name,\n            response: spareError.response ? {\n              status: spareError.response.status,\n              statusText: spareError.response.statusText,\n              data: spareError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, imposta un array vuoto\n          setCaviSpare([]);\n\n          // Aggiungi un messaggio di errore visibile all'utente se non c'è già un errore per i cavi attivi\n          if (!error) {\n            setError(`Errore nel caricamento dei cavi spare: ${spareError.message}. Controlla la console per maggiori dettagli.`);\n          }\n        }\n\n        // Se siamo arrivati qui, almeno abbiamo caricato l'interfaccia di base\n        setLoading(false);\n\n      } catch (err) {\n        console.error('Errore nel caricamento dei cavi:', err);\n        console.error('Dettagli errore generale:', {\n          message: err.message,\n          status: err.status || err.response?.status,\n          data: err.data || err.response?.data,\n          stack: err.stack\n        });\n\n        // Estrai il messaggio di errore dettagliato\n        let errorMessage = 'Errore sconosciuto';\n\n        if (err.message && err.message.includes('ID cantiere non valido')) {\n          errorMessage = err.message;\n        } else if (err.status === 401 || err.status === 403 ||\n                  err.response?.status === 401 || err.response?.status === 403) {\n          errorMessage = 'Sessione scaduta o non autorizzata. Effettua nuovamente il login.';\n        } else if (err.response?.data?.detail) {\n          // Estrai il messaggio di errore dettagliato dall'API\n          errorMessage = `Errore API: ${err.response.data.detail}`;\n        } else if (err.code === 'ERR_NETWORK') {\n          // Errore di rete\n          errorMessage = 'Network Error. Verifica che il backend sia in esecuzione e accessibile.';\n        } else if (err.message) {\n          errorMessage = err.message;\n        }\n\n        setError(`Impossibile caricare i cavi: ${errorMessage}. Riprova più tardi.`);\n\n        // Imposta array vuoti per evitare errori di rendering\n        setCaviAttivi([]);\n        setCaviSpare([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchData();\n  }, [filters]); // Ricarica i dati quando cambiano i filtri\n\n  // I filtri sono ora gestiti dal componente CaviFilterableTable\n\n  // Funzione per aprire il dialogo dei dettagli del cavo\n  const handleOpenDetails = (cavo) => {\n    setSelectedCavo(cavo);\n    setDetailsDialogOpen(true);\n  };\n\n  // Funzione per chiudere il dialogo dei dettagli del cavo\n  const handleCloseDetails = () => {\n    setDetailsDialogOpen(false);\n    setSelectedCavo(null);\n  };\n\n  // Funzione per chiudere la notifica\n  const handleCloseNotification = () => {\n    setNotification(prev => ({ ...prev, open: false }));\n  };\n\n  // Funzione per mostrare una notifica\n  const showNotification = (message, severity = 'success') => {\n    setNotification({ open: true, message, severity });\n  };\n\n  // Funzioni per gestire la selezione dei cavi\n  const handleSelectionToggle = () => {\n    setSelectionEnabled(!selectionEnabled);\n    // Pulisci le selezioni quando si disabilita la modalità selezione\n    if (selectionEnabled) {\n      setSelectedCaviAttivi([]);\n      setSelectedCaviSpare([]);\n    }\n  };\n\n  const handleCaviAttiviSelectionChange = (selectedIds) => {\n    setSelectedCaviAttivi(selectedIds);\n  };\n\n  const handleCaviSpareSelectionChange = (selectedIds) => {\n    setSelectedCaviSpare(selectedIds);\n  };\n\n  // Funzione per ottenere tutti i cavi selezionati\n  const getAllSelectedCavi = () => {\n    const selectedAttiviCavi = caviAttivi.filter(cavo => selectedCaviAttivi.includes(cavo.id_cavo));\n    const selectedSpareCavi = caviSpare.filter(cavo => selectedCaviSpare.includes(cavo.id_cavo));\n    return [...selectedAttiviCavi, ...selectedSpareCavi];\n  };\n\n  // Funzione per ottenere il conteggio totale dei cavi selezionati\n  const getTotalSelectedCount = () => {\n    return selectedCaviAttivi.length + selectedCaviSpare.length;\n  };\n\n  // Funzioni per gestire le azioni del menu contestuale\n  const handleContextMenuAction = (cavo, action) => {\n    console.log('Azione menu contestuale:', action, 'per cavo:', cavo);\n\n    switch (action) {\n      case 'view_details':\n        handleOpenDetails(cavo);\n        break;\n      case 'edit':\n        // Funzionalità di modifica cavo non ancora implementata\n        showNotification('Funzionalità di modifica cavo in sviluppo', 'info');\n        break;\n      case 'delete':\n        // Funzionalità di eliminazione cavo non ancora implementata\n        showNotification('Funzionalità di eliminazione cavo in sviluppo', 'info');\n        break;\n      case 'select':\n        if (caviAttivi.some(c => c.id_cavo === cavo.id_cavo)) {\n          // È un cavo attivo\n          const isSelected = selectedCaviAttivi.includes(cavo.id_cavo);\n          if (isSelected) {\n            setSelectedCaviAttivi(prev => prev.filter(id => id !== cavo.id_cavo));\n          } else {\n            setSelectedCaviAttivi(prev => [...prev, cavo.id_cavo]);\n          }\n        } else {\n          // È un cavo spare\n          const isSelected = selectedCaviSpare.includes(cavo.id_cavo);\n          if (isSelected) {\n            setSelectedCaviSpare(prev => prev.filter(id => id !== cavo.id_cavo));\n          } else {\n            setSelectedCaviSpare(prev => [...prev, cavo.id_cavo]);\n          }\n        }\n        // Abilita automaticamente la modalità selezione se non è già attiva\n        if (!selectionEnabled) {\n          setSelectionEnabled(true);\n        }\n        break;\n      case 'copy_id':\n        const totalSelectedCount = getTotalSelectedCount();\n        if (totalSelectedCount > 1) {\n          // Copia tutti gli ID dei cavi selezionati\n          const allSelectedCavi = getAllSelectedCavi();\n          const allIds = allSelectedCavi.map(c => c.id_cavo).join(', ');\n          navigator.clipboard.writeText(allIds);\n          showNotification(`${totalSelectedCount} IDs cavi copiati negli appunti`, 'success');\n        } else {\n          navigator.clipboard.writeText(cavo.id_cavo);\n          showNotification(`ID cavo ${cavo.id_cavo} copiato negli appunti`, 'success');\n        }\n        break;\n      case 'copy_details':\n        const totalSelected = getTotalSelectedCount();\n        if (totalSelected > 1) {\n          // Copia dettagli di tutti i cavi selezionati\n          const allSelectedCavi = getAllSelectedCavi();\n          const allDetails = allSelectedCavi.map(c =>\n            `ID: ${c.id_cavo}\\nTipologia: ${c.tipologia}\\nSezione: ${c.sezione}\\nMetri: ${c.metri_teorici}`\n          ).join('\\n\\n');\n          navigator.clipboard.writeText(allDetails);\n          showNotification(`Dettagli di ${totalSelected} cavi copiati negli appunti`, 'success');\n        } else {\n          const details = `ID: ${cavo.id_cavo}\\nTipologia: ${cavo.tipologia}\\nSezione: ${cavo.sezione}\\nMetri: ${cavo.metri_teorici}`;\n          navigator.clipboard.writeText(details);\n          showNotification('Dettagli cavo copiati negli appunti', 'success');\n        }\n        break;\n      case 'add_new':\n        // Funzionalità di aggiunta cavo non ancora implementata\n        showNotification('Funzionalità di aggiunta cavo in sviluppo', 'info');\n        break;\n      // Nuove azioni per la creazione di comande multiple\n      case 'create_command_posa':\n        handleCreateMultipleCommand('POSA');\n        break;\n      case 'create_command_collegamento_partenza':\n        handleCreateMultipleCommand('COLLEGAMENTO_PARTENZA');\n        break;\n      case 'create_command_collegamento_arrivo':\n        handleCreateMultipleCommand('COLLEGAMENTO_ARRIVO');\n        break;\n      case 'create_command_certificazione':\n        handleCreateMultipleCommand('CERTIFICAZIONE');\n        break;\n      default:\n        console.warn('Azione non riconosciuta:', action);\n    }\n  };\n\n  // Funzione per controllare rapidamente la validazione di un tipo di comanda\n  const getQuickValidationStatus = (tipoComanda) => {\n    // Temporaneamente disabilitato per debug - restituisce sempre valido\n    return { valid: true, issues: 0, errors: 0, warnings: 0 };\n  };\n\n  // Funzione per gestire la creazione di comande multiple\n  const handleCreateMultipleCommand = (tipoComanda) => {\n    const allSelectedCavi = getAllSelectedCavi();\n\n    if (allSelectedCavi.length === 0) {\n      showNotification('Nessun cavo selezionato per la creazione della comanda', 'warning');\n      return;\n    }\n\n    console.log(`Creazione comanda ${tipoComanda} per ${allSelectedCavi.length} cavi:`, allSelectedCavi.map(c => c.id_cavo));\n\n    // Apri il dialog di creazione comanda con i cavi preselezionati\n    setCreateCommandDialog({\n      open: true,\n      tipoComanda: tipoComanda,\n      caviSelezionati: allSelectedCavi,\n      loading: false\n    });\n  };\n\n  // Funzione per chiudere il dialog di creazione comande\n  const handleCloseCreateCommand = () => {\n    setCreateCommandDialog({\n      open: false,\n      tipoComanda: '',\n      caviSelezionati: [],\n      loading: false\n    });\n  };\n\n  // Funzione per gestire il successo della creazione comanda\n  const handleCreateCommandSuccess = (response) => {\n    showNotification(`Comanda ${response.codice_comanda} creata con successo!`, 'success');\n\n    // Deseleziona tutti i cavi\n    setSelectedCaviAttivi([]);\n    setSelectedCaviSpare([]);\n\n    // Chiudi il dialog\n    handleCloseCreateCommand();\n\n    // Ricarica i dati per aggiornare lo stato\n    setTimeout(() => fetchCavi(true), 500);\n  };\n\n  // Funzione per gestire gli errori nella creazione comanda\n  const handleCreateCommandError = (error) => {\n    console.error('Errore nella creazione della comanda:', error);\n    showNotification('Errore nella creazione della comanda', 'error');\n  };\n\n  // Definizione degli elementi del menu contestuale\n  const getContextMenuItems = (cavo) => {\n    const isSelected = caviAttivi.some(c => c.id_cavo === cavo?.id_cavo)\n      ? selectedCaviAttivi.includes(cavo?.id_cavo)\n      : selectedCaviSpare.includes(cavo?.id_cavo);\n\n    const totalSelectedCount = getTotalSelectedCount();\n    const hasMultipleSelection = totalSelectedCount > 1;\n\n    // Menu base per singolo cavo\n    const baseMenuItems = [\n      {\n        type: 'header',\n        label: hasMultipleSelection ? `${totalSelectedCount} cavi selezionati` : `Cavo ${cavo?.id_cavo || ''}`\n      },\n      // Sezione comande multiple (solo se ci sono più cavi selezionati)\n      ...(hasMultipleSelection ? [\n        {\n          type: 'header',\n          label: `📋 Crea Comande Multiple (${totalSelectedCount} cavi)`\n        },\n        {\n          id: 'create_command_posa',\n          label: 'Comanda Posa',\n          icon: <BuildIcon fontSize=\"small\" />,\n          action: 'create_command_posa',\n          onClick: handleContextMenuAction,\n          color: (() => {\n            const validation = getQuickValidationStatus('POSA');\n            return validation.errors > 0 ? 'error' : validation.warnings > 0 ? 'warning' : 'primary';\n          })(),\n          description: (() => {\n            const validation = getQuickValidationStatus('POSA');\n            let desc = `Crea comanda posa per ${totalSelectedCount} cavi`;\n            if (validation.issues > 0) {\n              desc += ` (${validation.errors} errori, ${validation.warnings} avvisi)`;\n            }\n            return desc;\n          })()\n        },\n        {\n          id: 'create_command_collegamento_partenza',\n          label: 'Comanda Collegamento Partenza',\n          icon: <LinkIcon fontSize=\"small\" />,\n          action: 'create_command_collegamento_partenza',\n          onClick: handleContextMenuAction,\n          color: (() => {\n            const validation = getQuickValidationStatus('COLLEGAMENTO_PARTENZA');\n            return validation.errors > 0 ? 'error' : validation.warnings > 0 ? 'warning' : 'primary';\n          })(),\n          description: (() => {\n            const validation = getQuickValidationStatus('COLLEGAMENTO_PARTENZA');\n            let desc = `Crea comanda collegamento partenza per ${totalSelectedCount} cavi`;\n            if (validation.issues > 0) {\n              desc += ` (${validation.errors} errori, ${validation.warnings} avvisi)`;\n            }\n            return desc;\n          })()\n        },\n        {\n          id: 'create_command_collegamento_arrivo',\n          label: 'Comanda Collegamento Arrivo',\n          icon: <LinkIcon fontSize=\"small\" />,\n          action: 'create_command_collegamento_arrivo',\n          onClick: handleContextMenuAction,\n          color: (() => {\n            const validation = getQuickValidationStatus('COLLEGAMENTO_ARRIVO');\n            return validation.errors > 0 ? 'error' : validation.warnings > 0 ? 'warning' : 'primary';\n          })(),\n          description: (() => {\n            const validation = getQuickValidationStatus('COLLEGAMENTO_ARRIVO');\n            let desc = `Crea comanda collegamento arrivo per ${totalSelectedCount} cavi`;\n            if (validation.issues > 0) {\n              desc += ` (${validation.errors} errori, ${validation.warnings} avvisi)`;\n            }\n            return desc;\n          })()\n        },\n        {\n          id: 'create_command_certificazione',\n          label: 'Comanda Certificazione',\n          icon: <VerifiedIcon fontSize=\"small\" />,\n          action: 'create_command_certificazione',\n          onClick: handleContextMenuAction,\n          color: (() => {\n            const validation = getQuickValidationStatus('CERTIFICAZIONE');\n            return validation.errors > 0 ? 'error' : validation.warnings > 0 ? 'warning' : 'primary';\n          })(),\n          description: (() => {\n            const validation = getQuickValidationStatus('CERTIFICAZIONE');\n            let desc = `Crea comanda certificazione per ${totalSelectedCount} cavi`;\n            if (validation.issues > 0) {\n              desc += ` (${validation.errors} errori, ${validation.warnings} avvisi)`;\n            }\n            return desc;\n          })()\n        },\n        {\n          type: 'divider'\n        }\n      ] : []),\n      // Azioni singolo cavo (solo se non c'è selezione multipla)\n      ...(!hasMultipleSelection ? [\n        {\n          id: 'view_details',\n          label: 'Visualizza Dettagli',\n          icon: <VisibilityIcon fontSize=\"small\" />,\n          action: 'view_details',\n          onClick: handleContextMenuAction\n        },\n        {\n          type: 'divider'\n        },\n        {\n          id: 'edit',\n          label: 'Modifica',\n          icon: <EditIcon fontSize=\"small\" />,\n          action: 'edit',\n          onClick: handleContextMenuAction,\n          color: 'primary'\n        },\n        {\n          id: 'delete',\n          label: 'Elimina',\n          icon: <DeleteIcon fontSize=\"small\" />,\n          action: 'delete',\n          onClick: handleContextMenuAction,\n          color: 'error'\n        },\n        {\n          type: 'divider'\n        },\n        {\n          id: 'add_new',\n          label: 'Aggiungi nuovo cavo',\n          icon: <AddIcon fontSize=\"small\" />,\n          action: 'add_new',\n          onClick: handleContextMenuAction,\n          color: 'success'\n        },\n        {\n          type: 'divider'\n        }\n      ] : []),\n      // Azioni di selezione (sempre presenti)\n      {\n        id: 'select',\n        label: isSelected ? 'Deseleziona' : 'Seleziona',\n        icon: <SelectAllIcon fontSize=\"small\" />,\n        action: 'select',\n        onClick: handleContextMenuAction,\n        color: isSelected ? 'warning' : 'success'\n      },\n      // Azioni di copia (sempre presenti)\n      {\n        type: 'divider'\n      },\n      {\n        id: 'copy_id',\n        label: hasMultipleSelection ? 'Copia IDs Selezionati' : 'Copia ID',\n        icon: <CopyIcon fontSize=\"small\" />,\n        action: 'copy_id',\n        onClick: handleContextMenuAction,\n        shortcut: 'Ctrl+C'\n      },\n      {\n        id: 'copy_details',\n        label: hasMultipleSelection ? 'Copia Dettagli Selezionati' : 'Copia Dettagli',\n        icon: <CopyIcon fontSize=\"small\" />,\n        action: 'copy_details',\n        onClick: handleContextMenuAction,\n        description: hasMultipleSelection ? 'Copia dettagli di tutti i cavi selezionati' : 'Copia ID, tipologia, sezione e metri'\n      }\n    ];\n\n    return baseMenuItems;\n  };\n\n  // Funzioni per gestire le azioni sui pulsanti stato\n  const handleStatusAction = async (cavo, actionType, actionLabel) => {\n    console.log('🎯 CLICK PULSANTE STATO RILEVATO!');\n    console.log('Azione pulsante stato:', actionType, 'per cavo:', cavo);\n    console.log('Action label:', actionLabel);\n\n    if (actionType === 'insert_meters') {\n      // Apri il dialogo per inserire i metri posati\n      setInserisciMetriDialog({\n        open: true,\n        cavo: cavo,\n        loading: false\n      });\n    } else if (actionType === 'modify_reel') {\n      // Apri il dialog completo per modificare la bobina con cavo preselezionato\n      console.log('Apertura dialog modifica bobina per cavo:', cavo.id_cavo);\n      setModificaBobinaDialog({\n        open: true,\n        cavo: cavo,\n        loading: false\n      });\n    } else if (actionType === 'connect_cable' || actionType === 'connect_arrival' ||\n               actionType === 'connect_departure' || actionType === 'disconnect_cable' ||\n               actionType === 'manage_connections') {\n\n      // Verifica se il cavo è installato\n      if (cavo.stato_installazione !== 'Installato') {\n        console.log('Azione collegamenti ignorata per cavo non installato:', cavo.id_cavo);\n        showNotification('I collegamenti sono disponibili solo per cavi installati', 'info');\n        return;\n      }\n\n      // Cavo installato - apri il popup per gestire i collegamenti\n      console.log('Apertura popup collegamenti per cavo installato:', cavo.id_cavo, 'azione:', actionType);\n\n      // Usa setTimeout per evitare conflitti di stato\n      setTimeout(() => {\n        setCollegamentiDialog({\n          open: true,\n          cavo: cavo,\n          loading: false\n        });\n      }, 50);\n    } else if (actionType === 'create_certificate') {\n      // Apri il dialog per creare una certificazione usando il componente esistente\n      console.log('Apertura dialog creazione certificazione per cavo:', cavo.id_cavo);\n\n      // Usa il componente CertificazioneCavi esistente con tutti i prerequisiti\n      if (certificazioneRef.current) {\n        certificazioneRef.current.createCertificationForCavo(cavo);\n      }\n    } else if (actionType === 'view_certificate') {\n      // Apri il dialog per visualizzare la certificazione esistente\n      console.log('Apertura dialog visualizzazione certificazione per cavo:', cavo.id_cavo);\n\n      // Usa il componente CertificazioneCavi esistente per visualizzare\n      if (certificazioneRef.current) {\n        certificazioneRef.current.viewCertificationForCavo(cavo);\n      }\n    } else if (actionType === 'generate_pdf') {\n      // Genera il PDF del certificato per il cavo\n      console.log('Generazione PDF certificato per cavo:', cavo.id_cavo);\n\n      // Usa il componente CertificazioneCavi esistente per generare il PDF\n      if (certificazioneRef.current) {\n        certificazioneRef.current.generatePdfForCavo(cavo);\n      }\n    }\n  };\n\n\n\n\n  // Funzioni per gestire i callback del dialog modifica bobina\n  const handleModificaBobinaSuccess = (message) => {\n    showNotification(message, 'success');\n    // Ricarica i dati per aggiornare lo stato\n    setTimeout(() => fetchCavi(true), 500);\n  };\n\n  const handleModificaBobinaError = (message) => {\n    showNotification(message, 'error');\n  };\n\n  // Funzioni per chiudere i dialoghi\n  const handleCloseInserisciMetri = () => {\n    if (!inserisciMetriDialog.loading) {\n      setInserisciMetriDialog({ open: false, cavo: null, loading: false });\n    }\n  };\n\n  const handleCloseModificaBobina = () => {\n    if (!modificaBobinaDialog.loading) {\n      setModificaBobinaDialog({ open: false, cavo: null, loading: false });\n    }\n  };\n\n  const handleCloseCollegamenti = useCallback(() => {\n    if (!collegamentiDialog.loading) {\n      setCollegamentiDialog(prev => ({ ...prev, open: false }));\n    }\n  }, [collegamentiDialog.loading]);\n\n\n\n\n\n  // Nessuna funzione di navigazione necessaria, tutto è gestito dal menu principale\n\n  // Dashboard minimal con statistiche essenziali per visualizzazione cavi\n  const renderDashboard = () => (\n    <Paper sx={{ p: 2, mb: 3, bgcolor: 'grey.50' }}>\n      <Stack direction=\"row\" spacing={4} alignItems=\"center\" justifyContent=\"space-between\" flexWrap=\"wrap\">\n        {/* Statistiche essenziali in formato compatto */}\n        <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n          <CableIcon color=\"primary\" fontSize=\"small\" />\n          <Box>\n            <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n              {statistics.totaleCavi}\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              Totale\n            </Typography>\n          </Box>\n        </Stack>\n\n        <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n          <CheckCircleIcon color=\"success\" fontSize=\"small\" />\n          <Box>\n            <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n              {statistics.caviInstallati}\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              Installati\n            </Typography>\n          </Box>\n        </Stack>\n\n        <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n          <LinkIcon color=\"info\" fontSize=\"small\" />\n          <Box>\n            <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n              {statistics.caviCollegati}\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              Collegati\n            </Typography>\n          </Box>\n        </Stack>\n\n        <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n          <VerifiedIcon color=\"warning\" fontSize=\"small\" />\n          <Box>\n            <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n              {statistics.caviCertificati}\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              Certificati\n            </Typography>\n          </Box>\n        </Stack>\n\n        <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n          <Box sx={{\n            width: 32,\n            height: 32,\n            borderRadius: '50%',\n            bgcolor: statistics.iap >= 80 ? 'success.main' :\n                     statistics.iap >= 50 ? 'warning.main' : 'error.main',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          }}>\n            <Typography variant=\"caption\" fontWeight=\"bold\" color=\"white\">\n              {statistics.iap}%\n            </Typography>\n          </Box>\n          <Box>\n            <Typography variant=\"body2\" fontWeight=\"medium\" sx={{ lineHeight: 1 }}>\n              IAP (Avanzamento Ponderato)\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              {statistics.metriInstallati}m installati\n            </Typography>\n          </Box>\n        </Stack>\n      </Stack>\n    </Paper>\n  );\n\n  // La visualizzazione dei cavi è ora gestita dal componente CaviFilterableTable\n\n  // Rimossa funzione handleViewModeChange\n\n  // Renderizza il dialogo dei dettagli del cavo\n  const renderDetailsDialog = () => {\n    if (!selectedCavo) return null;\n\n    return (\n      <Dialog open={detailsDialogOpen} onClose={handleCloseDetails} maxWidth=\"md\" fullWidth>\n        <DialogTitle>\n          Dettagli Cavo: {selectedCavo.id_cavo}\n        </DialogTitle>\n        <DialogContent dividers>\n          <Grid container spacing={2}>\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle1\" gutterBottom>Informazioni Generali</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Sistema:</strong> {selectedCavo.sistema || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Utility:</strong> {selectedCavo.utility || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Tipologia:</strong> {selectedCavo.tipologia || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Colore:</strong> {selectedCavo.colore_cavo || 'N/A'}</Typography>\n                {/* n_conduttori field is now a spare field (kept in DB but hidden in UI) */}\n                <Typography variant=\"body2\"><strong>Formazione:</strong> {selectedCavo.sezione || 'N/A'}</Typography>\n                {/* sh field is now a spare field (kept in DB but hidden in UI) */}\n              </Box>\n\n              <Typography variant=\"subtitle1\" gutterBottom>Partenza</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Ubicazione:</strong> {selectedCavo.ubicazione_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Utenza:</strong> {selectedCavo.utenza_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Descrizione:</strong> {selectedCavo.descrizione_utenza_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Responsabile:</strong> {selectedCavo.responsabile_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Comanda:</strong> {selectedCavo.comanda_partenza || 'N/A'}</Typography>\n              </Box>\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle1\" gutterBottom>Arrivo</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Ubicazione:</strong> {selectedCavo.ubicazione_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Utenza:</strong> {selectedCavo.utenza_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Descrizione:</strong> {selectedCavo.descrizione_utenza_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Responsabile:</strong> {selectedCavo.responsabile_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Comanda:</strong> {selectedCavo.comanda_arrivo || 'N/A'}</Typography>\n              </Box>\n\n              <Typography variant=\"subtitle1\" gutterBottom>Installazione</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Metri Teorici:</strong> {selectedCavo.metri_teorici || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Metratura Reale:</strong> {selectedCavo.metratura_reale || '0'}</Typography>\n                <Typography variant=\"body2\"><strong>Stato:</strong> {normalizeInstallationStatus(selectedCavo.stato_installazione)}</Typography>\n                <Typography variant=\"body2\"><strong>Collegamenti:</strong> {selectedCavo.collegamenti || '0'}</Typography>\n                <Typography variant=\"body2\"><strong>Bobina:</strong> {selectedCavo.id_bobina || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Responsabile Posa:</strong> {selectedCavo.responsabile_posa || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Comanda Posa:</strong> {selectedCavo.comanda_posa || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Ultimo Aggiornamento:</strong> {new Date(selectedCavo.timestamp).toLocaleString()}</Typography>\n              </Box>\n            </Grid>\n          </Grid>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handleCloseDetails}>Chiudi</Button>\n        </DialogActions>\n      </Dialog>\n    );\n  };\n\n  // Il pannello dei filtri è ora gestito dal componente CaviFilterableTable\n\n\n\n  return (\n    <Box className=\"cavi-page\">\n      {loading ? (\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mt: 4 }}>\n          <CircularProgress size={40} />\n          <Typography sx={{ mt: 2 }}>Caricamento cavi...</Typography>\n          <Button\n            variant=\"outlined\"\n            color=\"primary\"\n            onClick={() => window.location.reload()}\n            sx={{ mt: 2 }}\n          >\n            Ricarica la pagina\n          </Button>\n        </Box>\n      ) : error ? (\n        <Box>\n          <Alert severity=\"error\" sx={{ mb: 2 }}>\n            {error}\n            {error.includes('Network Error') && (\n              <Typography variant=\"body2\" sx={{ mt: 1 }}>\n                <strong>Suggerimento:</strong> Verifica che il server backend sia in esecuzione sulla porta 8001.\n                <br />\n                Puoi avviare il backend eseguendo il file <code>run_system.py</code> nella cartella principale del progetto.\n              </Typography>\n            )}\n          </Alert>\n          <Box sx={{ display: 'flex', gap: 2 }}>\n            <Button\n              variant=\"contained\"\n              className=\"primary-button\"\n              onClick={() => window.location.reload()}\n            >\n              Ricarica la pagina\n            </Button>\n          </Box>\n        </Box>\n      ) : (\n        <Box>\n          {/* Selettore Revisione */}\n          {revisioniDisponibili.length > 0 && (\n            <Paper sx={{ p: 2, mb: 3 }}>\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                <Typography variant=\"h6\">Visualizzazione:</Typography>\n                <FormControl size=\"small\" sx={{ minWidth: 250 }}>\n                  <InputLabel>Revisione da Visualizzare</InputLabel>\n                  <Select\n                    value={revisioneSelezionata || 'corrente'}\n                    onChange={handleRevisioneChange}\n                    label=\"Revisione da Visualizzare\"\n                  >\n                    <MenuItem value=\"corrente\">\n                      📋 Revisione Corrente {revisioneCorrente && `(${revisioneCorrente})`}\n                    </MenuItem>\n                    {revisioniDisponibili.map((rev) => (\n                      <MenuItem key={rev.revisione} value={rev.revisione}>\n                        📚 {rev.revisione} ({rev.cavi_count} cavi)\n                        {rev.revisione === revisioneCorrente && ' - Attuale'}\n                      </MenuItem>\n                    ))}\n                  </Select>\n                </FormControl>\n                <Chip\n                  label={\n                    revisioneSelezionata\n                      ? `Storico: ${revisioneSelezionata}`\n                      : `Corrente: ${revisioneCorrente || 'N/A'}`\n                  }\n                  color={revisioneSelezionata ? \"secondary\" : \"primary\"}\n                  variant=\"outlined\"\n                />\n              </Box>\n            </Paper>\n          )}\n\n          {/* Dashboard con statistiche avanzate */}\n          {renderDashboard()}\n\n          {/* Sezione Cavi */}\n          <Box sx={{ mt: 4 }}>\n            {/* Banner informativo per modalità selezione */}\n            {selectionEnabled && (\n              <Alert\n                severity=\"info\"\n                sx={{ mb: 2 }}\n                action={\n                  <Button\n                    color=\"inherit\"\n                    size=\"small\"\n                    onClick={() => setSelectionEnabled(false)}\n                  >\n                    Disabilita\n                  </Button>\n                }\n              >\n                <Typography variant=\"body2\">\n                  <strong>Modalità Selezione Attiva</strong> - Clicca sui cavi per selezionarli,\n                  poi usa il <strong>tasto destro</strong> per creare comande multiple.\n                  {getTotalSelectedCount() > 0 && (\n                    <span> <strong>{getTotalSelectedCount()} cavi selezionati</strong></span>\n                  )}\n                </Typography>\n              </Alert>\n            )}\n\n            {/* Contatore selezione compatto - solo quando ci sono cavi selezionati */}\n            {selectionEnabled && getTotalSelectedCount() > 0 && (\n              <Box sx={{ mb: 2 }}>\n                <Chip\n                  label={`${getTotalSelectedCount()} cavi selezionati`}\n                  color=\"primary\"\n                  variant=\"filled\"\n                  size=\"small\"\n                />\n              </Box>\n            )}\n\n            {/* Debug: Mostra le proprietà del primo cavo per verificare il nome del campo revisione */}\n            {process.env.NODE_ENV === 'development' && caviAttivi.length > 0 && (\n              <Box sx={{ mb: 2, p: 1, bgcolor: '#f0f0f0', borderRadius: 1, fontSize: '0.8rem', fontFamily: 'monospace', display: 'none' }}>\n                {Object.keys(caviAttivi[0]).map(key => (\n                  <div key={key}>{key}: {JSON.stringify(caviAttivi[0][key])}</div>\n                ))}\n              </Box>\n            )}\n\n            <CaviFilterableTable\n              cavi={caviAttivi}\n              loading={loading}\n              onFilteredDataChange={(filteredData) => console.log('Cavi attivi filtrati:', filteredData.length)}\n              revisioneCorrente={caviAttivi[0]?.revisione_ufficiale || caviAttivi[0]?.revisione || caviAttivi[0]?.rev}\n              selectionEnabled={selectionEnabled}\n              selectedCavi={selectedCaviAttivi}\n              onSelectionChange={handleCaviAttiviSelectionChange}\n              onSelectionToggle={handleSelectionToggle}\n              contextMenuItems={getContextMenuItems}\n              onContextMenuAction={handleContextMenuAction}\n              onStatusAction={handleStatusAction}\n            />\n            {caviAttivi.length === 0 && !loading && (\n              <Alert severity=\"info\" sx={{ mt: 2 }}>\n                Nessun cavo attivo trovato. I cavi attivi appariranno qui.\n              </Alert>\n            )}\n          </Box>\n\n          {/* Sezione Cavi Spare */}\n          <Box sx={{ mt: 4 }}>\n            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n              <Typography variant=\"h5\">\n                Cavi Spare {caviSpare.length > 0 ? `(${caviSpare.length})` : ''}\n              </Typography>\n            </Box>\n            <CaviFilterableTable\n              cavi={caviSpare}\n              loading={loading}\n              onFilteredDataChange={(filteredData) => console.log('Cavi spare filtrati:', filteredData.length)}\n              selectionEnabled={selectionEnabled}\n              selectedCavi={selectedCaviSpare}\n              onSelectionChange={handleCaviSpareSelectionChange}\n              onSelectionToggle={handleSelectionToggle}\n              contextMenuItems={getContextMenuItems}\n              onContextMenuAction={handleContextMenuAction}\n              onStatusAction={handleStatusAction}\n            />\n            {caviSpare.length === 0 && !loading && (\n              <Alert severity=\"info\" sx={{ mt: 2 }}>\n                Nessun cavo SPARE trovato. I cavi marcati come SPARE appariranno qui.\n              </Alert>\n            )}\n          </Box>\n\n          {/* Rimossa sezione Debug */}\n\n          {/* Dialogo dei dettagli del cavo */}\n          {renderDetailsDialog()}\n\n          {/* OBSOLETO: Dialog eliminazione cavo rimosso - Funzionalità integrata nel menu contestuale */}\n\n          {/* OBSOLETO: Dialog modifica cavo rimosso - Funzionalità integrata nel menu contestuale */}\n\n          {/* OBSOLETO: Dialog aggiunta cavo rimosso - Funzionalità integrata nel menu contestuale */}\n\n          {/* Dialoghi per azioni sui pulsanti stato */}\n          <InserisciMetriDialogCompleto\n            open={inserisciMetriDialog.open}\n            onClose={handleCloseInserisciMetri}\n            cavo={inserisciMetriDialog.cavo}\n            cantiereId={cantiereId}\n            onSuccess={(message) => {\n              showNotification(message, 'success');\n              // Ricarica i dati per aggiornare lo stato\n              setTimeout(() => fetchCavi(true), 500);\n            }}\n            onError={(message) => {\n              showNotification(message, 'error');\n            }}\n            loading={inserisciMetriDialog.loading}\n          />\n\n          <ModificaBobinaDialogCompleto\n            open={modificaBobinaDialog.open}\n            onClose={handleCloseModificaBobina}\n            cavo={modificaBobinaDialog.cavo}\n            cantiereId={cantiereId}\n            onSuccess={handleModificaBobinaSuccess}\n            onError={handleModificaBobinaError}\n          />\n\n          {/* Dialog per la gestione collegamenti */}\n          <Dialog\n            open={collegamentiDialog.open}\n            onClose={handleCloseCollegamenti}\n            maxWidth=\"md\"\n            fullWidth\n            disableEscapeKeyDown={false}\n            keepMounted={false}\n            disablePortal={false}\n            disableScrollLock={true}\n            hideBackdrop={false}\n            disableAutoFocus={true}\n            disableEnforceFocus={true}\n            disableRestoreFocus={true}\n            transitionDuration={0}\n            TransitionProps={{\n              timeout: 0,\n              appear: false,\n              enter: false,\n              exit: false\n            }}\n            PaperProps={{\n              style: {\n                transition: 'none',\n                transform: 'none'\n              }\n            }}\n          >\n            <DialogContent>\n              {collegamentiDialog.cavo && (\n                <CollegamentiCavo\n                  cantiereId={cantiereId}\n                  selectedCavo={collegamentiDialog.cavo}\n                  onSuccess={(message) => {\n                    if (message) {\n                      showNotification(message, 'success');\n                      // Chiudi il dialog immediatamente\n                      setCollegamentiDialog(prev => ({ ...prev, open: false }));\n                      // Ricarica i dati per aggiornare lo stato dei collegamenti\n                      setTimeout(() => fetchCavi(true), 300);\n                    }\n                    // Non chiudere il dialog se message è null (annullamento)\n                  }}\n                  onError={(message) => {\n                    showNotification(message, 'error');\n                  }}\n                  onClose={handleCloseCollegamenti}\n                />\n              )}\n            </DialogContent>\n          </Dialog>\n\n          {/* Dialog per la creazione di comande multiple */}\n          <CreaComandaMultipla\n            open={createCommandDialog.open}\n            onClose={handleCloseCreateCommand}\n            onSuccess={handleCreateCommandSuccess}\n            onError={handleCreateCommandError}\n            tipoComanda={createCommandDialog.tipoComanda}\n            caviSelezionati={createCommandDialog.caviSelezionati}\n            cantiereId={cantiereId}\n          />\n\n          {/* Componente CertificazioneCaviImproved per i dialog completi */}\n          <Box sx={{ display: 'none' }}>\n            <CertificazioneCaviImproved\n              ref={certificazioneRef}\n              cantiereId={cantiereId}\n              onSuccess={(message) => {\n                showNotification(message, 'success');\n                // Ricarica i dati per aggiornare lo stato di certificazione\n                setTimeout(() => fetchCavi(true), 500);\n              }}\n              onError={(message) => {\n                showNotification(message, 'error');\n              }}\n            />\n          </Box>\n\n          {/* Snackbar per le notifiche */}\n          <Snackbar\n            open={notification.open}\n            autoHideDuration={4000}\n            onClose={handleCloseNotification}\n            anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n          >\n            <Alert onClose={handleCloseNotification} severity={notification.severity} sx={{ width: '100%' }}>\n              {notification.message}\n            </Alert>\n          </Snackbar>\n        </Box>\n      )}\n    </Box>\n  );\n};\n\nexport default VisualizzaCaviPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,QAAQ,OAAO;AACvE,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,UAAU,EACVC,IAAI,EACJC,gBAAgB,EAChBC,cAAc,EACdC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,QAAQ,EACRC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,KAAK,QACA,eAAe;AACtB,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,SACEC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,eAAe,EAC9BC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,OAAO,IAAIC,WAAW,EACtBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,oBAAoB,IAAIC,wBAAwB,EAChDC,UAAU,IAAIC,cAAc,EAC5BC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,GAAG,IAAIC,OAAO,EACdC,SAAS,IAAIC,aAAa,EAC1BC,WAAW,IAAIC,QAAQ,EACvBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,KAAK,IAAIC,SAAS,QACb,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D;AACA,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,QAAQ,MAAM,gCAAgC;AACrD,SAASC,2BAA2B,QAAQ,6BAA6B;AACzE,OAAOC,mBAAmB,MAAM,2CAA2C;AAC3E,OAAOC,4BAA4B,MAAM,oDAAoD;AAC7F,OAAOC,4BAA4B,MAAM,oDAAoD;AAC7F,OAAOC,gBAAgB,MAAM,wCAAwC;AACrE,OAAOC,0BAA0B,MAAM,kDAAkD;AACzF,OAAOC,mBAAmB,MAAM,8CAA8C;AAC9E;;AAEA,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,YAAA,EAAAC,aAAA,EAAAC,aAAA;EAC/B,MAAM;IAAEC,eAAe;IAAEC;EAAK,CAAC,GAAGpB,OAAO,CAAC,CAAC;EAC3C,MAAM;IAAEqB,qBAAqB;IAAEC,wBAAwB;IAAEC,sBAAsB;IAAEC,yBAAyB;IAAEC,sBAAsB;IAAEC;EAA0B,CAAC,GAAGzB,gBAAgB,CAAC,CAAC;EACpL,MAAM0B,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC6B,UAAU,EAAEC,aAAa,CAAC,GAAG3F,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC4F,YAAY,EAAEC,eAAe,CAAC,GAAG7F,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC8F,UAAU,EAAEC,aAAa,CAAC,GAAG/F,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACgG,SAAS,EAAEC,YAAY,CAAC,GAAGjG,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACkG,OAAO,EAAEC,UAAU,CAAC,GAAGnG,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoG,KAAK,EAAEC,QAAQ,CAAC,GAAGrG,QAAQ,CAAC,IAAI,CAAC;EACxC;EACA,MAAM,CAACsG,YAAY,EAAEC,eAAe,CAAC,GAAGvG,QAAQ,CAAC;IAAEwG,IAAI,EAAE,KAAK;IAAEC,OAAO,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAU,CAAC,CAAC;EACnG;;EAEA;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG5G,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC6G,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9G,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAAC+G,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhH,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACiH,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGlH,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACmH,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpH,QAAQ,CAAC,EAAE,CAAC;;EAE9D;EACA,MAAM,CAACqH,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGtH,QAAQ,CAAC;IAC/DwG,IAAI,EAAE,KAAK;IACXe,IAAI,EAAE,IAAI;IACVrB,OAAO,EAAE;EACX,CAAC,CAAC;EAEF,MAAM,CAACsB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGzH,QAAQ,CAAC;IAC3DwG,IAAI,EAAE,KAAK;IACXe,IAAI,EAAE,IAAI;IACVrB,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACwB,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG3H,QAAQ,CAAC;IAC/DwG,IAAI,EAAE,KAAK;IACXe,IAAI,EAAE,IAAI;IACVrB,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,MAAM0B,iBAAiB,GAAGzH,MAAM,CAAC,IAAI,CAAC;;EAEtC;EACA,MAAM,CAAC0H,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG9H,QAAQ,CAAC;IAC7DwG,IAAI,EAAE,KAAK;IACXuB,WAAW,EAAE,EAAE;IACfC,eAAe,EAAE,EAAE;IACnB9B,OAAO,EAAE;EACX,CAAC,CAAC;;EAIF;EACA,MAAM,CAAC+B,UAAU,EAAEC,aAAa,CAAC,GAAGlI,QAAQ,CAAC;IAC3CmI,UAAU,EAAE,CAAC;IACbC,cAAc,EAAE,CAAC;IACjBC,gBAAgB,EAAE,CAAC;IACnBC,WAAW,EAAE,CAAC;IACdC,aAAa,EAAE,CAAC;IAChBC,gBAAgB,EAAE,CAAC;IACnBC,eAAe,EAAE,CAAC;IAClBC,kBAAkB,EAAE,CAAC;IACrBC,wBAAwB,EAAE,CAAC;IAC3BC,uBAAuB,EAAE,CAAC;IAC1BC,yBAAyB,EAAE,CAAC;IAC5BC,GAAG,EAAE,CAAC;IAAE;IACRC,WAAW,EAAE,CAAC;IACdC,eAAe,EAAE,CAAC;IAClBC,cAAc,EAAE;EAClB,CAAC,CAAC;;EAIF;EACA,MAAM,CAACC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGnJ,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAACoJ,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGrJ,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAACsJ,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGvJ,QAAQ,CAAC,EAAE,CAAC;;EAE9D;;EAEA;EACA,MAAMwJ,YAAY,GAAGA,CAACC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,KAAK;IAClD;IACA,MAAMC,EAAE,GAAG,GAAG,CAAC,CAAE;IACjB,MAAMC,EAAE,GAAG,GAAG,CAAC,CAAE;IACjB,MAAMC,EAAE,GAAG,GAAG,CAAC,CAAE;;IAEjB;IACA,IAAIN,IAAI,KAAK,CAAC,EAAE,OAAO,CAAC;;IAExB;IACA,MAAMO,oBAAoB,GAAG,CAACN,KAAK,GAAGC,KAAK,IAAIE,EAAE;IACjD,MAAMI,mBAAmB,GAAG,CAACN,KAAK,GAAGC,KAAK,KAAKC,EAAE,GAAGC,EAAE,CAAC;IACvD,MAAMI,iBAAiB,GAAGN,KAAK,IAAIC,EAAE,GAAGC,EAAE,GAAGC,EAAE,CAAC;IAChD,MAAMI,UAAU,GAAGH,oBAAoB,GAAGC,mBAAmB,GAAGC,iBAAiB;;IAEjF;IACA,MAAME,YAAY,GAAGX,IAAI,IAAII,EAAE,GAAGC,EAAE,GAAGC,EAAE,CAAC;;IAE1C;IACA,MAAMjB,GAAG,GAAIqB,UAAU,GAAGC,YAAY,GAAI,GAAG;IAE7CC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE;MAC1Bb,IAAI;MAAEC,KAAK;MAAEC,KAAK;MAAEC,KAAK;MACzBW,IAAI,EAAE;QAAEV,EAAE;QAAEC,EAAE;QAAEC;MAAG,CAAC;MACpBC,oBAAoB;MACpBC,mBAAmB;MACnBC,iBAAiB;MACjBC,UAAU;MACVC,YAAY;MACZtB,GAAG,EAAE0B,IAAI,CAACC,KAAK,CAAC3B,GAAG,GAAG,GAAG,CAAC,GAAG;IAC/B,CAAC,CAAC;IAEF,OAAO0B,IAAI,CAACC,KAAK,CAAC3B,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;EACtC,CAAC;;EAED;EACA,MAAM4B,mBAAmB,GAAGA,CAACC,cAAc,EAAEC,aAAa,KAAK;IAC7D,MAAMC,SAAS,GAAG,CAAC,IAAIF,cAAc,IAAI,EAAE,CAAC,EAAE,IAAIC,aAAa,IAAI,EAAE,CAAC,CAAC;IAEvE,IAAIC,SAAS,CAACC,MAAM,KAAK,CAAC,EAAE;MAC1BT,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;MACvE;IACF;IAEAD,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;MAC3CxE,UAAU,EAAE,CAAA6E,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEG,MAAM,KAAI,CAAC;MACvC9E,SAAS,EAAE,CAAA4E,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEE,MAAM,KAAI,CAAC;MACrCC,MAAM,EAAEF,SAAS,CAACC;IACpB,CAAC,CAAC;IAEF,MAAM3C,UAAU,GAAG0C,SAAS,CAACC,MAAM;;IAEnC;IACA,MAAM1C,cAAc,GAAGyC,SAAS,CAACG,MAAM,CAACzD,IAAI,IAC1CA,IAAI,CAAC0D,mBAAmB,KAAK,YAAY,IACzC1D,IAAI,CAAC0D,mBAAmB,KAAK,YAAY,IACzC1D,IAAI,CAAC0D,mBAAmB,KAAK,QAC/B,CAAC,CAACH,MAAM;IAER,MAAMzC,gBAAgB,GAAGwC,SAAS,CAACG,MAAM,CAACzD,IAAI,IAC5CA,IAAI,CAAC0D,mBAAmB,KAAK,eAAe,IAC5C1D,IAAI,CAAC0D,mBAAmB,KAAK,eAC/B,CAAC,CAACH,MAAM;IAER,MAAMxC,WAAW,GAAGuC,SAAS,CAACG,MAAM,CAACzD,IAAI,IACvCA,IAAI,CAAC0D,mBAAmB,KAAK,UAAU,IACvC1D,IAAI,CAAC0D,mBAAmB,KAAK,UAC/B,CAAC,CAACH,MAAM;;IAER;IACA,MAAMvC,aAAa,GAAGsC,SAAS,CAACG,MAAM,CAACzD,IAAI,IACzCA,IAAI,CAAC2D,YAAY,KAAK,CAAC,IACvB3D,IAAI,CAAC4D,qBAAqB,IAC1B5D,IAAI,CAAC6D,mBACP,CAAC,CAACN,MAAM;IAER,MAAMtC,gBAAgB,GAAGL,UAAU,GAAGI,aAAa;;IAEnD;IACA,MAAME,eAAe,GAAGoC,SAAS,CAACG,MAAM,CAACzD,IAAI,IAAIA,IAAI,CAAC8D,WAAW,KAAK,IAAI,IAAI9D,IAAI,CAAC8D,WAAW,KAAK,MAAM,CAAC,CAACP,MAAM;;IAEjH;IACA,MAAMhC,GAAG,GAAGU,YAAY,CAACrB,UAAU,EAAEC,cAAc,EAAEG,aAAa,EAAEE,eAAe,CAAC;;IAEpF;IACA,MAAME,wBAAwB,GAAGG,GAAG,CAAC,CAAC;IACtC,MAAMF,uBAAuB,GAAGT,UAAU,GAAG,CAAC,GAAGqC,IAAI,CAACC,KAAK,CAAElC,aAAa,GAAGJ,UAAU,GAAI,GAAG,CAAC,GAAG,CAAC;;IAEnG;IACA,MAAMY,WAAW,GAAG8B,SAAS,CAACS,MAAM,CAAC,CAACC,GAAG,EAAEhE,IAAI,KAAKgE,GAAG,IAAIC,UAAU,CAACjE,IAAI,CAACkE,aAAa,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IACnG,MAAMzC,eAAe,GAAG6B,SAAS,CAC9BG,MAAM,CAACzD,IAAI,IAAIA,IAAI,CAAC0D,mBAAmB,KAAK,YAAY,IAAI1D,IAAI,CAAC0D,mBAAmB,KAAK,YAAY,IAAI1D,IAAI,CAAC0D,mBAAmB,KAAK,QAAQ,CAAC,CAC/IK,MAAM,CAAC,CAACC,GAAG,EAAEhE,IAAI,KAAKgE,GAAG,IAAIC,UAAU,CAACjE,IAAI,CAACmE,eAAe,CAAC,IAAIF,UAAU,CAACjE,IAAI,CAACkE,aAAa,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5G,MAAMxC,cAAc,GAAGF,WAAW,GAAGC,eAAe;IACpD,MAAMN,kBAAkB,GAAGP,UAAU,GAAGM,eAAe;IACvD,MAAMI,yBAAyB,GAAGV,UAAU,GAAG,CAAC,GAAGqC,IAAI,CAACC,KAAK,CAAEhC,eAAe,GAAGN,UAAU,GAAI,GAAG,CAAC,GAAG,CAAC;IAEvG,MAAMwD,aAAa,GAAG;MACpBxD,UAAU;MACVC,cAAc;MACdC,gBAAgB;MAChBC,WAAW;MACXC,aAAa;MACbC,gBAAgB;MAChBC,eAAe;MACfC,kBAAkB;MAClBC,wBAAwB;MACxBC,uBAAuB;MACvBC,yBAAyB;MACzBC,GAAG;MAAE;MACLC,WAAW,EAAEyB,IAAI,CAACC,KAAK,CAAC1B,WAAW,CAAC;MACpCC,eAAe,EAAEwB,IAAI,CAACC,KAAK,CAACzB,eAAe,CAAC;MAC5CC,cAAc,EAAEuB,IAAI,CAACC,KAAK,CAACxB,cAAc;IAC3C,CAAC;IAEDoB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEqB,aAAa,CAAC;IAC1DzD,aAAa,CAACyD,aAAa,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;IACnC;IACAC,qBAAqB,CAAC,CAAC,YAAY,EAAE,eAAe,EAAE,UAAU,CAAC,CAAC;EACpE,CAAC;;EAED;EACA,MAAMC,aAAa,GAAG,MAAOC,eAAe,IAAK;IAC/C,IAAI;MACF1B,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEyB,eAAe,CAAC;;MAEnE;MACA,MAAMC,qBAAqB,GAAG,MAAMhI,WAAW,CAACiI,oBAAoB,CAACF,eAAe,CAAC;MACrF1B,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE0B,qBAAqB,CAAC;MACzDzC,oBAAoB,CAACyC,qBAAqB,CAACE,kBAAkB,CAAC;;MAE9D;MACA,MAAMC,aAAa,GAAG,MAAMnI,WAAW,CAACoI,uBAAuB,CAACL,eAAe,CAAC;MAChF1B,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE6B,aAAa,CAAC;MACpDhD,uBAAuB,CAACgD,aAAa,CAACE,SAAS,IAAI,EAAE,CAAC;;MAEtD;MACA;MACAhC,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;IACvE,CAAC,CAAC,OAAOlE,KAAK,EAAE;MACdiE,OAAO,CAACjE,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;IACjE;EACF,CAAC;;EAED;EACA,MAAMkG,qBAAqB,GAAIC,KAAK,IAAK;IACvC,MAAMC,cAAc,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK;;IAEzC;IACA;IACA;IACA,IAAIF,cAAc,KAAK,EAAE,IAAIA,cAAc,KAAK,UAAU,EAAE;MAC1DnD,uBAAuB,CAAC,EAAE,CAAC;MAC3BgB,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;IACzD,CAAC,MAAM;MACLjB,uBAAuB,CAACmD,cAAc,CAAC;MACvCnC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEkC,cAAc,CAAC;IAC/D;EACF,CAAC;;EAED;EACA,MAAM,CAACG,OAAO,EAAEC,UAAU,CAAC,GAAG5M,QAAQ,CAAC;IACrCiL,mBAAmB,EAAE,EAAE;IACvB4B,SAAS,EAAE,EAAE;IACbC,OAAO,EAAE,EAAE;IACXC,UAAU,EAAE;EACd,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,kBAAkB,EAAEnB,qBAAqB,CAAC,GAAG7L,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACiN,aAAa,EAAEC,gBAAgB,CAAC,GAAGlN,QAAQ,CAAC,EAAE,CAAC;;EAEtD;;EAEA;EACA;EACA,MAAMmN,SAAS,GAAG,MAAAA,CAAOC,aAAa,GAAG,KAAK,KAAK;IACjD,IAAI;MACF,IAAI,CAACA,aAAa,EAAE;QAClBjH,UAAU,CAAC,IAAI,CAAC;MAClB;MACAkE,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE5E,UAAU,CAAC;;MAEzD;MACA,IAAI,CAACA,UAAU,EAAE;QACf2E,OAAO,CAACjE,KAAK,CAAC,mCAAmC,EAAEV,UAAU,CAAC;QAC9DW,QAAQ,CAAC,wDAAwD,CAAC;QAClEF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA,IAAI4F,eAAe,GAAGrG,UAAU;MAChC,IAAI,CAACqG,eAAe,EAAE;QACpBA,eAAe,GAAGsB,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;QAC5DjD,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEyB,eAAe,CAAC;QACnE,IAAI,CAACA,eAAe,EAAE;UACpB1B,OAAO,CAACjE,KAAK,CAAC,2CAA2C,CAAC;UAC1DC,QAAQ,CAAC,8CAA8C,CAAC;UACxDF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;MACF;;MAEA;MACAkE,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;MACvD,IAAIiD,MAAM,GAAG,EAAE;MACf,IAAI;QACFA,MAAM,GAAG,MAAMvJ,WAAW,CAACwJ,OAAO,CAACzB,eAAe,EAAE,CAAC,EAAEY,OAAO,CAAC;QAC/DtC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEiD,MAAM,GAAGA,MAAM,CAACzC,MAAM,GAAG,CAAC,CAAC;MAClE,CAAC,CAAC,OAAO2C,WAAW,EAAE;QACpBpD,OAAO,CAACjE,KAAK,CAAC,yCAAyC,EAAEqH,WAAW,CAAC;QACrE;QACAF,MAAM,GAAG,EAAE;MACb;;MAEA;MACA,IAAIA,MAAM,IAAIA,MAAM,CAACzC,MAAM,GAAG,CAAC,EAAE;QAC/B,MAAM4C,kBAAkB,GAAGH,MAAM,CAACvC,MAAM,CAACzD,IAAI,IAAIA,IAAI,CAACoG,sBAAsB,KAAK,CAAC,CAAC;QACnF,IAAID,kBAAkB,CAAC5C,MAAM,GAAG,CAAC,EAAE;UACjCT,OAAO,CAACjE,KAAK,CAAC,wEAAwE,EAAEsH,kBAAkB,CAAC;QAC7G;MACF;MAEA3H,aAAa,CAACwH,MAAM,IAAI,EAAE,CAAC;;MAE3B;MACA,IAAIK,KAAK,GAAG,EAAE;MACd,IAAI;QACFvD,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;QAC9DsD,KAAK,GAAG,MAAM5J,WAAW,CAAC6J,YAAY,CAAC9B,eAAe,CAAC;QACvD1B,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEsD,KAAK,GAAGA,KAAK,CAAC9C,MAAM,GAAG,CAAC,CAAC;QACnF,IAAI8C,KAAK,IAAIA,KAAK,CAAC9C,MAAM,GAAG,CAAC,EAAE;UAC7BT,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEsD,KAAK,CAAC,CAAC,CAAC,CAAC;QAC5C;MACF,CAAC,CAAC,OAAOE,UAAU,EAAE;QACnBzD,OAAO,CAACjE,KAAK,CAAC,8DAA8D,EAAE0H,UAAU,CAAC;QACzF;QACA,IAAI;UACFzD,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;UAC/CsD,KAAK,GAAG,MAAM5J,WAAW,CAACwJ,OAAO,CAACzB,eAAe,EAAE,CAAC,CAAC;UACrD1B,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEsD,KAAK,GAAGA,KAAK,CAAC9C,MAAM,GAAG,CAAC,CAAC;QACnF,CAAC,CAAC,OAAOiD,aAAa,EAAE;UACtB1D,OAAO,CAACjE,KAAK,CAAC,mCAAmC,EAAE2H,aAAa,CAAC;UACjE;UACAH,KAAK,GAAG,EAAE;QACZ;MACF;MACA3H,YAAY,CAAC2H,KAAK,IAAI,EAAE,CAAC;;MAIzB;MACAvH,QAAQ,CAAC,EAAE,CAAC;IACd,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdiE,OAAO,CAACjE,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjEC,QAAQ,CAAC,oCAAoCD,KAAK,CAACK,OAAO,IAAI,oBAAoB,EAAE,CAAC;;MAErF;MACAuH,UAAU,CAAC,MAAM;QACf;QACA,IAAIC,QAAQ,CAACC,IAAI,CAACC,WAAW,CAACC,QAAQ,CAAC,iCAAiC,CAAC,EAAE;UACzE/D,OAAO,CAACC,GAAG,CAAC,gEAAgE,CAAC;UAC7E+D,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;QAC1B;MACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IACZ,CAAC,SAAS;MACR,IAAI,CAACnB,aAAa,EAAE;QAClBjH,UAAU,CAAC,KAAK,CAAC;MACnB;IACF;EACF,CAAC;;EAED;EACAlG,SAAS,CAAC,MAAM;IACd;IACA2L,sBAAsB,CAAC,CAAC;IAExB,MAAM4C,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACFnE,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;QAErD;QACA,MAAMmE,KAAK,GAAGpB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3CjD,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,CAAC,CAACmE,KAAK,CAAC;QACvC,IAAI,CAACA,KAAK,EAAE;UACVpI,QAAQ,CAAC,iDAAiD,CAAC;UAC3DF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,IAAIuI,kBAAkB,GAAGrB,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;QACnE,IAAIqB,oBAAoB,GAAGtB,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;QAEvEjD,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE;UAAEoE,kBAAkB;UAAEC;QAAqB,CAAC,CAAC;QACnGtE,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEpF,IAAI,CAAC;;QAEjC;QACAmF,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;QACrD,KAAK,IAAIsE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvB,YAAY,CAACvC,MAAM,EAAE8D,CAAC,EAAE,EAAE;UAC5C,MAAMC,GAAG,GAAGxB,YAAY,CAACwB,GAAG,CAACD,CAAC,CAAC;UAC/BvE,OAAO,CAACC,GAAG,CAAC,GAAGuE,GAAG,KAAKxB,YAAY,CAACC,OAAO,CAACuB,GAAG,CAAC,EAAE,CAAC;QACrD;;QAEA;QACA,IAAI,CAAA3J,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4J,IAAI,MAAK,eAAe,EAAE;UAClCzE,OAAO,CAACC,GAAG,CAAC,6EAA6E,CAAC;;UAE1F;UACA,IAAIpF,IAAI,CAAC6J,WAAW,EAAE;YACpB1E,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEpF,IAAI,CAAC6J,WAAW,CAAC;YACrEL,kBAAkB,GAAGxJ,IAAI,CAAC6J,WAAW,CAACC,QAAQ,CAAC,CAAC;YAChDL,oBAAoB,GAAGzJ,IAAI,CAAC+J,aAAa,IAAI,YAAY/J,IAAI,CAAC6J,WAAW,EAAE;;YAE3E;YACA1B,YAAY,CAAC6B,OAAO,CAAC,oBAAoB,EAAER,kBAAkB,CAAC;YAC9DrB,YAAY,CAAC6B,OAAO,CAAC,sBAAsB,EAAEP,oBAAoB,CAAC;YAClEtE,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEoE,kBAAkB,CAAC;UAC1E,CAAC,MAAM;YACL;YACA,IAAI;cACFrE,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC;cAClF,MAAMmE,KAAK,GAAGpB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;cAC3C,IAAImB,KAAK,EAAE;gBACT;gBACA,MAAMU,SAAS,GAAGV,KAAK,CAACW,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBACrC,MAAMC,MAAM,GAAGF,SAAS,CAACG,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;gBAC9D,MAAMC,WAAW,GAAGC,kBAAkB,CAACC,IAAI,CAACJ,MAAM,CAAC,CAACD,KAAK,CAAC,EAAE,CAAC,CAACM,GAAG,CAACC,CAAC,IAAI;kBACrE,OAAO,GAAG,GAAG,CAAC,IAAI,GAAGA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAACZ,QAAQ,CAAC,EAAE,CAAC,EAAEa,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC9D,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAEZ,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACV,WAAW,CAAC;gBACvClF,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEyF,OAAO,CAAC;gBAE9C,IAAIA,OAAO,CAAChB,WAAW,EAAE;kBACvB1E,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEyF,OAAO,CAAChB,WAAW,CAAC;kBACtEL,kBAAkB,GAAGqB,OAAO,CAAChB,WAAW,CAACC,QAAQ,CAAC,CAAC;kBACnD;kBACAL,oBAAoB,GAAG,YAAYoB,OAAO,CAAChB,WAAW,EAAE;;kBAExD;kBACA1B,YAAY,CAAC6B,OAAO,CAAC,oBAAoB,EAAER,kBAAkB,CAAC;kBAC9DrB,YAAY,CAAC6B,OAAO,CAAC,sBAAsB,EAAEP,oBAAoB,CAAC;kBAClEtE,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEoE,kBAAkB,CAAC;gBAC1E;cACF;YACF,CAAC,CAAC,OAAOwB,CAAC,EAAE;cACV7F,OAAO,CAACjE,KAAK,CAAC,6CAA6C,EAAE8J,CAAC,CAAC;YACjE;UACF;QACF;;QAEA;QACA,IAAI,CAACxB,kBAAkB,IAAIA,kBAAkB,KAAK,WAAW,IAAIA,kBAAkB,KAAK,MAAM,EAAE;UAC9FrE,OAAO,CAAC8F,IAAI,CAAC,6EAA6E,CAAC;UAC3F;UACAzB,kBAAkB,GAAG,GAAG,CAAC,CAAC;UAC1BC,oBAAoB,GAAG,gBAAgB;;UAEvC;UACAtB,YAAY,CAAC6B,OAAO,CAAC,oBAAoB,EAAER,kBAAkB,CAAC;UAC9DrB,YAAY,CAAC6B,OAAO,CAAC,sBAAsB,EAAEP,oBAAoB,CAAC;UAClEtE,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEoE,kBAAkB,CAAC;QACpF;;QAEA;QACA,IAAI,CAACA,kBAAkB,EAAE;UACvBrI,QAAQ,CAAC,8DAA8D,CAAC;UACxEF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,MAAMiK,aAAa,GAAGC,QAAQ,CAAC3B,kBAAkB,EAAE,EAAE,CAAC;QACtDrE,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE8F,aAAa,CAAC;QAC9D,IAAIE,KAAK,CAACF,aAAa,CAAC,EAAE;UACxB/J,QAAQ,CAAC,2BAA2BqI,kBAAkB,mCAAmC,CAAC;UAC1FvI,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACAR,aAAa,CAACyK,aAAa,CAAC;QAC5BvK,eAAe,CAAC8I,oBAAoB,IAAI,YAAYyB,aAAa,EAAE,CAAC;;QAEpE;QACA,MAAMtE,aAAa,CAACsE,aAAa,CAAC;;QAIlC;QACA/F,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE8F,aAAa,CAAC;QACnE,IAAI;UACF;UACA,MAAMG,cAAc,GAAG,IAAIC,OAAO,CAAC,CAACC,CAAC,EAAEC,MAAM,KAAK;YAChD1C,UAAU,CAAC,MAAM0C,MAAM,CAAC,IAAIC,KAAK,CAAC,gDAAgD,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;UAChG,CAAC,CAAC;;UAEF;UACAtG,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAEqC,OAAO,CAAC;UAC1E,MAAMiE,WAAW,GAAG5M,WAAW,CAACwJ,OAAO,CAAC4C,aAAa,EAAE,CAAC,EAAEzD,OAAO,CAAC;UAClE,MAAMY,MAAM,GAAG,MAAMiD,OAAO,CAACK,IAAI,CAAC,CAACD,WAAW,EAAEL,cAAc,CAAC,CAAC;UAEhElG,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEiD,MAAM,CAAC;UAC5ClD,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEiD,MAAM,GAAGA,MAAM,CAACzC,MAAM,GAAG,CAAC,CAAC;UACzE,IAAIyC,MAAM,IAAIA,MAAM,CAACzC,MAAM,GAAG,CAAC,EAAE;YAC/BT,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEiD,MAAM,CAAC,CAAC,CAAC,CAAC;UAC9C,CAAC,MAAM;YACLlD,OAAO,CAAC8F,IAAI,CAAC,4CAA4C,EAAEC,aAAa,CAAC;UAC3E;UACArK,aAAa,CAACwH,MAAM,IAAI,EAAE,CAAC;;UAE3B;UACA7C,mBAAmB,CAAC6C,MAAM,IAAI,EAAE,EAAEvH,SAAS,CAAC;QAC9C,CAAC,CAAC,OAAO8K,SAAS,EAAE;UAClBzG,OAAO,CAACjE,KAAK,CAAC,yCAAyC,EAAE0K,SAAS,CAAC;UACnEzG,OAAO,CAACjE,KAAK,CAAC,8BAA8B,EAAE;YAC5CK,OAAO,EAAEqK,SAAS,CAACrK,OAAO;YAC1BsK,MAAM,EAAED,SAAS,CAACC,MAAM;YACxBC,IAAI,EAAEF,SAAS,CAACE,IAAI;YACpBC,KAAK,EAAEH,SAAS,CAACG,KAAK;YACtBC,IAAI,EAAEJ,SAAS,CAACI,IAAI;YACpBC,IAAI,EAAEL,SAAS,CAACK,IAAI;YACpBC,QAAQ,EAAEN,SAAS,CAACM,QAAQ,GAAG;cAC7BL,MAAM,EAAED,SAAS,CAACM,QAAQ,CAACL,MAAM;cACjCM,UAAU,EAAEP,SAAS,CAACM,QAAQ,CAACC,UAAU;cACzCL,IAAI,EAAEF,SAAS,CAACM,QAAQ,CAACJ;YAC3B,CAAC,GAAG;UACN,CAAC,CAAC;;UAEF;UACAjL,aAAa,CAAC,EAAE,CAAC;UACjBsE,OAAO,CAAC8F,IAAI,CAAC,sDAAsD,CAAC;;UAEpE;UACA9J,QAAQ,CAAC,2CAA2CyK,SAAS,CAACrK,OAAO,+CAA+C,CAAC;QACvH;;QAEA;QACA4D,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE8F,aAAa,CAAC;QAClE,IAAI;UACF;UACA,MAAMG,cAAc,GAAG,IAAIC,OAAO,CAAC,CAACC,CAAC,EAAEC,MAAM,KAAK;YAChD1C,UAAU,CAAC,MAAM0C,MAAM,CAAC,IAAIC,KAAK,CAAC,+CAA+C,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;UAC/F,CAAC,CAAC;;UAEF;UACAtG,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;UACvD;UACA,MAAMgH,YAAY,GAAGtN,WAAW,CAACwJ,OAAO,CAAC4C,aAAa,EAAE,CAAC,CAAC;UAC1D,MAAMxC,KAAK,GAAG,MAAM4C,OAAO,CAACK,IAAI,CAAC,CAACS,YAAY,EAAEf,cAAc,CAAC,CAAC;UAEhElG,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEsD,KAAK,CAAC;UAC1CvD,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEsD,KAAK,GAAGA,KAAK,CAAC9C,MAAM,GAAG,CAAC,CAAC;UACtE,IAAI8C,KAAK,IAAIA,KAAK,CAAC9C,MAAM,GAAG,CAAC,EAAE;YAC7BT,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEsD,KAAK,CAAC,CAAC,CAAC,CAAC;UAC5C,CAAC,MAAM;YACLvD,OAAO,CAAC8F,IAAI,CAAC,2CAA2C,EAAEC,aAAa,CAAC;UAC1E;UACAnK,YAAY,CAAC2H,KAAK,IAAI,EAAE,CAAC;;UAEzB;UACAlD,mBAAmB,CAAC5E,UAAU,EAAE8H,KAAK,IAAI,EAAE,CAAC;QAC9C,CAAC,CAAC,OAAOE,UAAU,EAAE;UACnBzD,OAAO,CAACjE,KAAK,CAAC,wCAAwC,EAAE0H,UAAU,CAAC;UACnEzD,OAAO,CAACjE,KAAK,CAAC,6BAA6B,EAAE;YAC3CK,OAAO,EAAEqH,UAAU,CAACrH,OAAO;YAC3BsK,MAAM,EAAEjD,UAAU,CAACiD,MAAM;YACzBC,IAAI,EAAElD,UAAU,CAACkD,IAAI;YACrBC,KAAK,EAAEnD,UAAU,CAACmD,KAAK;YACvBC,IAAI,EAAEpD,UAAU,CAACoD,IAAI;YACrBC,IAAI,EAAErD,UAAU,CAACqD,IAAI;YACrBC,QAAQ,EAAEtD,UAAU,CAACsD,QAAQ,GAAG;cAC9BL,MAAM,EAAEjD,UAAU,CAACsD,QAAQ,CAACL,MAAM;cAClCM,UAAU,EAAEvD,UAAU,CAACsD,QAAQ,CAACC,UAAU;cAC1CL,IAAI,EAAElD,UAAU,CAACsD,QAAQ,CAACJ;YAC5B,CAAC,GAAG;UACN,CAAC,CAAC;;UAEF;UACA/K,YAAY,CAAC,EAAE,CAAC;;UAEhB;UACA,IAAI,CAACG,KAAK,EAAE;YACVC,QAAQ,CAAC,0CAA0CyH,UAAU,CAACrH,OAAO,+CAA+C,CAAC;UACvH;QACF;;QAEA;QACAN,UAAU,CAAC,KAAK,CAAC;MAEnB,CAAC,CAAC,OAAOoL,GAAG,EAAE;QAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,mBAAA;QACZxH,OAAO,CAACjE,KAAK,CAAC,kCAAkC,EAAEmL,GAAG,CAAC;QACtDlH,OAAO,CAACjE,KAAK,CAAC,2BAA2B,EAAE;UACzCK,OAAO,EAAE8K,GAAG,CAAC9K,OAAO;UACpBsK,MAAM,EAAEQ,GAAG,CAACR,MAAM,MAAAS,aAAA,GAAID,GAAG,CAACH,QAAQ,cAAAI,aAAA,uBAAZA,aAAA,CAAcT,MAAM;UAC1CC,IAAI,EAAEO,GAAG,CAACP,IAAI,MAAAS,cAAA,GAAIF,GAAG,CAACH,QAAQ,cAAAK,cAAA,uBAAZA,cAAA,CAAcT,IAAI;UACpCC,KAAK,EAAEM,GAAG,CAACN;QACb,CAAC,CAAC;;QAEF;QACA,IAAIa,YAAY,GAAG,oBAAoB;QAEvC,IAAIP,GAAG,CAAC9K,OAAO,IAAI8K,GAAG,CAAC9K,OAAO,CAAC2H,QAAQ,CAAC,wBAAwB,CAAC,EAAE;UACjE0D,YAAY,GAAGP,GAAG,CAAC9K,OAAO;QAC5B,CAAC,MAAM,IAAI8K,GAAG,CAACR,MAAM,KAAK,GAAG,IAAIQ,GAAG,CAACR,MAAM,KAAK,GAAG,IACzC,EAAAW,cAAA,GAAAH,GAAG,CAACH,QAAQ,cAAAM,cAAA,uBAAZA,cAAA,CAAcX,MAAM,MAAK,GAAG,IAAI,EAAAY,cAAA,GAAAJ,GAAG,CAACH,QAAQ,cAAAO,cAAA,uBAAZA,cAAA,CAAcZ,MAAM,MAAK,GAAG,EAAE;UACtEe,YAAY,GAAG,mEAAmE;QACpF,CAAC,MAAM,KAAAF,cAAA,GAAIL,GAAG,CAACH,QAAQ,cAAAQ,cAAA,gBAAAC,mBAAA,GAAZD,cAAA,CAAcZ,IAAI,cAAAa,mBAAA,eAAlBA,mBAAA,CAAoBE,MAAM,EAAE;UACrC;UACAD,YAAY,GAAG,eAAeP,GAAG,CAACH,QAAQ,CAACJ,IAAI,CAACe,MAAM,EAAE;QAC1D,CAAC,MAAM,IAAIR,GAAG,CAACL,IAAI,KAAK,aAAa,EAAE;UACrC;UACAY,YAAY,GAAG,yEAAyE;QAC1F,CAAC,MAAM,IAAIP,GAAG,CAAC9K,OAAO,EAAE;UACtBqL,YAAY,GAAGP,GAAG,CAAC9K,OAAO;QAC5B;QAEAJ,QAAQ,CAAC,gCAAgCyL,YAAY,sBAAsB,CAAC;;QAE5E;QACA/L,aAAa,CAAC,EAAE,CAAC;QACjBE,YAAY,CAAC,EAAE,CAAC;MAClB,CAAC,SAAS;QACRE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDqI,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAAC7B,OAAO,CAAC,CAAC,CAAC,CAAC;;EAEf;;EAEA;EACA,MAAMqF,iBAAiB,GAAIzK,IAAI,IAAK;IAClCX,eAAe,CAACW,IAAI,CAAC;IACrBT,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMmL,kBAAkB,GAAGA,CAAA,KAAM;IAC/BnL,oBAAoB,CAAC,KAAK,CAAC;IAC3BF,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMsL,uBAAuB,GAAGA,CAAA,KAAM;IACpC3L,eAAe,CAAC4L,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE3L,IAAI,EAAE;IAAM,CAAC,CAAC,CAAC;EACrD,CAAC;;EAED;EACA,MAAM4L,gBAAgB,GAAGA,CAAC3L,OAAO,EAAEC,QAAQ,GAAG,SAAS,KAAK;IAC1DH,eAAe,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO;MAAEC;IAAS,CAAC,CAAC;EACpD,CAAC;;EAED;EACA,MAAM2L,qBAAqB,GAAGA,CAAA,KAAM;IAClCrL,mBAAmB,CAAC,CAACD,gBAAgB,CAAC;IACtC;IACA,IAAIA,gBAAgB,EAAE;MACpBG,qBAAqB,CAAC,EAAE,CAAC;MACzBE,oBAAoB,CAAC,EAAE,CAAC;IAC1B;EACF,CAAC;EAED,MAAMkL,+BAA+B,GAAIC,WAAW,IAAK;IACvDrL,qBAAqB,CAACqL,WAAW,CAAC;EACpC,CAAC;EAED,MAAMC,8BAA8B,GAAID,WAAW,IAAK;IACtDnL,oBAAoB,CAACmL,WAAW,CAAC;EACnC,CAAC;;EAED;EACA,MAAME,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMC,kBAAkB,GAAG5M,UAAU,CAACkF,MAAM,CAACzD,IAAI,IAAIN,kBAAkB,CAACmH,QAAQ,CAAC7G,IAAI,CAACoL,OAAO,CAAC,CAAC;IAC/F,MAAMC,iBAAiB,GAAG5M,SAAS,CAACgF,MAAM,CAACzD,IAAI,IAAIJ,iBAAiB,CAACiH,QAAQ,CAAC7G,IAAI,CAACoL,OAAO,CAAC,CAAC;IAC5F,OAAO,CAAC,GAAGD,kBAAkB,EAAE,GAAGE,iBAAiB,CAAC;EACtD,CAAC;;EAED;EACA,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAClC,OAAO5L,kBAAkB,CAAC6D,MAAM,GAAG3D,iBAAiB,CAAC2D,MAAM;EAC7D,CAAC;;EAED;EACA,MAAMgI,uBAAuB,GAAGA,CAACvL,IAAI,EAAEwL,MAAM,KAAK;IAChD1I,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEyI,MAAM,EAAE,WAAW,EAAExL,IAAI,CAAC;IAElE,QAAQwL,MAAM;MACZ,KAAK,cAAc;QACjBf,iBAAiB,CAACzK,IAAI,CAAC;QACvB;MACF,KAAK,MAAM;QACT;QACA6K,gBAAgB,CAAC,2CAA2C,EAAE,MAAM,CAAC;QACrE;MACF,KAAK,QAAQ;QACX;QACAA,gBAAgB,CAAC,+CAA+C,EAAE,MAAM,CAAC;QACzE;MACF,KAAK,QAAQ;QACX,IAAItM,UAAU,CAACkN,IAAI,CAACrD,CAAC,IAAIA,CAAC,CAACgD,OAAO,KAAKpL,IAAI,CAACoL,OAAO,CAAC,EAAE;UACpD;UACA,MAAMM,UAAU,GAAGhM,kBAAkB,CAACmH,QAAQ,CAAC7G,IAAI,CAACoL,OAAO,CAAC;UAC5D,IAAIM,UAAU,EAAE;YACd/L,qBAAqB,CAACiL,IAAI,IAAIA,IAAI,CAACnH,MAAM,CAACkI,EAAE,IAAIA,EAAE,KAAK3L,IAAI,CAACoL,OAAO,CAAC,CAAC;UACvE,CAAC,MAAM;YACLzL,qBAAqB,CAACiL,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE5K,IAAI,CAACoL,OAAO,CAAC,CAAC;UACxD;QACF,CAAC,MAAM;UACL;UACA,MAAMM,UAAU,GAAG9L,iBAAiB,CAACiH,QAAQ,CAAC7G,IAAI,CAACoL,OAAO,CAAC;UAC3D,IAAIM,UAAU,EAAE;YACd7L,oBAAoB,CAAC+K,IAAI,IAAIA,IAAI,CAACnH,MAAM,CAACkI,EAAE,IAAIA,EAAE,KAAK3L,IAAI,CAACoL,OAAO,CAAC,CAAC;UACtE,CAAC,MAAM;YACLvL,oBAAoB,CAAC+K,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE5K,IAAI,CAACoL,OAAO,CAAC,CAAC;UACvD;QACF;QACA;QACA,IAAI,CAAC5L,gBAAgB,EAAE;UACrBC,mBAAmB,CAAC,IAAI,CAAC;QAC3B;QACA;MACF,KAAK,SAAS;QACZ,MAAMmM,kBAAkB,GAAGN,qBAAqB,CAAC,CAAC;QAClD,IAAIM,kBAAkB,GAAG,CAAC,EAAE;UAC1B;UACA,MAAMC,eAAe,GAAGX,kBAAkB,CAAC,CAAC;UAC5C,MAAMY,MAAM,GAAGD,eAAe,CAAC1D,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACgD,OAAO,CAAC,CAAC7C,IAAI,CAAC,IAAI,CAAC;UAC7DwD,SAAS,CAACC,SAAS,CAACC,SAAS,CAACH,MAAM,CAAC;UACrCjB,gBAAgB,CAAC,GAAGe,kBAAkB,iCAAiC,EAAE,SAAS,CAAC;QACrF,CAAC,MAAM;UACLG,SAAS,CAACC,SAAS,CAACC,SAAS,CAACjM,IAAI,CAACoL,OAAO,CAAC;UAC3CP,gBAAgB,CAAC,WAAW7K,IAAI,CAACoL,OAAO,wBAAwB,EAAE,SAAS,CAAC;QAC9E;QACA;MACF,KAAK,cAAc;QACjB,MAAMc,aAAa,GAAGZ,qBAAqB,CAAC,CAAC;QAC7C,IAAIY,aAAa,GAAG,CAAC,EAAE;UACrB;UACA,MAAML,eAAe,GAAGX,kBAAkB,CAAC,CAAC;UAC5C,MAAMiB,UAAU,GAAGN,eAAe,CAAC1D,GAAG,CAACC,CAAC,IACtC,OAAOA,CAAC,CAACgD,OAAO,gBAAgBhD,CAAC,CAAC9C,SAAS,cAAc8C,CAAC,CAACgE,OAAO,YAAYhE,CAAC,CAAClE,aAAa,EAC/F,CAAC,CAACqE,IAAI,CAAC,MAAM,CAAC;UACdwD,SAAS,CAACC,SAAS,CAACC,SAAS,CAACE,UAAU,CAAC;UACzCtB,gBAAgB,CAAC,eAAeqB,aAAa,6BAA6B,EAAE,SAAS,CAAC;QACxF,CAAC,MAAM;UACL,MAAMG,OAAO,GAAG,OAAOrM,IAAI,CAACoL,OAAO,gBAAgBpL,IAAI,CAACsF,SAAS,cAActF,IAAI,CAACoM,OAAO,YAAYpM,IAAI,CAACkE,aAAa,EAAE;UAC3H6H,SAAS,CAACC,SAAS,CAACC,SAAS,CAACI,OAAO,CAAC;UACtCxB,gBAAgB,CAAC,qCAAqC,EAAE,SAAS,CAAC;QACpE;QACA;MACF,KAAK,SAAS;QACZ;QACAA,gBAAgB,CAAC,2CAA2C,EAAE,MAAM,CAAC;QACrE;MACF;MACA,KAAK,qBAAqB;QACxByB,2BAA2B,CAAC,MAAM,CAAC;QACnC;MACF,KAAK,sCAAsC;QACzCA,2BAA2B,CAAC,uBAAuB,CAAC;QACpD;MACF,KAAK,oCAAoC;QACvCA,2BAA2B,CAAC,qBAAqB,CAAC;QAClD;MACF,KAAK,+BAA+B;QAClCA,2BAA2B,CAAC,gBAAgB,CAAC;QAC7C;MACF;QACExJ,OAAO,CAAC8F,IAAI,CAAC,0BAA0B,EAAE4C,MAAM,CAAC;IACpD;EACF,CAAC;;EAED;EACA,MAAMe,wBAAwB,GAAI/L,WAAW,IAAK;IAChD;IACA,OAAO;MAAEgM,KAAK,EAAE,IAAI;MAAEC,MAAM,EAAE,CAAC;MAAEC,MAAM,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAE,CAAC;EAC3D,CAAC;;EAED;EACA,MAAML,2BAA2B,GAAI9L,WAAW,IAAK;IACnD,MAAMqL,eAAe,GAAGX,kBAAkB,CAAC,CAAC;IAE5C,IAAIW,eAAe,CAACtI,MAAM,KAAK,CAAC,EAAE;MAChCsH,gBAAgB,CAAC,wDAAwD,EAAE,SAAS,CAAC;MACrF;IACF;IAEA/H,OAAO,CAACC,GAAG,CAAC,qBAAqBvC,WAAW,QAAQqL,eAAe,CAACtI,MAAM,QAAQ,EAAEsI,eAAe,CAAC1D,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACgD,OAAO,CAAC,CAAC;;IAExH;IACA7K,sBAAsB,CAAC;MACrBtB,IAAI,EAAE,IAAI;MACVuB,WAAW,EAAEA,WAAW;MACxBC,eAAe,EAAEoL,eAAe;MAChClN,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMiO,wBAAwB,GAAGA,CAAA,KAAM;IACrCrM,sBAAsB,CAAC;MACrBtB,IAAI,EAAE,KAAK;MACXuB,WAAW,EAAE,EAAE;MACfC,eAAe,EAAE,EAAE;MACnB9B,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMkO,0BAA0B,GAAIhD,QAAQ,IAAK;IAC/CgB,gBAAgB,CAAC,WAAWhB,QAAQ,CAACiD,cAAc,uBAAuB,EAAE,SAAS,CAAC;;IAEtF;IACAnN,qBAAqB,CAAC,EAAE,CAAC;IACzBE,oBAAoB,CAAC,EAAE,CAAC;;IAExB;IACA+M,wBAAwB,CAAC,CAAC;;IAE1B;IACAnG,UAAU,CAAC,MAAMb,SAAS,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC;EACxC,CAAC;;EAED;EACA,MAAMmH,wBAAwB,GAAIlO,KAAK,IAAK;IAC1CiE,OAAO,CAACjE,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;IAC7DgM,gBAAgB,CAAC,sCAAsC,EAAE,OAAO,CAAC;EACnE,CAAC;;EAED;EACA,MAAMmC,mBAAmB,GAAIhN,IAAI,IAAK;IACpC,MAAM0L,UAAU,GAAGnN,UAAU,CAACkN,IAAI,CAACrD,CAAC,IAAIA,CAAC,CAACgD,OAAO,MAAKpL,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoL,OAAO,EAAC,GAChE1L,kBAAkB,CAACmH,QAAQ,CAAC7G,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoL,OAAO,CAAC,GAC1CxL,iBAAiB,CAACiH,QAAQ,CAAC7G,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoL,OAAO,CAAC;IAE7C,MAAMQ,kBAAkB,GAAGN,qBAAqB,CAAC,CAAC;IAClD,MAAM2B,oBAAoB,GAAGrB,kBAAkB,GAAG,CAAC;;IAEnD;IACA,MAAMsB,aAAa,GAAG,CACpB;MACEC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAEH,oBAAoB,GAAG,GAAGrB,kBAAkB,mBAAmB,GAAG,QAAQ,CAAA5L,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoL,OAAO,KAAI,EAAE;IACtG,CAAC;IACD;IACA,IAAI6B,oBAAoB,GAAG,CACzB;MACEE,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,6BAA6BxB,kBAAkB;IACxD,CAAC,EACD;MACED,EAAE,EAAE,qBAAqB;MACzByB,KAAK,EAAE,cAAc;MACrBC,IAAI,eAAEjQ,OAAA,CAACf,SAAS;QAACiR,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACpClC,MAAM,EAAE,qBAAqB;MAC7BmC,OAAO,EAAEpC,uBAAuB;MAChCqC,KAAK,EAAE,CAAC,MAAM;QACZ,MAAMC,UAAU,GAAGtB,wBAAwB,CAAC,MAAM,CAAC;QACnD,OAAOsB,UAAU,CAACnB,MAAM,GAAG,CAAC,GAAG,OAAO,GAAGmB,UAAU,CAAClB,QAAQ,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS;MAC1F,CAAC,EAAE,CAAC;MACJmB,WAAW,EAAE,CAAC,MAAM;QAClB,MAAMD,UAAU,GAAGtB,wBAAwB,CAAC,MAAM,CAAC;QACnD,IAAIwB,IAAI,GAAG,yBAAyBnC,kBAAkB,OAAO;QAC7D,IAAIiC,UAAU,CAACpB,MAAM,GAAG,CAAC,EAAE;UACzBsB,IAAI,IAAI,KAAKF,UAAU,CAACnB,MAAM,YAAYmB,UAAU,CAAClB,QAAQ,UAAU;QACzE;QACA,OAAOoB,IAAI;MACb,CAAC,EAAE;IACL,CAAC,EACD;MACEpC,EAAE,EAAE,sCAAsC;MAC1CyB,KAAK,EAAE,+BAA+B;MACtCC,IAAI,eAAEjQ,OAAA,CAACzC,QAAQ;QAAC2S,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnClC,MAAM,EAAE,sCAAsC;MAC9CmC,OAAO,EAAEpC,uBAAuB;MAChCqC,KAAK,EAAE,CAAC,MAAM;QACZ,MAAMC,UAAU,GAAGtB,wBAAwB,CAAC,uBAAuB,CAAC;QACpE,OAAOsB,UAAU,CAACnB,MAAM,GAAG,CAAC,GAAG,OAAO,GAAGmB,UAAU,CAAClB,QAAQ,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS;MAC1F,CAAC,EAAE,CAAC;MACJmB,WAAW,EAAE,CAAC,MAAM;QAClB,MAAMD,UAAU,GAAGtB,wBAAwB,CAAC,uBAAuB,CAAC;QACpE,IAAIwB,IAAI,GAAG,0CAA0CnC,kBAAkB,OAAO;QAC9E,IAAIiC,UAAU,CAACpB,MAAM,GAAG,CAAC,EAAE;UACzBsB,IAAI,IAAI,KAAKF,UAAU,CAACnB,MAAM,YAAYmB,UAAU,CAAClB,QAAQ,UAAU;QACzE;QACA,OAAOoB,IAAI;MACb,CAAC,EAAE;IACL,CAAC,EACD;MACEpC,EAAE,EAAE,oCAAoC;MACxCyB,KAAK,EAAE,6BAA6B;MACpCC,IAAI,eAAEjQ,OAAA,CAACzC,QAAQ;QAAC2S,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnClC,MAAM,EAAE,oCAAoC;MAC5CmC,OAAO,EAAEpC,uBAAuB;MAChCqC,KAAK,EAAE,CAAC,MAAM;QACZ,MAAMC,UAAU,GAAGtB,wBAAwB,CAAC,qBAAqB,CAAC;QAClE,OAAOsB,UAAU,CAACnB,MAAM,GAAG,CAAC,GAAG,OAAO,GAAGmB,UAAU,CAAClB,QAAQ,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS;MAC1F,CAAC,EAAE,CAAC;MACJmB,WAAW,EAAE,CAAC,MAAM;QAClB,MAAMD,UAAU,GAAGtB,wBAAwB,CAAC,qBAAqB,CAAC;QAClE,IAAIwB,IAAI,GAAG,wCAAwCnC,kBAAkB,OAAO;QAC5E,IAAIiC,UAAU,CAACpB,MAAM,GAAG,CAAC,EAAE;UACzBsB,IAAI,IAAI,KAAKF,UAAU,CAACnB,MAAM,YAAYmB,UAAU,CAAClB,QAAQ,UAAU;QACzE;QACA,OAAOoB,IAAI;MACb,CAAC,EAAE;IACL,CAAC,EACD;MACEpC,EAAE,EAAE,+BAA+B;MACnCyB,KAAK,EAAE,wBAAwB;MAC/BC,IAAI,eAAEjQ,OAAA,CAACjB,YAAY;QAACmR,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACvClC,MAAM,EAAE,+BAA+B;MACvCmC,OAAO,EAAEpC,uBAAuB;MAChCqC,KAAK,EAAE,CAAC,MAAM;QACZ,MAAMC,UAAU,GAAGtB,wBAAwB,CAAC,gBAAgB,CAAC;QAC7D,OAAOsB,UAAU,CAACnB,MAAM,GAAG,CAAC,GAAG,OAAO,GAAGmB,UAAU,CAAClB,QAAQ,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS;MAC1F,CAAC,EAAE,CAAC;MACJmB,WAAW,EAAE,CAAC,MAAM;QAClB,MAAMD,UAAU,GAAGtB,wBAAwB,CAAC,gBAAgB,CAAC;QAC7D,IAAIwB,IAAI,GAAG,mCAAmCnC,kBAAkB,OAAO;QACvE,IAAIiC,UAAU,CAACpB,MAAM,GAAG,CAAC,EAAE;UACzBsB,IAAI,IAAI,KAAKF,UAAU,CAACnB,MAAM,YAAYmB,UAAU,CAAClB,QAAQ,UAAU;QACzE;QACA,OAAOoB,IAAI;MACb,CAAC,EAAE;IACL,CAAC,EACD;MACEZ,IAAI,EAAE;IACR,CAAC,CACF,GAAG,EAAE,CAAC;IACP;IACA,IAAI,CAACF,oBAAoB,GAAG,CAC1B;MACEtB,EAAE,EAAE,cAAc;MAClByB,KAAK,EAAE,qBAAqB;MAC5BC,IAAI,eAAEjQ,OAAA,CAAC/B,cAAc;QAACiS,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACzClC,MAAM,EAAE,cAAc;MACtBmC,OAAO,EAAEpC;IACX,CAAC,EACD;MACE4B,IAAI,EAAE;IACR,CAAC,EACD;MACExB,EAAE,EAAE,MAAM;MACVyB,KAAK,EAAE,UAAU;MACjBC,IAAI,eAAEjQ,OAAA,CAAC7B,QAAQ;QAAC+R,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnClC,MAAM,EAAE,MAAM;MACdmC,OAAO,EAAEpC,uBAAuB;MAChCqC,KAAK,EAAE;IACT,CAAC,EACD;MACEjC,EAAE,EAAE,QAAQ;MACZyB,KAAK,EAAE,SAAS;MAChBC,IAAI,eAAEjQ,OAAA,CAAC3B,UAAU;QAAC6R,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACrClC,MAAM,EAAE,QAAQ;MAChBmC,OAAO,EAAEpC,uBAAuB;MAChCqC,KAAK,EAAE;IACT,CAAC,EACD;MACET,IAAI,EAAE;IACR,CAAC,EACD;MACExB,EAAE,EAAE,SAAS;MACbyB,KAAK,EAAE,qBAAqB;MAC5BC,IAAI,eAAEjQ,OAAA,CAACzB,OAAO;QAAC2R,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAClClC,MAAM,EAAE,SAAS;MACjBmC,OAAO,EAAEpC,uBAAuB;MAChCqC,KAAK,EAAE;IACT,CAAC,EACD;MACET,IAAI,EAAE;IACR,CAAC,CACF,GAAG,EAAE,CAAC;IACP;IACA;MACExB,EAAE,EAAE,QAAQ;MACZyB,KAAK,EAAE1B,UAAU,GAAG,aAAa,GAAG,WAAW;MAC/C2B,IAAI,eAAEjQ,OAAA,CAACvB,aAAa;QAACyR,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACxClC,MAAM,EAAE,QAAQ;MAChBmC,OAAO,EAAEpC,uBAAuB;MAChCqC,KAAK,EAAElC,UAAU,GAAG,SAAS,GAAG;IAClC,CAAC;IACD;IACA;MACEyB,IAAI,EAAE;IACR,CAAC,EACD;MACExB,EAAE,EAAE,SAAS;MACbyB,KAAK,EAAEH,oBAAoB,GAAG,uBAAuB,GAAG,UAAU;MAClEI,IAAI,eAAEjQ,OAAA,CAACrB,QAAQ;QAACuR,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnClC,MAAM,EAAE,SAAS;MACjBmC,OAAO,EAAEpC,uBAAuB;MAChCyC,QAAQ,EAAE;IACZ,CAAC,EACD;MACErC,EAAE,EAAE,cAAc;MAClByB,KAAK,EAAEH,oBAAoB,GAAG,4BAA4B,GAAG,gBAAgB;MAC7EI,IAAI,eAAEjQ,OAAA,CAACrB,QAAQ;QAACuR,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnClC,MAAM,EAAE,cAAc;MACtBmC,OAAO,EAAEpC,uBAAuB;MAChCuC,WAAW,EAAEb,oBAAoB,GAAG,4CAA4C,GAAG;IACrF,CAAC,CACF;IAED,OAAOC,aAAa;EACtB,CAAC;;EAED;EACA,MAAMe,kBAAkB,GAAG,MAAAA,CAAOjO,IAAI,EAAEkO,UAAU,EAAEC,WAAW,KAAK;IAClErL,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAChDD,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEmL,UAAU,EAAE,WAAW,EAAElO,IAAI,CAAC;IACpE8C,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEoL,WAAW,CAAC;IAEzC,IAAID,UAAU,KAAK,eAAe,EAAE;MAClC;MACAnO,uBAAuB,CAAC;QACtBd,IAAI,EAAE,IAAI;QACVe,IAAI,EAAEA,IAAI;QACVrB,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIuP,UAAU,KAAK,aAAa,EAAE;MACvC;MACApL,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE/C,IAAI,CAACoL,OAAO,CAAC;MACtEhL,uBAAuB,CAAC;QACtBnB,IAAI,EAAE,IAAI;QACVe,IAAI,EAAEA,IAAI;QACVrB,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIuP,UAAU,KAAK,eAAe,IAAIA,UAAU,KAAK,iBAAiB,IAClEA,UAAU,KAAK,mBAAmB,IAAIA,UAAU,KAAK,kBAAkB,IACvEA,UAAU,KAAK,oBAAoB,EAAE;MAE9C;MACA,IAAIlO,IAAI,CAAC0D,mBAAmB,KAAK,YAAY,EAAE;QAC7CZ,OAAO,CAACC,GAAG,CAAC,uDAAuD,EAAE/C,IAAI,CAACoL,OAAO,CAAC;QAClFP,gBAAgB,CAAC,0DAA0D,EAAE,MAAM,CAAC;QACpF;MACF;;MAEA;MACA/H,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAE/C,IAAI,CAACoL,OAAO,EAAE,SAAS,EAAE8C,UAAU,CAAC;;MAEpG;MACAzH,UAAU,CAAC,MAAM;QACfvG,qBAAqB,CAAC;UACpBjB,IAAI,EAAE,IAAI;UACVe,IAAI,EAAEA,IAAI;UACVrB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,CAAC,EAAE,EAAE,CAAC;IACR,CAAC,MAAM,IAAIuP,UAAU,KAAK,oBAAoB,EAAE;MAC9C;MACApL,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAE/C,IAAI,CAACoL,OAAO,CAAC;;MAE/E;MACA,IAAI/K,iBAAiB,CAAC+N,OAAO,EAAE;QAC7B/N,iBAAiB,CAAC+N,OAAO,CAACC,0BAA0B,CAACrO,IAAI,CAAC;MAC5D;IACF,CAAC,MAAM,IAAIkO,UAAU,KAAK,kBAAkB,EAAE;MAC5C;MACApL,OAAO,CAACC,GAAG,CAAC,0DAA0D,EAAE/C,IAAI,CAACoL,OAAO,CAAC;;MAErF;MACA,IAAI/K,iBAAiB,CAAC+N,OAAO,EAAE;QAC7B/N,iBAAiB,CAAC+N,OAAO,CAACE,wBAAwB,CAACtO,IAAI,CAAC;MAC1D;IACF,CAAC,MAAM,IAAIkO,UAAU,KAAK,cAAc,EAAE;MACxC;MACApL,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE/C,IAAI,CAACoL,OAAO,CAAC;;MAElE;MACA,IAAI/K,iBAAiB,CAAC+N,OAAO,EAAE;QAC7B/N,iBAAiB,CAAC+N,OAAO,CAACG,kBAAkB,CAACvO,IAAI,CAAC;MACpD;IACF;EACF,CAAC;;EAKD;EACA,MAAMwO,2BAA2B,GAAItP,OAAO,IAAK;IAC/C2L,gBAAgB,CAAC3L,OAAO,EAAE,SAAS,CAAC;IACpC;IACAuH,UAAU,CAAC,MAAMb,SAAS,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC;EACxC,CAAC;EAED,MAAM6I,yBAAyB,GAAIvP,OAAO,IAAK;IAC7C2L,gBAAgB,CAAC3L,OAAO,EAAE,OAAO,CAAC;EACpC,CAAC;;EAED;EACA,MAAMwP,yBAAyB,GAAGA,CAAA,KAAM;IACtC,IAAI,CAAC5O,oBAAoB,CAACnB,OAAO,EAAE;MACjCoB,uBAAuB,CAAC;QAAEd,IAAI,EAAE,KAAK;QAAEe,IAAI,EAAE,IAAI;QAAErB,OAAO,EAAE;MAAM,CAAC,CAAC;IACtE;EACF,CAAC;EAED,MAAMgQ,yBAAyB,GAAGA,CAAA,KAAM;IACtC,IAAI,CAACxO,oBAAoB,CAACxB,OAAO,EAAE;MACjCyB,uBAAuB,CAAC;QAAEnB,IAAI,EAAE,KAAK;QAAEe,IAAI,EAAE,IAAI;QAAErB,OAAO,EAAE;MAAM,CAAC,CAAC;IACtE;EACF,CAAC;EAED,MAAMiQ,uBAAuB,GAAGjW,WAAW,CAAC,MAAM;IAChD,IAAI,CAACsH,kBAAkB,CAACtB,OAAO,EAAE;MAC/BuB,qBAAqB,CAAC0K,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE3L,IAAI,EAAE;MAAM,CAAC,CAAC,CAAC;IAC3D;EACF,CAAC,EAAE,CAACgB,kBAAkB,CAACtB,OAAO,CAAC,CAAC;;EAMhC;;EAEA;EACA,MAAMkQ,eAAe,GAAGA,CAAA,kBACtBzR,OAAA,CAACrE,KAAK;IAAC+V,EAAE,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,EAAE,EAAE,CAAC;MAAEC,OAAO,EAAE;IAAU,CAAE;IAAAC,QAAA,eAC7C9R,OAAA,CAAClD,KAAK;MAACiV,SAAS,EAAC,KAAK;MAACC,OAAO,EAAE,CAAE;MAACC,UAAU,EAAC,QAAQ;MAACC,cAAc,EAAC,eAAe;MAACC,QAAQ,EAAC,MAAM;MAAAL,QAAA,gBAEnG9R,OAAA,CAAClD,KAAK;QAACiV,SAAS,EAAC,KAAK;QAACE,UAAU,EAAC,QAAQ;QAACD,OAAO,EAAE,CAAE;QAAAF,QAAA,gBACpD9R,OAAA,CAAC/C,SAAS;UAACuT,KAAK,EAAC,SAAS;UAACN,QAAQ,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9CtQ,OAAA,CAACvE,GAAG;UAAAqW,QAAA,gBACF9R,OAAA,CAACtE,UAAU;YAAC0W,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAACX,EAAE,EAAE;cAAEY,UAAU,EAAE;YAAE,CAAE;YAAAR,QAAA,EAC9DxO,UAAU,CAACE;UAAU;YAAA2M,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACbtQ,OAAA,CAACtE,UAAU;YAAC0W,OAAO,EAAC,SAAS;YAAC5B,KAAK,EAAC,gBAAgB;YAAAsB,QAAA,EAAC;UAErD;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAERtQ,OAAA,CAAClD,KAAK;QAACiV,SAAS,EAAC,KAAK;QAACE,UAAU,EAAC,QAAQ;QAACD,OAAO,EAAE,CAAE;QAAAF,QAAA,gBACpD9R,OAAA,CAAC7C,eAAe;UAACqT,KAAK,EAAC,SAAS;UAACN,QAAQ,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpDtQ,OAAA,CAACvE,GAAG;UAAAqW,QAAA,gBACF9R,OAAA,CAACtE,UAAU;YAAC0W,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAACX,EAAE,EAAE;cAAEY,UAAU,EAAE;YAAE,CAAE;YAAAR,QAAA,EAC9DxO,UAAU,CAACG;UAAc;YAAA0M,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eACbtQ,OAAA,CAACtE,UAAU;YAAC0W,OAAO,EAAC,SAAS;YAAC5B,KAAK,EAAC,gBAAgB;YAAAsB,QAAA,EAAC;UAErD;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAERtQ,OAAA,CAAClD,KAAK;QAACiV,SAAS,EAAC,KAAK;QAACE,UAAU,EAAC,QAAQ;QAACD,OAAO,EAAE,CAAE;QAAAF,QAAA,gBACpD9R,OAAA,CAACzC,QAAQ;UAACiT,KAAK,EAAC,MAAM;UAACN,QAAQ,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1CtQ,OAAA,CAACvE,GAAG;UAAAqW,QAAA,gBACF9R,OAAA,CAACtE,UAAU;YAAC0W,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAACX,EAAE,EAAE;cAAEY,UAAU,EAAE;YAAE,CAAE;YAAAR,QAAA,EAC9DxO,UAAU,CAACM;UAAa;YAAAuM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eACbtQ,OAAA,CAACtE,UAAU;YAAC0W,OAAO,EAAC,SAAS;YAAC5B,KAAK,EAAC,gBAAgB;YAAAsB,QAAA,EAAC;UAErD;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAERtQ,OAAA,CAAClD,KAAK;QAACiV,SAAS,EAAC,KAAK;QAACE,UAAU,EAAC,QAAQ;QAACD,OAAO,EAAE,CAAE;QAAAF,QAAA,gBACpD9R,OAAA,CAACjB,YAAY;UAACyR,KAAK,EAAC,SAAS;UAACN,QAAQ,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjDtQ,OAAA,CAACvE,GAAG;UAAAqW,QAAA,gBACF9R,OAAA,CAACtE,UAAU;YAAC0W,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAACX,EAAE,EAAE;cAAEY,UAAU,EAAE;YAAE,CAAE;YAAAR,QAAA,EAC9DxO,UAAU,CAACQ;UAAe;YAAAqM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACbtQ,OAAA,CAACtE,UAAU;YAAC0W,OAAO,EAAC,SAAS;YAAC5B,KAAK,EAAC,gBAAgB;YAAAsB,QAAA,EAAC;UAErD;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAERtQ,OAAA,CAAClD,KAAK;QAACiV,SAAS,EAAC,KAAK;QAACE,UAAU,EAAC,QAAQ;QAACD,OAAO,EAAE,CAAE;QAAAF,QAAA,gBACpD9R,OAAA,CAACvE,GAAG;UAACiW,EAAE,EAAE;YACPa,KAAK,EAAE,EAAE;YACTC,MAAM,EAAE,EAAE;YACVC,YAAY,EAAE,KAAK;YACnBZ,OAAO,EAAEvO,UAAU,CAACa,GAAG,IAAI,EAAE,GAAG,cAAc,GACrCb,UAAU,CAACa,GAAG,IAAI,EAAE,GAAG,cAAc,GAAG,YAAY;YAC7DuO,OAAO,EAAE,MAAM;YACfT,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAJ,QAAA,eACA9R,OAAA,CAACtE,UAAU;YAAC0W,OAAO,EAAC,SAAS;YAACC,UAAU,EAAC,MAAM;YAAC7B,KAAK,EAAC,OAAO;YAAAsB,QAAA,GAC1DxO,UAAU,CAACa,GAAG,EAAC,GAClB;UAAA;YAAAgM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNtQ,OAAA,CAACvE,GAAG;UAAAqW,QAAA,gBACF9R,OAAA,CAACtE,UAAU;YAAC0W,OAAO,EAAC,OAAO;YAACC,UAAU,EAAC,QAAQ;YAACX,EAAE,EAAE;cAAEY,UAAU,EAAE;YAAE,CAAE;YAAAR,QAAA,EAAC;UAEvE;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbtQ,OAAA,CAACtE,UAAU;YAAC0W,OAAO,EAAC,SAAS;YAAC5B,KAAK,EAAC,gBAAgB;YAAAsB,QAAA,GACjDxO,UAAU,CAACe,eAAe,EAAC,cAC9B;UAAA;YAAA8L,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACR;;EAED;;EAEA;;EAEA;EACA,MAAMqC,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAI,CAAC3Q,YAAY,EAAE,OAAO,IAAI;IAE9B,oBACEhC,OAAA,CAAC3D,MAAM;MAACwF,IAAI,EAAEK,iBAAkB;MAAC0Q,OAAO,EAAEtF,kBAAmB;MAACuF,QAAQ,EAAC,IAAI;MAACC,SAAS;MAAAhB,QAAA,gBACnF9R,OAAA,CAAC1D,WAAW;QAAAwV,QAAA,GAAC,iBACI,EAAC9P,YAAY,CAACgM,OAAO;MAAA;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eACdtQ,OAAA,CAACzD,aAAa;QAACwW,QAAQ;QAAAjB,QAAA,eACrB9R,OAAA,CAACnE,IAAI;UAACmX,SAAS;UAAChB,OAAO,EAAE,CAAE;UAAAF,QAAA,gBACzB9R,OAAA,CAACnE,IAAI;YAACoX,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAArB,QAAA,gBACvB9R,OAAA,CAACtE,UAAU;cAAC0W,OAAO,EAAC,WAAW;cAACgB,YAAY;cAAAtB,QAAA,EAAC;YAAqB;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/EtQ,OAAA,CAACvE,GAAG;cAACiW,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAE,QAAA,gBACjB9R,OAAA,CAACtE,UAAU;gBAAC0W,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAC9R,OAAA;kBAAA8R,QAAA,EAAQ;gBAAQ;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACtO,YAAY,CAACqR,OAAO,IAAI,KAAK;cAAA;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAClGtQ,OAAA,CAACtE,UAAU;gBAAC0W,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAC9R,OAAA;kBAAA8R,QAAA,EAAQ;gBAAQ;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACtO,YAAY,CAACsR,OAAO,IAAI,KAAK;cAAA;gBAAAnD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAClGtQ,OAAA,CAACtE,UAAU;gBAAC0W,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAC9R,OAAA;kBAAA8R,QAAA,EAAQ;gBAAU;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACtO,YAAY,CAACkG,SAAS,IAAI,KAAK;cAAA;gBAAAiI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACtGtQ,OAAA,CAACtE,UAAU;gBAAC0W,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAC9R,OAAA;kBAAA8R,QAAA,EAAQ;gBAAO;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACtO,YAAY,CAACuR,WAAW,IAAI,KAAK;cAAA;gBAAApD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAErGtQ,OAAA,CAACtE,UAAU;gBAAC0W,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAC9R,OAAA;kBAAA8R,QAAA,EAAQ;gBAAW;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACtO,YAAY,CAACgN,OAAO,IAAI,KAAK;cAAA;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAElG,CAAC,eAENtQ,OAAA,CAACtE,UAAU;cAAC0W,OAAO,EAAC,WAAW;cAACgB,YAAY;cAAAtB,QAAA,EAAC;YAAQ;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAClEtQ,OAAA,CAACvE,GAAG;cAACiW,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAE,QAAA,gBACjB9R,OAAA,CAACtE,UAAU;gBAAC0W,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAC9R,OAAA;kBAAA8R,QAAA,EAAQ;gBAAW;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACtO,YAAY,CAACwR,mBAAmB,IAAI,KAAK;cAAA;gBAAArD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACjHtQ,OAAA,CAACtE,UAAU;gBAAC0W,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAC9R,OAAA;kBAAA8R,QAAA,EAAQ;gBAAO;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACtO,YAAY,CAACyR,eAAe,IAAI,KAAK;cAAA;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACzGtQ,OAAA,CAACtE,UAAU;gBAAC0W,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAC9R,OAAA;kBAAA8R,QAAA,EAAQ;gBAAY;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACtO,YAAY,CAAC0R,2BAA2B,IAAI,KAAK;cAAA;gBAAAvD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC1HtQ,OAAA,CAACtE,UAAU;gBAAC0W,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAC9R,OAAA;kBAAA8R,QAAA,EAAQ;gBAAa;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACtO,YAAY,CAACwE,qBAAqB,IAAI,KAAK;cAAA;gBAAA2J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACrHtQ,OAAA,CAACtE,UAAU;gBAAC0W,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAC9R,OAAA;kBAAA8R,QAAA,EAAQ;gBAAQ;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACtO,YAAY,CAAC2R,gBAAgB,IAAI,KAAK;cAAA;gBAAAxD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEPtQ,OAAA,CAACnE,IAAI;YAACoX,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAArB,QAAA,gBACvB9R,OAAA,CAACtE,UAAU;cAAC0W,OAAO,EAAC,WAAW;cAACgB,YAAY;cAAAtB,QAAA,EAAC;YAAM;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChEtQ,OAAA,CAACvE,GAAG;cAACiW,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAE,QAAA,gBACjB9R,OAAA,CAACtE,UAAU;gBAAC0W,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAC9R,OAAA;kBAAA8R,QAAA,EAAQ;gBAAW;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACtO,YAAY,CAAC4R,iBAAiB,IAAI,KAAK;cAAA;gBAAAzD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC/GtQ,OAAA,CAACtE,UAAU;gBAAC0W,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAC9R,OAAA;kBAAA8R,QAAA,EAAQ;gBAAO;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACtO,YAAY,CAAC6R,aAAa,IAAI,KAAK;cAAA;gBAAA1D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACvGtQ,OAAA,CAACtE,UAAU;gBAAC0W,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAC9R,OAAA;kBAAA8R,QAAA,EAAQ;gBAAY;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACtO,YAAY,CAAC8R,yBAAyB,IAAI,KAAK;cAAA;gBAAA3D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACxHtQ,OAAA,CAACtE,UAAU;gBAAC0W,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAC9R,OAAA;kBAAA8R,QAAA,EAAQ;gBAAa;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACtO,YAAY,CAACyE,mBAAmB,IAAI,KAAK;cAAA;gBAAA0J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACnHtQ,OAAA,CAACtE,UAAU;gBAAC0W,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAC9R,OAAA;kBAAA8R,QAAA,EAAQ;gBAAQ;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACtO,YAAY,CAAC+R,cAAc,IAAI,KAAK;cAAA;gBAAA5D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtG,CAAC,eAENtQ,OAAA,CAACtE,UAAU;cAAC0W,OAAO,EAAC,WAAW;cAACgB,YAAY;cAAAtB,QAAA,EAAC;YAAa;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACvEtQ,OAAA,CAACvE,GAAG;cAACiW,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAE,QAAA,gBACjB9R,OAAA,CAACtE,UAAU;gBAAC0W,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAC9R,OAAA;kBAAA8R,QAAA,EAAQ;gBAAc;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACtO,YAAY,CAAC8E,aAAa,IAAI,KAAK;cAAA;gBAAAqJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC9GtQ,OAAA,CAACtE,UAAU;gBAAC0W,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAC9R,OAAA;kBAAA8R,QAAA,EAAQ;gBAAgB;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACtO,YAAY,CAAC+E,eAAe,IAAI,GAAG;cAAA;gBAAAoJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAChHtQ,OAAA,CAACtE,UAAU;gBAAC0W,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAC9R,OAAA;kBAAA8R,QAAA,EAAQ;gBAAM;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC9Q,2BAA2B,CAACwC,YAAY,CAACsE,mBAAmB,CAAC;cAAA;gBAAA6J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAChItQ,OAAA,CAACtE,UAAU;gBAAC0W,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAC9R,OAAA;kBAAA8R,QAAA,EAAQ;gBAAa;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACtO,YAAY,CAACuE,YAAY,IAAI,GAAG;cAAA;gBAAA4J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC1GtQ,OAAA,CAACtE,UAAU;gBAAC0W,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAC9R,OAAA;kBAAA8R,QAAA,EAAQ;gBAAO;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACtO,YAAY,CAACgS,SAAS,IAAI,KAAK;cAAA;gBAAA7D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACnGtQ,OAAA,CAACtE,UAAU;gBAAC0W,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAC9R,OAAA;kBAAA8R,QAAA,EAAQ;gBAAkB;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACtO,YAAY,CAACiS,iBAAiB,IAAI,KAAK;cAAA;gBAAA9D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACtHtQ,OAAA,CAACtE,UAAU;gBAAC0W,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAC9R,OAAA;kBAAA8R,QAAA,EAAQ;gBAAa;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACtO,YAAY,CAACkS,YAAY,IAAI,KAAK;cAAA;gBAAA/D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC5GtQ,OAAA,CAACtE,UAAU;gBAAC0W,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAC9R,OAAA;kBAAA8R,QAAA,EAAQ;gBAAqB;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC,IAAI6D,IAAI,CAACnS,YAAY,CAACoS,SAAS,CAAC,CAACC,cAAc,CAAC,CAAC;cAAA;gBAAAlE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChBtQ,OAAA,CAACxD,aAAa;QAAAsV,QAAA,eACZ9R,OAAA,CAACpE,MAAM;UAAC2U,OAAO,EAAEjD,kBAAmB;UAAAwE,QAAA,EAAC;QAAM;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEb,CAAC;;EAED;;EAIA,oBACEtQ,OAAA,CAACvE,GAAG;IAAC6Y,SAAS,EAAC,WAAW;IAAAxC,QAAA,EACvBvQ,OAAO,gBACNvB,OAAA,CAACvE,GAAG;MAACiW,EAAE,EAAE;QAAEgB,OAAO,EAAE,MAAM;QAAE6B,aAAa,EAAE,QAAQ;QAAEtC,UAAU,EAAE,QAAQ;QAAEuC,EAAE,EAAE;MAAE,CAAE;MAAA1C,QAAA,gBACjF9R,OAAA,CAAC7D,gBAAgB;QAACsY,IAAI,EAAE;MAAG;QAAAtE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9BtQ,OAAA,CAACtE,UAAU;QAACgW,EAAE,EAAE;UAAE8C,EAAE,EAAE;QAAE,CAAE;QAAA1C,QAAA,EAAC;MAAmB;QAAA3B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC3DtQ,OAAA,CAACpE,MAAM;QACLwW,OAAO,EAAC,UAAU;QAClB5B,KAAK,EAAC,SAAS;QACfD,OAAO,EAAEA,CAAA,KAAM7G,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;QACxC8H,EAAE,EAAE;UAAE8C,EAAE,EAAE;QAAE,CAAE;QAAA1C,QAAA,EACf;MAED;QAAA3B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,GACJ7O,KAAK,gBACPzB,OAAA,CAACvE,GAAG;MAAAqW,QAAA,gBACF9R,OAAA,CAAChE,KAAK;QAAC+F,QAAQ,EAAC,OAAO;QAAC2P,EAAE,EAAE;UAAEE,EAAE,EAAE;QAAE,CAAE;QAAAE,QAAA,GACnCrQ,KAAK,EACLA,KAAK,CAACgI,QAAQ,CAAC,eAAe,CAAC,iBAC9BzJ,OAAA,CAACtE,UAAU;UAAC0W,OAAO,EAAC,OAAO;UAACV,EAAE,EAAE;YAAE8C,EAAE,EAAE;UAAE,CAAE;UAAA1C,QAAA,gBACxC9R,OAAA;YAAA8R,QAAA,EAAQ;UAAa;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,uEAC9B,eAAAtQ,OAAA;YAAAmQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,8CACoC,eAAAtQ,OAAA;YAAA8R,QAAA,EAAM;UAAa;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,4CACtE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eACRtQ,OAAA,CAACvE,GAAG;QAACiW,EAAE,EAAE;UAAEgB,OAAO,EAAE,MAAM;UAAEgC,GAAG,EAAE;QAAE,CAAE;QAAA5C,QAAA,eACnC9R,OAAA,CAACpE,MAAM;UACLwW,OAAO,EAAC,WAAW;UACnBkC,SAAS,EAAC,gBAAgB;UAC1B/D,OAAO,EAAEA,CAAA,KAAM7G,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UAAAkI,QAAA,EACzC;QAED;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,gBAENtQ,OAAA,CAACvE,GAAG;MAAAqW,QAAA,GAEDvN,oBAAoB,CAAC4B,MAAM,GAAG,CAAC,iBAC9BnG,OAAA,CAACrE,KAAK;QAAC+V,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAE,QAAA,eACzB9R,OAAA,CAACvE,GAAG;UAACiW,EAAE,EAAE;YAAEgB,OAAO,EAAE,MAAM;YAAET,UAAU,EAAE,QAAQ;YAAEyC,GAAG,EAAE;UAAE,CAAE;UAAA5C,QAAA,gBACzD9R,OAAA,CAACtE,UAAU;YAAC0W,OAAO,EAAC,IAAI;YAAAN,QAAA,EAAC;UAAgB;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACtDtQ,OAAA,CAACtD,WAAW;YAAC+X,IAAI,EAAC,OAAO;YAAC/C,EAAE,EAAE;cAAEiD,QAAQ,EAAE;YAAI,CAAE;YAAA7C,QAAA,gBAC9C9R,OAAA,CAACrD,UAAU;cAAAmV,QAAA,EAAC;YAAyB;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAClDtQ,OAAA,CAACpD,MAAM;cACLmL,KAAK,EAAEtD,oBAAoB,IAAI,UAAW;cAC1CmQ,QAAQ,EAAEjN,qBAAsB;cAChCqI,KAAK,EAAC,2BAA2B;cAAA8B,QAAA,gBAEjC9R,OAAA,CAACnD,QAAQ;gBAACkL,KAAK,EAAC,UAAU;gBAAA+J,QAAA,GAAC,kCACH,EAACnN,iBAAiB,IAAI,IAAIA,iBAAiB,GAAG;cAAA;gBAAAwL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC,EACV/L,oBAAoB,CAACwG,GAAG,CAAE8J,GAAG,iBAC5B7U,OAAA,CAACnD,QAAQ;gBAAqBkL,KAAK,EAAE8M,GAAG,CAACC,SAAU;gBAAAhD,QAAA,GAAC,eAC/C,EAAC+C,GAAG,CAACC,SAAS,EAAC,IAAE,EAACD,GAAG,CAACE,UAAU,EAAC,QACpC,EAACF,GAAG,CAACC,SAAS,KAAKnQ,iBAAiB,IAAI,YAAY;cAAA,GAFvCkQ,GAAG,CAACC,SAAS;gBAAA3E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGlB,CACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACdtQ,OAAA,CAAC9D,IAAI;YACH8T,KAAK,EACHvL,oBAAoB,GAChB,YAAYA,oBAAoB,EAAE,GAClC,aAAaE,iBAAiB,IAAI,KAAK,EAC5C;YACD6L,KAAK,EAAE/L,oBAAoB,GAAG,WAAW,GAAG,SAAU;YACtD2N,OAAO,EAAC;UAAU;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,EAGAmB,eAAe,CAAC,CAAC,eAGlBzR,OAAA,CAACvE,GAAG;QAACiW,EAAE,EAAE;UAAE8C,EAAE,EAAE;QAAE,CAAE;QAAA1C,QAAA,GAEhB1P,gBAAgB,iBACfpC,OAAA,CAAChE,KAAK;UACJ+F,QAAQ,EAAC,MAAM;UACf2P,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UACdxD,MAAM,eACJpO,OAAA,CAACpE,MAAM;YACL4U,KAAK,EAAC,SAAS;YACfiE,IAAI,EAAC,OAAO;YACZlE,OAAO,EAAEA,CAAA,KAAMlO,mBAAmB,CAAC,KAAK,CAAE;YAAAyP,QAAA,EAC3C;UAED;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;UAAAwB,QAAA,eAED9R,OAAA,CAACtE,UAAU;YAAC0W,OAAO,EAAC,OAAO;YAAAN,QAAA,gBACzB9R,OAAA;cAAA8R,QAAA,EAAQ;YAAyB;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,oDAC/B,eAAAtQ,OAAA;cAAA8R,QAAA,EAAQ;YAAY;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,iCACxC,EAACpC,qBAAqB,CAAC,CAAC,GAAG,CAAC,iBAC1BlO,OAAA;cAAA8R,QAAA,GAAM,GAAC,eAAA9R,OAAA;gBAAA8R,QAAA,GAAS5D,qBAAqB,CAAC,CAAC,EAAC,mBAAiB;cAAA;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACzE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CACR,EAGAlO,gBAAgB,IAAI8L,qBAAqB,CAAC,CAAC,GAAG,CAAC,iBAC9ClO,OAAA,CAACvE,GAAG;UAACiW,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAAE,QAAA,eACjB9R,OAAA,CAAC9D,IAAI;YACH8T,KAAK,EAAE,GAAG9B,qBAAqB,CAAC,CAAC,mBAAoB;YACrDsC,KAAK,EAAC,SAAS;YACf4B,OAAO,EAAC,QAAQ;YAChBqC,IAAI,EAAC;UAAO;YAAAtE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EAGA0E,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,IAAI/T,UAAU,CAACgF,MAAM,GAAG,CAAC,iBAC9DnG,OAAA,CAACvE,GAAG;UAACiW,EAAE,EAAE;YAAEE,EAAE,EAAE,CAAC;YAAED,CAAC,EAAE,CAAC;YAAEE,OAAO,EAAE,SAAS;YAAEY,YAAY,EAAE,CAAC;YAAEvC,QAAQ,EAAE,QAAQ;YAAEiF,UAAU,EAAE,WAAW;YAAEzC,OAAO,EAAE;UAAO,CAAE;UAAAZ,QAAA,EACzHsD,MAAM,CAACC,IAAI,CAAClU,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC4J,GAAG,CAACb,GAAG,iBACjClK,OAAA;YAAA8R,QAAA,GAAgB5H,GAAG,EAAC,IAAE,EAACmB,IAAI,CAACiK,SAAS,CAACnU,UAAU,CAAC,CAAC,CAAC,CAAC+I,GAAG,CAAC,CAAC;UAAA,GAA/CA,GAAG;YAAAiG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAkD,CAChE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eAEDtQ,OAAA,CAACP,mBAAmB;UAClB8V,IAAI,EAAEpU,UAAW;UACjBI,OAAO,EAAEA,OAAQ;UACjBiU,oBAAoB,EAAGC,YAAY,IAAK/P,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE8P,YAAY,CAACtP,MAAM,CAAE;UAClGxB,iBAAiB,EAAE,EAAAxE,YAAA,GAAAgB,UAAU,CAAC,CAAC,CAAC,cAAAhB,YAAA,uBAAbA,YAAA,CAAeuV,mBAAmB,OAAAtV,aAAA,GAAIe,UAAU,CAAC,CAAC,CAAC,cAAAf,aAAA,uBAAbA,aAAA,CAAe0U,SAAS,OAAAzU,aAAA,GAAIc,UAAU,CAAC,CAAC,CAAC,cAAAd,aAAA,uBAAbA,aAAA,CAAewU,GAAG,CAAC;UACxGzS,gBAAgB,EAAEA,gBAAiB;UACnCuT,YAAY,EAAErT,kBAAmB;UACjCsT,iBAAiB,EAAEjI,+BAAgC;UACnDkI,iBAAiB,EAAEnI,qBAAsB;UACzCoI,gBAAgB,EAAElG,mBAAoB;UACtCmG,mBAAmB,EAAE5H,uBAAwB;UAC7C6H,cAAc,EAAEnF;QAAmB;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,EACDnP,UAAU,CAACgF,MAAM,KAAK,CAAC,IAAI,CAAC5E,OAAO,iBAClCvB,OAAA,CAAChE,KAAK;UAAC+F,QAAQ,EAAC,MAAM;UAAC2P,EAAE,EAAE;YAAE8C,EAAE,EAAE;UAAE,CAAE;UAAA1C,QAAA,EAAC;QAEtC;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNtQ,OAAA,CAACvE,GAAG;QAACiW,EAAE,EAAE;UAAE8C,EAAE,EAAE;QAAE,CAAE;QAAA1C,QAAA,gBACjB9R,OAAA,CAACvE,GAAG;UAACiW,EAAE,EAAE;YAAEgB,OAAO,EAAE,MAAM;YAAER,cAAc,EAAE,eAAe;YAAED,UAAU,EAAE,QAAQ;YAAEL,EAAE,EAAE;UAAE,CAAE;UAAAE,QAAA,eACzF9R,OAAA,CAACtE,UAAU;YAAC0W,OAAO,EAAC,IAAI;YAAAN,QAAA,GAAC,aACZ,EAACzQ,SAAS,CAAC8E,MAAM,GAAG,CAAC,GAAG,IAAI9E,SAAS,CAAC8E,MAAM,GAAG,GAAG,EAAE;UAAA;YAAAgK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNtQ,OAAA,CAACP,mBAAmB;UAClB8V,IAAI,EAAElU,SAAU;UAChBE,OAAO,EAAEA,OAAQ;UACjBiU,oBAAoB,EAAGC,YAAY,IAAK/P,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE8P,YAAY,CAACtP,MAAM,CAAE;UACjG/D,gBAAgB,EAAEA,gBAAiB;UACnCuT,YAAY,EAAEnT,iBAAkB;UAChCoT,iBAAiB,EAAE/H,8BAA+B;UAClDgI,iBAAiB,EAAEnI,qBAAsB;UACzCoI,gBAAgB,EAAElG,mBAAoB;UACtCmG,mBAAmB,EAAE5H,uBAAwB;UAC7C6H,cAAc,EAAEnF;QAAmB;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,EACDjP,SAAS,CAAC8E,MAAM,KAAK,CAAC,IAAI,CAAC5E,OAAO,iBACjCvB,OAAA,CAAChE,KAAK;UAAC+F,QAAQ,EAAC,MAAM;UAAC2P,EAAE,EAAE;YAAE8C,EAAE,EAAE;UAAE,CAAE;UAAA1C,QAAA,EAAC;QAEtC;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAKLqC,mBAAmB,CAAC,CAAC,eAStB3S,OAAA,CAACN,4BAA4B;QAC3BmC,IAAI,EAAEa,oBAAoB,CAACb,IAAK;QAChC+Q,OAAO,EAAEtB,yBAA0B;QACnC1O,IAAI,EAAEF,oBAAoB,CAACE,IAAK;QAChC7B,UAAU,EAAEA,UAAW;QACvBkV,SAAS,EAAGnU,OAAO,IAAK;UACtB2L,gBAAgB,CAAC3L,OAAO,EAAE,SAAS,CAAC;UACpC;UACAuH,UAAU,CAAC,MAAMb,SAAS,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC;QACxC,CAAE;QACF0N,OAAO,EAAGpU,OAAO,IAAK;UACpB2L,gBAAgB,CAAC3L,OAAO,EAAE,OAAO,CAAC;QACpC,CAAE;QACFP,OAAO,EAAEmB,oBAAoB,CAACnB;MAAQ;QAAA4O,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC,eAEFtQ,OAAA,CAACL,4BAA4B;QAC3BkC,IAAI,EAAEkB,oBAAoB,CAAClB,IAAK;QAChC+Q,OAAO,EAAErB,yBAA0B;QACnC3O,IAAI,EAAEG,oBAAoB,CAACH,IAAK;QAChC7B,UAAU,EAAEA,UAAW;QACvBkV,SAAS,EAAE7E,2BAA4B;QACvC8E,OAAO,EAAE7E;MAA0B;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,eAGFtQ,OAAA,CAAC3D,MAAM;QACLwF,IAAI,EAAEgB,kBAAkB,CAAChB,IAAK;QAC9B+Q,OAAO,EAAEpB,uBAAwB;QACjCqB,QAAQ,EAAC,IAAI;QACbC,SAAS;QACTqD,oBAAoB,EAAE,KAAM;QAC5BC,WAAW,EAAE,KAAM;QACnBC,aAAa,EAAE,KAAM;QACrBC,iBAAiB,EAAE,IAAK;QACxBC,YAAY,EAAE,KAAM;QACpBC,gBAAgB,EAAE,IAAK;QACvBC,mBAAmB,EAAE,IAAK;QAC1BC,mBAAmB,EAAE,IAAK;QAC1BC,kBAAkB,EAAE,CAAE;QACtBC,eAAe,EAAE;UACfC,OAAO,EAAE,CAAC;UACVC,MAAM,EAAE,KAAK;UACbC,KAAK,EAAE,KAAK;UACZC,IAAI,EAAE;QACR,CAAE;QACFC,UAAU,EAAE;UACVC,KAAK,EAAE;YACLC,UAAU,EAAE,MAAM;YAClBC,SAAS,EAAE;UACb;QACF,CAAE;QAAAtF,QAAA,eAEF9R,OAAA,CAACzD,aAAa;UAAAuV,QAAA,EACXjP,kBAAkB,CAACD,IAAI,iBACtB5C,OAAA,CAACJ,gBAAgB;YACfmB,UAAU,EAAEA,UAAW;YACvBiB,YAAY,EAAEa,kBAAkB,CAACD,IAAK;YACtCqT,SAAS,EAAGnU,OAAO,IAAK;cACtB,IAAIA,OAAO,EAAE;gBACX2L,gBAAgB,CAAC3L,OAAO,EAAE,SAAS,CAAC;gBACpC;gBACAgB,qBAAqB,CAAC0K,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAE3L,IAAI,EAAE;gBAAM,CAAC,CAAC,CAAC;gBACzD;gBACAwH,UAAU,CAAC,MAAMb,SAAS,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC;cACxC;cACA;YACF,CAAE;YACF0N,OAAO,EAAGpU,OAAO,IAAK;cACpB2L,gBAAgB,CAAC3L,OAAO,EAAE,OAAO,CAAC;YACpC,CAAE;YACF8Q,OAAO,EAAEpB;UAAwB;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGTtQ,OAAA,CAACF,mBAAmB;QAClB+B,IAAI,EAAEqB,mBAAmB,CAACrB,IAAK;QAC/B+Q,OAAO,EAAEpD,wBAAyB;QAClCyG,SAAS,EAAExG,0BAA2B;QACtCyG,OAAO,EAAEvG,wBAAyB;QAClCvM,WAAW,EAAEF,mBAAmB,CAACE,WAAY;QAC7CC,eAAe,EAAEH,mBAAmB,CAACG,eAAgB;QACrDtC,UAAU,EAAEA;MAAW;QAAAoP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC,eAGFtQ,OAAA,CAACvE,GAAG;QAACiW,EAAE,EAAE;UAAEgB,OAAO,EAAE;QAAO,CAAE;QAAAZ,QAAA,eAC3B9R,OAAA,CAACH,0BAA0B;UACzBwX,GAAG,EAAEpU,iBAAkB;UACvBlC,UAAU,EAAEA,UAAW;UACvBkV,SAAS,EAAGnU,OAAO,IAAK;YACtB2L,gBAAgB,CAAC3L,OAAO,EAAE,SAAS,CAAC;YACpC;YACAuH,UAAU,CAAC,MAAMb,SAAS,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC;UACxC,CAAE;UACF0N,OAAO,EAAGpU,OAAO,IAAK;YACpB2L,gBAAgB,CAAC3L,OAAO,EAAE,OAAO,CAAC;UACpC;QAAE;UAAAqO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNtQ,OAAA,CAACvD,QAAQ;QACPoF,IAAI,EAAEF,YAAY,CAACE,IAAK;QACxByV,gBAAgB,EAAE,IAAK;QACvB1E,OAAO,EAAErF,uBAAwB;QACjCgK,YAAY,EAAE;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAA3F,QAAA,eAE3D9R,OAAA,CAAChE,KAAK;UAAC4W,OAAO,EAAErF,uBAAwB;UAACxL,QAAQ,EAAEJ,YAAY,CAACI,QAAS;UAAC2P,EAAE,EAAE;YAAEa,KAAK,EAAE;UAAO,CAAE;UAAAT,QAAA,EAC7FnQ,YAAY,CAACG;QAAO;UAAAqO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EACN;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACpQ,EAAA,CA3hDID,kBAAkB;EAAA,QACYd,OAAO,EACyHC,gBAAgB,EACjKF,WAAW;AAAA;AAAAwY,EAAA,GAHxBzX,kBAAkB;AA6hDxB,eAAeA,kBAAkB;AAAC,IAAAyX,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}