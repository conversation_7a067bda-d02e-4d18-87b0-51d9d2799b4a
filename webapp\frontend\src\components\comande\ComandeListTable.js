import React, { useState } from 'react';
import {
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Tooltip,
  Typography,
  TextField,
  InputAdornment,
  Stack
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Construction as ConstructionIcon,
  Print as PrintIcon
} from '@mui/icons-material';

const ComandeListTable = ({
  comande,
  onEditComanda,
  onDeleteComanda,
  onInserimentoMetri,
  onPrintComanda,
  loading = false
}) => {
  const [searchTerm, setSearchTerm] = useState('');

  // Funzione per ottenere il colore del chip in base al tipo di comanda
  const getTipoComandaColor = (tipo) => {
    const colors = {
      'POSA': 'primary',
      'COLLEGAMENTO_PARTENZA': 'secondary',
      'COLLEGAMENTO_ARRIVO': 'info',
      'CERTIFICAZIONE': 'success',
      'TESTING': 'warning'
    };
    return colors[tipo] || 'default';
  };

  // Funzione per ottenere il colore del chip in base allo stato
  const getStatoColor = (stato) => {
    const colors = {
      'CREATA': 'default',
      'ASSEGNATA': 'info',
      'IN_CORSO': 'warning',
      'COMPLETATA': 'success',
      'ANNULLATA': 'error'
    };
    return colors[stato] || 'default';
  };



  // Funzione per ottenere l'etichetta del tipo di comanda
  const getTipoComandaLabel = (tipo) => {
    const labels = {
      'POSA': 'Posa',
      'COLLEGAMENTO_PARTENZA': 'Coll. Partenza',
      'COLLEGAMENTO_ARRIVO': 'Coll. Arrivo',
      'CERTIFICAZIONE': 'Certificazione',
      'TESTING': 'Testing'
    };
    return labels[tipo] || tipo;
  };

  // Assicurati che comande sia sempre un array
  const comandeArray = Array.isArray(comande) ? comande : [];

  // Filtro intelligente per la ricerca
  const filteredComande = comandeArray.filter(comanda => {
    if (!searchTerm) return true;

    const searchLower = searchTerm.toLowerCase();
    return (
      comanda.codice_comanda?.toLowerCase().includes(searchLower) ||
      comanda.responsabile?.toLowerCase().includes(searchLower) ||
      comanda.tipo_comanda?.toLowerCase().includes(searchLower) ||
      comanda.stato?.toLowerCase().includes(searchLower) ||
      comanda.descrizione?.toLowerCase().includes(searchLower) ||
      getTipoComandaLabel(comanda.tipo_comanda)?.toLowerCase().includes(searchLower)
    );
  });

  return (
    <Box>
      {/* Campo di ricerca */}
      <Box mb={3}>
        <TextField
          fullWidth
          placeholder="Cerca per codice, responsabile, tipo, stato o descrizione..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon color="action" />
              </InputAdornment>
            ),
          }}
          sx={{
            '& .MuiOutlinedInput-root': {
              backgroundColor: '#f5f7fa',
              '&:hover': {
                backgroundColor: 'rgba(33, 150, 243, 0.1)',
              },
              '&.Mui-focused': {
                backgroundColor: 'white',
              }
            }
          }}
        />
      </Box>

      {/* Tabella comande */}
      <TableContainer 
        component={Paper} 
        elevation={0} 
        sx={{ 
          border: '1px solid', 
          borderColor: 'grey.200',
          borderRadius: 2
        }}
      >
        <Table>
          <TableHead>
            <TableRow sx={{ backgroundColor: 'grey.50' }}>
              <TableCell sx={{ fontWeight: 600 }}>Codice</TableCell>
              <TableCell sx={{ fontWeight: 600 }}>Tipo</TableCell>
              <TableCell sx={{ fontWeight: 600 }}>Responsabile</TableCell>
              <TableCell sx={{ fontWeight: 600 }}>Contatti</TableCell>
              <TableCell sx={{ fontWeight: 600 }}>Stato</TableCell>
              <TableCell sx={{ fontWeight: 600 }}>Data Creazione</TableCell>
              <TableCell sx={{ fontWeight: 600, textAlign: 'center' }}>Cavi</TableCell>
              <TableCell sx={{ fontWeight: 600, textAlign: 'center' }}>Completamento</TableCell>
              <TableCell sx={{ fontWeight: 600, textAlign: 'center' }}>Azioni</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredComande.length === 0 ? (
              <TableRow>
                <TableCell colSpan={9} sx={{ textAlign: 'center', py: 4 }}>
                  <Typography variant="body2" color="text.secondary">
                    {searchTerm ? 'Nessuna comanda trovata per la ricerca' : 'Nessuna comanda disponibile'}
                  </Typography>
                </TableCell>
              </TableRow>
            ) : (
              filteredComande.map((comanda) => (
                <TableRow 
                  key={comanda.codice_comanda}
                  sx={{ 
                    '&:hover': { 
                      backgroundColor: 'rgba(33, 150, 243, 0.1)' 
                    } 
                  }}
                >
                  <TableCell>
                    <Typography variant="body2" fontWeight="bold">
                      {comanda.codice_comanda}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={getTipoComandaLabel(comanda.tipo_comanda)}
                      size="small"
                      color={getTipoComandaColor(comanda.tipo_comanda)}
                      variant="outlined"
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                      {comanda.responsabile || 'Non assegnato'}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Box>
                      {comanda.responsabile_telefono && (
                        <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.875rem' }}>
                          📞 {comanda.responsabile_telefono}
                        </Typography>
                      )}
                      {comanda.responsabile_email && (
                        <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.875rem' }}>
                          ✉️ {comanda.responsabile_email}
                        </Typography>
                      )}
                      {!comanda.responsabile_telefono && !comanda.responsabile_email && (
                        <Typography variant="body2" color="text.disabled" sx={{ fontSize: '0.875rem' }}>
                          Nessun contatto
                        </Typography>
                      )}
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={comanda.stato || 'CREATA'}
                      size="small"
                      color={getStatoColor(comanda.stato)}
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" color="text.secondary">
                      {comanda.data_creazione ? 
                        new Date(comanda.data_creazione).toLocaleDateString('it-IT') : 
                        '-'
                      }
                    </Typography>
                  </TableCell>
                  <TableCell sx={{ textAlign: 'center' }}>
                    <Typography variant="body2" fontWeight="bold" color="primary">
                      {comanda.numero_cavi_assegnati || 0}
                    </Typography>
                  </TableCell>
                  <TableCell sx={{ textAlign: 'center' }}>
                    <Typography variant="body2" fontWeight="bold">
                      {(comanda.percentuale_completamento || 0).toFixed(1)}%
                    </Typography>
                  </TableCell>
                  <TableCell sx={{ textAlign: 'center' }}>
                    <Stack direction="row" spacing={0.5} justifyContent="center">
                      {/* Pulsante Gestione Workflow - per comande POSA, COLLEGAMENTO_PARTENZA, COLLEGAMENTO_ARRIVO */}
                      {['POSA', 'COLLEGAMENTO_PARTENZA', 'COLLEGAMENTO_ARRIVO'].includes(comanda.tipo_comanda) && onInserimentoMetri && (
                        <Tooltip title={
                          comanda.tipo_comanda === 'POSA' ? 'Inserisci Metri Posati' :
                          comanda.tipo_comanda === 'COLLEGAMENTO_PARTENZA' ? 'Gestisci Collegamento Partenza' :
                          'Gestisci Collegamento Arrivo'
                        }>
                          <IconButton
                            size="small"
                            onClick={() => onInserimentoMetri(comanda)}
                            sx={{
                              color: '#2196f3',
                              '&:hover': {
                                backgroundColor: 'rgba(33, 150, 243, 0.1)'
                              }
                            }}
                          >
                            <ConstructionIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      )}

                      <Tooltip title="Stampa Comanda">
                        <IconButton
                          size="small"
                          onClick={() => onPrintComanda && onPrintComanda(comanda)}
                          sx={{
                            color: '#4caf50',
                            '&:hover': {
                              backgroundColor: 'rgba(76, 175, 80, 0.1)'
                            }
                          }}
                        >
                          <PrintIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>

                      <Tooltip title="Modifica">
                        <IconButton
                          size="small"
                          onClick={() => onEditComanda(comanda)}
                          sx={{
                            color: '#6c757d',
                            '&:hover': {
                              backgroundColor: '#e9ecef'
                            }
                          }}
                        >
                          <EditIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Elimina">
                        <IconButton
                          size="small"
                          onClick={() => onDeleteComanda(comanda.codice_comanda)}
                          sx={{
                            color: '#6c757d',
                            '&:hover': {
                              backgroundColor: '#e9ecef'
                            }
                          }}
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </Stack>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Informazioni sui risultati della ricerca */}
      {searchTerm && (
        <Box mt={2}>
          <Typography variant="caption" color="text.secondary">
            Mostrando {filteredComande.length} di {comandeArray.length} comande
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default ComandeListTable;
