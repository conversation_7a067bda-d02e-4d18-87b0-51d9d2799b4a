from .auth import Token, TokenData, UserLogin, CantiereLogin
from .user import UserBase, UserCreate, UserUpdate, UserInDB
from .cantiere import CantiereBase, CantiereCreate, CantiereUpdate, CantiereInDB
from .parco_cavi import ParcoCavoBase, ParcoCavoCreate, ParcoCavoUpdate, ParcoCavoInDB
from .responsabile import ResponsabileBase, ResponsabileCreate, ResponsabileUpdate, ResponsabileInDB, ResponsabileResponse
from .comanda import (
    ComandaBase, ComandaCreate, ComandaUpdate, ComandaInDB,
    ComandaDettaglioBase, ComandaDettaglioCreate, ComandaDettaglioInDB,
    AssegnaCaviRequest, DatiPosaRequest, DatiCollegamentoRequest, DatiCertificazioneRequest, CambiaStatoRequest,
    ComandaResponse, ComandaDettagliataResponse, ComandaListResponse, StatisticheComande
)

# Importa altri schemi man mano che vengono creati
