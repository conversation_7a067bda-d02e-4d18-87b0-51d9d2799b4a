{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\comande\\\\ComandeList.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Card, CardContent, Typography, Button, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Chip, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, TextField, MenuItem, Alert, CircularProgress, Tooltip, Grid, List, ListItem, ListItemText, Divider } from '@mui/material';\nimport { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, Visibility as ViewIcon, Assignment as AssignIcon, Refresh as RefreshIcon, People as PeopleIcon, Person as PersonIcon, Email as EmailIcon, Phone as PhoneIcon, ExpandMore as ExpandMoreIcon } from '@mui/icons-material';\nimport { Accordion, AccordionSummary, AccordionDetails, Tabs, Tab } from '@mui/material';\nimport comandeService from '../../services/comandeService';\nimport CreaComandaConCavi from './CreaComandaConCavi';\nimport responsabiliService from '../../services/responsabiliService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ComandeList = ({\n  cantiereId,\n  cantiereName\n}) => {\n  _s();\n  const [comande, setComande] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [selectedComanda, setSelectedComanda] = useState(null);\n  const [dialogMode, setDialogMode] = useState('edit'); // 'edit', 'view', 'assign'\n  const [formData, setFormData] = useState({\n    tipo_comanda: 'POSA',\n    descrizione: '',\n    responsabile: '',\n    data_scadenza: '',\n    priorita: 'NORMALE',\n    note_capo_cantiere: ''\n  });\n  const [statistiche, setStatistiche] = useState(null);\n  const [caviAssegnazione, setCaviAssegnazione] = useState('');\n  const [openCreaConCavi, setOpenCreaConCavi] = useState(false);\n\n  // Stati per gestione responsabili integrata\n  const [responsabili, setResponsabili] = useState([]);\n  const [loadingResponsabili, setLoadingResponsabili] = useState(false);\n  const [comandePerResponsabile, setComandePerResponsabile] = useState({});\n  const [openResponsabileDialog, setOpenResponsabileDialog] = useState(false);\n  const [dialogModeResponsabile, setDialogModeResponsabile] = useState('create');\n  const [selectedResponsabile, setSelectedResponsabile] = useState(null);\n  const [formDataResponsabile, setFormDataResponsabile] = useState({\n    nome_responsabile: '',\n    email: '',\n    telefono: ''\n  });\n\n  // Stato per le tabs\n  const [activeTab, setActiveTab] = useState(0);\n\n  // Carica le comande al mount del componente\n  useEffect(() => {\n    if (cantiereId) {\n      loadComande();\n      loadStatistiche();\n      loadResponsabili();\n    }\n  }, [cantiereId]);\n  const loadComande = async () => {\n    try {\n      setLoading(true);\n      const response = await comandeService.getComande(cantiereId);\n      setComande(response.comande || []);\n      setError(null);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n      setError('Errore nel caricamento delle comande');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadStatistiche = async () => {\n    try {\n      const stats = await comandeService.getStatisticheComande(cantiereId);\n      setStatistiche(stats);\n    } catch (err) {\n      console.error('Errore nel caricamento delle statistiche:', err);\n    }\n  };\n  const loadResponsabili = async () => {\n    try {\n      setLoadingResponsabili(true);\n      setError(null);\n      const data = await responsabiliService.getResponsabiliCantiere(cantiereId);\n      setResponsabili(data || []);\n\n      // Carica le comande per ogni responsabile\n      await loadComandePerResponsabili(data || []);\n    } catch (err) {\n      console.error('Errore nel caricamento dei responsabili:', err);\n      setError('Errore nel caricamento dei responsabili');\n    } finally {\n      setLoadingResponsabili(false);\n    }\n  };\n  const loadComandePerResponsabili = async responsabiliList => {\n    try {\n      const comandeMap = {};\n      for (const responsabile of responsabiliList) {\n        try {\n          const comande = await comandeService.getComandeByResponsabile(cantiereId, responsabile.nome_responsabile);\n          comandeMap[responsabile.id_responsabile] = comande || [];\n        } catch (err) {\n          console.error(`Errore nel caricamento comande per ${responsabile.nome_responsabile}:`, err);\n          comandeMap[responsabile.id_responsabile] = [];\n        }\n      }\n      setComandePerResponsabile(comandeMap);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n    }\n  };\n  const handleOpenDialog = (mode, comanda = null) => {\n    setDialogMode(mode);\n    setSelectedComanda(comanda);\n    if (mode === 'edit' && comanda) {\n      setFormData({\n        tipo_comanda: comanda.tipo_comanda,\n        descrizione: comanda.descrizione || '',\n        responsabile: comanda.responsabile || '',\n        data_scadenza: comanda.data_scadenza || '',\n        priorita: comanda.priorita || 'NORMALE',\n        note_capo_cantiere: comanda.note_capo_cantiere || ''\n      });\n    }\n    setOpenDialog(true);\n  };\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setSelectedComanda(null);\n    setCaviAssegnazione('');\n    setFormData({\n      tipo_comanda: 'POSA',\n      descrizione: '',\n      responsabile: '',\n      data_scadenza: '',\n      priorita: 'NORMALE',\n      note_capo_cantiere: ''\n    });\n  };\n  const handleSubmit = async () => {\n    try {\n      if (dialogMode === 'edit') {\n        // Modifica comanda esistente\n        const response = await comandeService.updateComanda(selectedComanda.codice_comanda, formData);\n        console.log('Comanda aggiornata:', response);\n      } else if (dialogMode === 'assign') {\n        // Assegnazione cavi\n        if (!caviAssegnazione.trim()) {\n          setError('Inserisci almeno un ID cavo');\n          return;\n        }\n        const listaIdCavi = caviAssegnazione.split(',').map(id => id.trim()).filter(id => id);\n        await comandeService.assegnaCavi(selectedComanda.codice_comanda, listaIdCavi);\n      }\n      handleCloseDialog();\n      loadComande();\n      loadStatistiche();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError('Errore nel salvataggio della comanda');\n    }\n  };\n  const handleDelete = async codiceComanda => {\n    if (window.confirm('Sei sicuro di voler eliminare questa comanda?')) {\n      try {\n        await comandeService.deleteComanda(codiceComanda);\n        loadComande();\n        loadStatistiche();\n      } catch (err) {\n        console.error('Errore nell\\'eliminazione:', err);\n        setError('Errore nell\\'eliminazione della comanda');\n      }\n    }\n  };\n\n  // Funzioni per gestione responsabili\n  const handleOpenResponsabileDialog = (mode, responsabile = null) => {\n    setDialogModeResponsabile(mode);\n    setSelectedResponsabile(responsabile);\n    if (mode === 'edit' && responsabile) {\n      setFormDataResponsabile({\n        nome_responsabile: responsabile.nome_responsabile || '',\n        email: responsabile.email || '',\n        telefono: responsabile.telefono || ''\n      });\n    } else {\n      setFormDataResponsabile({\n        nome_responsabile: '',\n        email: '',\n        telefono: ''\n      });\n    }\n    setOpenResponsabileDialog(true);\n  };\n  const handleCloseResponsabileDialog = () => {\n    setOpenResponsabileDialog(false);\n    setSelectedResponsabile(null);\n    setError(null);\n  };\n  const handleSubmitResponsabile = async () => {\n    try {\n      setError(null);\n\n      // Validazione\n      if (!formDataResponsabile.nome_responsabile.trim()) {\n        setError('Il nome del responsabile è obbligatorio');\n        return;\n      }\n      if (!formDataResponsabile.email && !formDataResponsabile.telefono) {\n        setError('Almeno uno tra email e telefono deve essere specificato');\n        return;\n      }\n      if (dialogModeResponsabile === 'create') {\n        await responsabiliService.createResponsabile(cantiereId, formDataResponsabile);\n      } else if (dialogModeResponsabile === 'edit') {\n        await responsabiliService.updateResponsabile(selectedResponsabile.id_responsabile, formDataResponsabile);\n      }\n      handleCloseResponsabileDialog();\n      await loadResponsabili();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError(err.detail || 'Errore nel salvataggio del responsabile');\n    }\n  };\n  const handleDeleteResponsabile = async idResponsabile => {\n    if (!window.confirm('Sei sicuro di voler eliminare questo responsabile?')) {\n      return;\n    }\n    try {\n      await responsabiliService.deleteResponsabile(idResponsabile);\n      await loadResponsabili();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione del responsabile');\n    }\n  };\n  const getStatoColor = stato => {\n    switch (stato) {\n      case 'CREATA':\n        return 'default';\n      case 'ASSEGNATA':\n        return 'primary';\n      case 'IN_CORSO':\n        return 'warning';\n      case 'COMPLETATA':\n        return 'success';\n      case 'ANNULLATA':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n  const getTipoComandaLabel = tipo => {\n    switch (tipo) {\n      case 'POSA':\n        return 'Posa';\n      case 'COLLEGAMENTO_PARTENZA':\n        return 'Collegamento Partenza';\n      case 'COLLEGAMENTO_ARRIVO':\n        return 'Collegamento Arrivo';\n      case 'CERTIFICAZIONE':\n        return 'Certificazione';\n      case 'TESTING':\n        return 'Testing/Certificazione';\n      default:\n        return tipo;\n    }\n  };\n  const getPrioritaColor = priorita => {\n    switch (priorita) {\n      case 'BASSA':\n        return 'default';\n      case 'NORMALE':\n        return 'primary';\n      case 'ALTA':\n        return 'warning';\n      case 'URGENTE':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      minHeight: \"400px\",\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 336,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      mb: 3,\n      children: statistiche && /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        mb: 2,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 2,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"textSecondary\",\n                gutterBottom: true,\n                children: \"Totale\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                children: statistiche.totale_comande\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 2,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"textSecondary\",\n                gutterBottom: true,\n                children: \"Create\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                children: statistiche.comande_create\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 367,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 2,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"textSecondary\",\n                gutterBottom: true,\n                children: \"Assegnate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                children: statistiche.comande_assegnate\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 379,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 373,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 2,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"textSecondary\",\n                gutterBottom: true,\n                children: \"In Corso\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                children: statistiche.comande_in_corso\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 391,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 385,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 2,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"textSecondary\",\n                gutterBottom: true,\n                children: \"Completate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                children: statistiche.comande_completate\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 2,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"textSecondary\",\n                gutterBottom: true,\n                children: \"% Completamento\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                children: [statistiche.percentuale_completamento_medio.toFixed(1), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 348,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 345,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"space-between\",\n      alignItems: \"center\",\n      mb: 2,\n      flexWrap: \"wrap\",\n      gap: 1,\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        gap: 1,\n        flexWrap: \"wrap\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 24\n          }, this),\n          onClick: () => setOpenCreaConCavi(true),\n          color: \"primary\",\n          children: \"Nuova Comanda\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 428,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(AssignIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 24\n          }, this),\n          onClick: () => {\n            if (comande.length === 0) {\n              setError('Nessuna comanda disponibile per l\\'assegnazione');\n              return;\n            }\n            // Apri dialog per selezionare comanda\n            setError('Seleziona una comanda dalla tabella e clicca sull\\'icona \"Assegna Cavi\"');\n          },\n          disabled: comande.length === 0,\n          children: \"Assegna Cavi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 439,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 427,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 458,\n          columnNumber: 22\n        }, this),\n        onClick: () => {\n          loadComande();\n          loadStatistiche();\n        },\n        children: \"Aggiorna\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 456,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 426,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 2\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 470,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Paper,\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Codice\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Tipo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Priorit\\xE0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 482,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Responsabile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 483,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Data Creazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 484,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Stato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Cavi Assegnati\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Completamento\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 487,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Azioni\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 478,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: comande.map(comanda => /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                fontWeight: \"bold\",\n                children: comanda.codice_comanda\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 495,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 494,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: getTipoComandaLabel(comanda.tipo_comanda),\n                size: \"small\",\n                variant: \"outlined\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 500,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 499,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: comanda.priorita || 'NORMALE',\n                size: \"small\",\n                color: getPrioritaColor(comanda.priorita || 'NORMALE')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 507,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: comanda.responsabile\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 513,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: new Date(comanda.data_creazione).toLocaleDateString('it-IT')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 514,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: comanda.stato,\n                color: getStatoColor(comanda.stato),\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 518,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 517,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: comanda.numero_cavi_assegnati || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 524,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: comanda.percentuale_completamento ? `${comanda.percentuale_completamento.toFixed(1)}%` : '0%'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 525,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"Visualizza\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: () => handleOpenDialog('view', comanda),\n                  children: /*#__PURE__*/_jsxDEV(ViewIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 535,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 531,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 530,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"Modifica\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: () => handleOpenDialog('edit', comanda),\n                  children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 543,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 539,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 538,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"Assegna Cavi\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: () => handleOpenDialog('assign', comanda),\n                  color: \"primary\",\n                  children: /*#__PURE__*/_jsxDEV(AssignIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 552,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 547,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 546,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"Elimina\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: () => handleDelete(comanda.codice_comanda),\n                  color: \"error\",\n                  children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 561,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 556,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 555,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 529,\n              columnNumber: 17\n            }, this)]\n          }, comanda.codice_comanda, true, {\n            fileName: _jsxFileName,\n            lineNumber: 493,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 491,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 477,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 476,\n      columnNumber: 7\n    }, this), comande.length === 0 && !loading && /*#__PURE__*/_jsxDEV(Box, {\n      textAlign: \"center\",\n      py: 4,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"textSecondary\",\n        children: \"Nessuna comanda trovata\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 573,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"textSecondary\",\n        children: \"Clicca su \\\"Nuova Comanda\\\" per iniziare\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 576,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 572,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openDialog,\n      onClose: handleCloseDialog,\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: [dialogMode === 'edit' && 'Modifica Comanda', dialogMode === 'view' && 'Dettagli Comanda', dialogMode === 'assign' && `Assegna Cavi - ${selectedComanda === null || selectedComanda === void 0 ? void 0 : selectedComanda.codice_comanda}`]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 584,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            pt: 1\n          },\n          children: dialogMode === 'assign' ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"info\",\n              sx: {\n                mb: 2\n              },\n              children: \"Inserisci gli ID dei cavi da assegnare alla comanda, separati da virgola.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 593,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"ID Cavi (separati da virgola)\",\n              value: caviAssegnazione,\n              onChange: e => setCaviAssegnazione(e.target.value),\n              margin: \"normal\",\n              placeholder: \"es: CAVO001, CAVO002, CAVO003\",\n              helperText: \"Esempio: CAVO001, CAVO002, CAVO003\",\n              multiline: true,\n              rows: 3\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 596,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) : dialogMode === 'view' && selectedComanda ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: /*#__PURE__*/_jsxDEV(List, {\n              children: [/*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Codice Comanda\",\n                  secondary: selectedComanda.codice_comanda\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 612,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 611,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 617,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Tipo\",\n                  secondary: getTipoComandaLabel(selectedComanda.tipo_comanda)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 619,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 618,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 624,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Stato\",\n                  secondary: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: selectedComanda.stato,\n                    color: getStatoColor(selectedComanda.stato),\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 629,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 626,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 625,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 637,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Descrizione\",\n                  secondary: selectedComanda.descrizione || 'Nessuna descrizione'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 639,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 638,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 644,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Priorit\\xE0\",\n                  secondary: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: selectedComanda.priorita || 'NORMALE',\n                    color: getPrioritaColor(selectedComanda.priorita || 'NORMALE'),\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 649,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 646,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 645,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 657,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Responsabile\",\n                  secondary: selectedComanda.responsabile || 'Non assegnato'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 659,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 658,\n                columnNumber: 19\n              }, this), selectedComanda.note_capo_cantiere && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 666,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                  children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: \"Note Capo Cantiere\",\n                    secondary: selectedComanda.note_capo_cantiere\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 668,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 667,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 675,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Data Creazione\",\n                  secondary: new Date(selectedComanda.data_creazione).toLocaleDateString('it-IT')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 677,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 676,\n                columnNumber: 19\n              }, this), selectedComanda.data_scadenza && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 684,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                  children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: \"Data Scadenza\",\n                    secondary: new Date(selectedComanda.data_scadenza).toLocaleDateString('it-IT')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 686,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 685,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 693,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Cavi Assegnati\",\n                  secondary: selectedComanda.numero_cavi_assegnati || 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 695,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 694,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 700,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Completamento\",\n                  secondary: `${(selectedComanda.percentuale_completamento || 0).toFixed(1)}%`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 702,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 701,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 610,\n              columnNumber: 17\n            }, this)\n          }, void 0, false) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              select: true,\n              label: \"Tipo Comanda\",\n              value: formData.tipo_comanda,\n              onChange: e => setFormData({\n                ...formData,\n                tipo_comanda: e.target.value\n              }),\n              margin: \"normal\",\n              disabled: dialogMode === 'view',\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"POSA\",\n                children: \"Posa\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 720,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"COLLEGAMENTO_PARTENZA\",\n                children: \"Collegamento Partenza\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 721,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"COLLEGAMENTO_ARRIVO\",\n                children: \"Collegamento Arrivo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 722,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"CERTIFICAZIONE\",\n                children: \"Certificazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 723,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"TESTING\",\n                children: \"Testing\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 724,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 711,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              select: true,\n              label: \"Priorit\\xE0\",\n              value: formData.priorita,\n              onChange: e => setFormData({\n                ...formData,\n                priorita: e.target.value\n              }),\n              margin: \"normal\",\n              disabled: dialogMode === 'view',\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"BASSA\",\n                children: \"Bassa\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 736,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"NORMALE\",\n                children: \"Normale\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 737,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"ALTA\",\n                children: \"Alta\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 738,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"URGENTE\",\n                children: \"Urgente\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 739,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 727,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Descrizione\",\n              value: formData.descrizione,\n              onChange: e => setFormData({\n                ...formData,\n                descrizione: e.target.value\n              }),\n              margin: \"normal\",\n              multiline: true,\n              rows: 3,\n              disabled: dialogMode === 'view'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 742,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Responsabile\",\n              value: formData.responsabile,\n              onChange: e => setFormData({\n                ...formData,\n                responsabile: e.target.value\n              }),\n              margin: \"normal\",\n              disabled: dialogMode === 'view',\n              required: true,\n              helperText: \"Chi eseguir\\xE0 il lavoro (obbligatorio)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 753,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Note Capo Cantiere\",\n              value: formData.note_capo_cantiere,\n              onChange: e => setFormData({\n                ...formData,\n                note_capo_cantiere: e.target.value\n              }),\n              margin: \"normal\",\n              multiline: true,\n              rows: 2,\n              disabled: dialogMode === 'view',\n              helperText: \"Istruzioni specifiche per il responsabile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 764,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Data Scadenza\",\n              type: \"date\",\n              value: formData.data_scadenza,\n              onChange: e => setFormData({\n                ...formData,\n                data_scadenza: e.target.value\n              }),\n              margin: \"normal\",\n              InputLabelProps: {\n                shrink: true\n              },\n              disabled: dialogMode === 'view'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 776,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 590,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 589,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseDialog,\n          children: dialogMode === 'view' ? 'Chiudi' : 'Annulla'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 791,\n          columnNumber: 11\n        }, this), dialogMode !== 'view' && /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSubmit,\n          variant: \"contained\",\n          children: dialogMode === 'edit' ? 'Salva' : dialogMode === 'assign' ? 'Assegna Cavi' : 'Salva'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 795,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 790,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 583,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CreaComandaConCavi, {\n      cantiereId: cantiereId,\n      open: openCreaConCavi,\n      onClose: () => setOpenCreaConCavi(false),\n      onSuccess: () => {\n        loadComande();\n        loadStatistiche();\n        setOpenCreaConCavi(false);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 804,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 343,\n    columnNumber: 5\n  }, this);\n};\n_s(ComandeList, \"4sKIyp801Fmvwb83RoKeDo/plnQ=\");\n_c = ComandeList;\nexport default ComandeList;\nvar _c;\n$RefreshReg$(_c, \"ComandeList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "Chip", "IconButton", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "MenuItem", "<PERSON><PERSON>", "CircularProgress", "<PERSON><PERSON><PERSON>", "Grid", "List", "ListItem", "ListItemText", "Divider", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "Visibility", "ViewIcon", "Assignment", "AssignIcon", "Refresh", "RefreshIcon", "People", "PeopleIcon", "Person", "PersonIcon", "Email", "EmailIcon", "Phone", "PhoneIcon", "ExpandMore", "ExpandMoreIcon", "Accordion", "AccordionSummary", "AccordionDetails", "Tabs", "Tab", "comandeService", "CreaComandaConCavi", "responsabiliService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ComandeList", "cantiereId", "cantiereName", "_s", "comande", "setComande", "loading", "setLoading", "error", "setError", "openDialog", "setOpenDialog", "selectedComanda", "setSelectedComanda", "dialogMode", "setDialogMode", "formData", "setFormData", "tipo_comanda", "descrizione", "responsabile", "data_scadenza", "priorita", "note_capo_cantiere", "statistiche", "setStatistiche", "caviAssegnazione", "setCaviAssegnazione", "openCreaConCavi", "setOpenCreaConCavi", "responsabili", "setResponsabili", "loadingResponsabili", "setLoadingResponsabili", "comandePerResponsabile", "setComandePerResponsabile", "openResponsabileDialog", "setOpenResponsabileDialog", "dialogModeResponsabile", "setDialogModeResponsabile", "selectedResponsabile", "setSelectedResponsabile", "formDataResponsabile", "setFormDataResponsabile", "nome_responsabile", "email", "telefono", "activeTab", "setActiveTab", "loadComande", "loadStatistiche", "loadResponsabili", "response", "getComande", "err", "console", "stats", "getStatisticheComande", "data", "getResponsabiliCantiere", "loadComandePerResponsabili", "responsabiliList", "comandeMap", "getComandeByResponsabile", "id_responsabile", "handleOpenDialog", "mode", "comanda", "handleCloseDialog", "handleSubmit", "updateComanda", "codice_comanda", "log", "trim", "listaIdCavi", "split", "map", "id", "filter", "assegnaCavi", "handleDelete", "codiceComanda", "window", "confirm", "deleteComanda", "handleOpenResponsabileDialog", "handleCloseResponsabileDialog", "handleSubmitResponsabile", "createResponsabile", "updateResponsabile", "detail", "handleDeleteResponsabile", "idResponsabile", "deleteResponsabile", "getStatoColor", "stato", "getTipoComandaLabel", "tipo", "getPrioritaColor", "display", "justifyContent", "alignItems", "minHeight", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "mb", "container", "spacing", "item", "xs", "sm", "md", "color", "gutterBottom", "variant", "totale_comande", "comande_create", "comande_assegnate", "comande_in_corso", "comande_completate", "percentuale_completamento_medio", "toFixed", "flexWrap", "gap", "startIcon", "onClick", "length", "disabled", "severity", "sx", "component", "fontWeight", "label", "size", "Date", "data_creazione", "toLocaleDateString", "numero_cavi_assegnati", "percentuale_completamento", "title", "textAlign", "py", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "pt", "value", "onChange", "e", "target", "margin", "placeholder", "helperText", "multiline", "rows", "primary", "secondary", "select", "required", "type", "InputLabelProps", "shrink", "onSuccess", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/comande/ComandeList.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Card,\n  CardContent,\n  Typo<PERSON>,\n  Button,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  Chip,\n  IconButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  MenuItem,\n  Alert,\n  CircularProgress,\n  Tooltip,\n  Grid,\n  List,\n  ListItem,\n  ListItemText,\n  Divider\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  Visibility as ViewIcon,\n  Assignment as AssignIcon,\n  Refresh as RefreshIcon,\n  People as PeopleIcon,\n  Person as PersonIcon,\n  Email as EmailIcon,\n  Phone as PhoneIcon,\n  ExpandMore as ExpandMoreIcon\n} from '@mui/icons-material';\nimport {\n  Accordion,\n  AccordionSummary,\n  AccordionDetails,\n  Tabs,\n  Tab\n} from '@mui/material';\nimport comandeService from '../../services/comandeService';\nimport CreaComandaConCavi from './CreaComandaConCavi';\nimport responsabiliService from '../../services/responsabiliService';\n\nconst ComandeList = ({ cantiereId, cantiereName }) => {\n  const [comande, setComande] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [selectedComanda, setSelectedComanda] = useState(null);\n  const [dialogMode, setDialogMode] = useState('edit'); // 'edit', 'view', 'assign'\n  const [formData, setFormData] = useState({\n    tipo_comanda: 'POSA',\n    descrizione: '',\n    responsabile: '',\n    data_scadenza: '',\n    priorita: 'NORMALE',\n    note_capo_cantiere: ''\n  });\n  const [statistiche, setStatistiche] = useState(null);\n  const [caviAssegnazione, setCaviAssegnazione] = useState('');\n  const [openCreaConCavi, setOpenCreaConCavi] = useState(false);\n\n  // Stati per gestione responsabili integrata\n  const [responsabili, setResponsabili] = useState([]);\n  const [loadingResponsabili, setLoadingResponsabili] = useState(false);\n  const [comandePerResponsabile, setComandePerResponsabile] = useState({});\n  const [openResponsabileDialog, setOpenResponsabileDialog] = useState(false);\n  const [dialogModeResponsabile, setDialogModeResponsabile] = useState('create');\n  const [selectedResponsabile, setSelectedResponsabile] = useState(null);\n  const [formDataResponsabile, setFormDataResponsabile] = useState({\n    nome_responsabile: '',\n    email: '',\n    telefono: ''\n  });\n\n  // Stato per le tabs\n  const [activeTab, setActiveTab] = useState(0);\n\n  // Carica le comande al mount del componente\n  useEffect(() => {\n    if (cantiereId) {\n      loadComande();\n      loadStatistiche();\n      loadResponsabili();\n    }\n  }, [cantiereId]);\n\n  const loadComande = async () => {\n    try {\n      setLoading(true);\n      const response = await comandeService.getComande(cantiereId);\n      setComande(response.comande || []);\n      setError(null);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n      setError('Errore nel caricamento delle comande');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadStatistiche = async () => {\n    try {\n      const stats = await comandeService.getStatisticheComande(cantiereId);\n      setStatistiche(stats);\n    } catch (err) {\n      console.error('Errore nel caricamento delle statistiche:', err);\n    }\n  };\n\n  const loadResponsabili = async () => {\n    try {\n      setLoadingResponsabili(true);\n      setError(null);\n      const data = await responsabiliService.getResponsabiliCantiere(cantiereId);\n      setResponsabili(data || []);\n\n      // Carica le comande per ogni responsabile\n      await loadComandePerResponsabili(data || []);\n    } catch (err) {\n      console.error('Errore nel caricamento dei responsabili:', err);\n      setError('Errore nel caricamento dei responsabili');\n    } finally {\n      setLoadingResponsabili(false);\n    }\n  };\n\n  const loadComandePerResponsabili = async (responsabiliList) => {\n    try {\n      const comandeMap = {};\n\n      for (const responsabile of responsabiliList) {\n        try {\n          const comande = await comandeService.getComandeByResponsabile(cantiereId, responsabile.nome_responsabile);\n          comandeMap[responsabile.id_responsabile] = comande || [];\n        } catch (err) {\n          console.error(`Errore nel caricamento comande per ${responsabile.nome_responsabile}:`, err);\n          comandeMap[responsabile.id_responsabile] = [];\n        }\n      }\n\n      setComandePerResponsabile(comandeMap);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n    }\n  };\n\n  const handleOpenDialog = (mode, comanda = null) => {\n    setDialogMode(mode);\n    setSelectedComanda(comanda);\n\n    if (mode === 'edit' && comanda) {\n      setFormData({\n        tipo_comanda: comanda.tipo_comanda,\n        descrizione: comanda.descrizione || '',\n        responsabile: comanda.responsabile || '',\n        data_scadenza: comanda.data_scadenza || '',\n        priorita: comanda.priorita || 'NORMALE',\n        note_capo_cantiere: comanda.note_capo_cantiere || ''\n      });\n    }\n\n    setOpenDialog(true);\n  };\n\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setSelectedComanda(null);\n    setCaviAssegnazione('');\n    setFormData({\n      tipo_comanda: 'POSA',\n      descrizione: '',\n      responsabile: '',\n      data_scadenza: '',\n      priorita: 'NORMALE',\n      note_capo_cantiere: ''\n    });\n  };\n\n  const handleSubmit = async () => {\n    try {\n      if (dialogMode === 'edit') {\n        // Modifica comanda esistente\n        const response = await comandeService.updateComanda(selectedComanda.codice_comanda, formData);\n        console.log('Comanda aggiornata:', response);\n      } else if (dialogMode === 'assign') {\n        // Assegnazione cavi\n        if (!caviAssegnazione.trim()) {\n          setError('Inserisci almeno un ID cavo');\n          return;\n        }\n\n        const listaIdCavi = caviAssegnazione.split(',').map(id => id.trim()).filter(id => id);\n        await comandeService.assegnaCavi(selectedComanda.codice_comanda, listaIdCavi);\n      }\n\n      handleCloseDialog();\n      loadComande();\n      loadStatistiche();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError('Errore nel salvataggio della comanda');\n    }\n  };\n\n  const handleDelete = async (codiceComanda) => {\n    if (window.confirm('Sei sicuro di voler eliminare questa comanda?')) {\n      try {\n        await comandeService.deleteComanda(codiceComanda);\n        loadComande();\n        loadStatistiche();\n      } catch (err) {\n        console.error('Errore nell\\'eliminazione:', err);\n        setError('Errore nell\\'eliminazione della comanda');\n      }\n    }\n  };\n\n  // Funzioni per gestione responsabili\n  const handleOpenResponsabileDialog = (mode, responsabile = null) => {\n    setDialogModeResponsabile(mode);\n    setSelectedResponsabile(responsabile);\n\n    if (mode === 'edit' && responsabile) {\n      setFormDataResponsabile({\n        nome_responsabile: responsabile.nome_responsabile || '',\n        email: responsabile.email || '',\n        telefono: responsabile.telefono || ''\n      });\n    } else {\n      setFormDataResponsabile({\n        nome_responsabile: '',\n        email: '',\n        telefono: ''\n      });\n    }\n\n    setOpenResponsabileDialog(true);\n  };\n\n  const handleCloseResponsabileDialog = () => {\n    setOpenResponsabileDialog(false);\n    setSelectedResponsabile(null);\n    setError(null);\n  };\n\n  const handleSubmitResponsabile = async () => {\n    try {\n      setError(null);\n\n      // Validazione\n      if (!formDataResponsabile.nome_responsabile.trim()) {\n        setError('Il nome del responsabile è obbligatorio');\n        return;\n      }\n\n      if (!formDataResponsabile.email && !formDataResponsabile.telefono) {\n        setError('Almeno uno tra email e telefono deve essere specificato');\n        return;\n      }\n\n      if (dialogModeResponsabile === 'create') {\n        await responsabiliService.createResponsabile(cantiereId, formDataResponsabile);\n      } else if (dialogModeResponsabile === 'edit') {\n        await responsabiliService.updateResponsabile(selectedResponsabile.id_responsabile, formDataResponsabile);\n      }\n\n      handleCloseResponsabileDialog();\n      await loadResponsabili();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError(err.detail || 'Errore nel salvataggio del responsabile');\n    }\n  };\n\n  const handleDeleteResponsabile = async (idResponsabile) => {\n    if (!window.confirm('Sei sicuro di voler eliminare questo responsabile?')) {\n      return;\n    }\n\n    try {\n      await responsabiliService.deleteResponsabile(idResponsabile);\n      await loadResponsabili();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione del responsabile');\n    }\n  };\n\n  const getStatoColor = (stato) => {\n    switch (stato) {\n      case 'CREATA': return 'default';\n      case 'ASSEGNATA': return 'primary';\n      case 'IN_CORSO': return 'warning';\n      case 'COMPLETATA': return 'success';\n      case 'ANNULLATA': return 'error';\n      default: return 'default';\n    }\n  };\n\n  const getTipoComandaLabel = (tipo) => {\n    switch (tipo) {\n      case 'POSA': return 'Posa';\n      case 'COLLEGAMENTO_PARTENZA': return 'Collegamento Partenza';\n      case 'COLLEGAMENTO_ARRIVO': return 'Collegamento Arrivo';\n      case 'CERTIFICAZIONE': return 'Certificazione';\n      case 'TESTING': return 'Testing/Certificazione';\n      default: return tipo;\n    }\n  };\n\n  const getPrioritaColor = (priorita) => {\n    switch (priorita) {\n      case 'BASSA': return 'default';\n      case 'NORMALE': return 'primary';\n      case 'ALTA': return 'warning';\n      case 'URGENTE': return 'error';\n      default: return 'default';\n    }\n  };\n\n  if (loading) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"400px\">\n        <CircularProgress />\n      </Box>\n    );\n  }\n\n  return (\n    <Box>\n      {/* Header con statistiche */}\n      <Box mb={3}>\n        \n        {statistiche && (\n          <Grid container spacing={2} mb={2}>\n            <Grid item xs={12} sm={6} md={2}>\n              <Card>\n                <CardContent>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    Totale\n                  </Typography>\n                  <Typography variant=\"h5\">\n                    {statistiche.totale_comande}\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n            <Grid item xs={12} sm={6} md={2}>\n              <Card>\n                <CardContent>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    Create\n                  </Typography>\n                  <Typography variant=\"h5\">\n                    {statistiche.comande_create}\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n            <Grid item xs={12} sm={6} md={2}>\n              <Card>\n                <CardContent>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    Assegnate\n                  </Typography>\n                  <Typography variant=\"h5\">\n                    {statistiche.comande_assegnate}\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n            <Grid item xs={12} sm={6} md={2}>\n              <Card>\n                <CardContent>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    In Corso\n                  </Typography>\n                  <Typography variant=\"h5\">\n                    {statistiche.comande_in_corso}\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n            <Grid item xs={12} sm={6} md={2}>\n              <Card>\n                <CardContent>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    Completate\n                  </Typography>\n                  <Typography variant=\"h5\">\n                    {statistiche.comande_completate}\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n            <Grid item xs={12} sm={6} md={2}>\n              <Card>\n                <CardContent>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    % Completamento\n                  </Typography>\n                  <Typography variant=\"h5\">\n                    {statistiche.percentuale_completamento_medio.toFixed(1)}%\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n          </Grid>\n        )}\n      </Box>\n\n      {/* Toolbar */}\n      <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={2} flexWrap=\"wrap\" gap={1}>\n        <Box display=\"flex\" gap={1} flexWrap=\"wrap\">\n          <Button\n            variant=\"contained\"\n            startIcon={<AddIcon />}\n            onClick={() => setOpenCreaConCavi(true)}\n            color=\"primary\"\n          >\n            Nuova Comanda\n          </Button>\n\n\n\n          <Button\n            variant=\"outlined\"\n            startIcon={<AssignIcon />}\n            onClick={() => {\n              if (comande.length === 0) {\n                setError('Nessuna comanda disponibile per l\\'assegnazione');\n                return;\n              }\n              // Apri dialog per selezionare comanda\n              setError('Seleziona una comanda dalla tabella e clicca sull\\'icona \"Assegna Cavi\"');\n            }}\n            disabled={comande.length === 0}\n          >\n            Assegna Cavi\n          </Button>\n        </Box>\n\n        <Button\n          variant=\"outlined\"\n          startIcon={<RefreshIcon />}\n          onClick={() => {\n            loadComande();\n            loadStatistiche();\n          }}\n        >\n          Aggiorna\n        </Button>\n      </Box>\n\n      {/* Messaggio di errore */}\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          {error}\n        </Alert>\n      )}\n\n      {/* Tabella comande */}\n      <TableContainer component={Paper}>\n        <Table>\n          <TableHead>\n            <TableRow>\n              <TableCell>Codice</TableCell>\n              <TableCell>Tipo</TableCell>\n              <TableCell>Priorità</TableCell>\n              <TableCell>Responsabile</TableCell>\n              <TableCell>Data Creazione</TableCell>\n              <TableCell>Stato</TableCell>\n              <TableCell>Cavi Assegnati</TableCell>\n              <TableCell>Completamento</TableCell>\n              <TableCell>Azioni</TableCell>\n            </TableRow>\n          </TableHead>\n          <TableBody>\n            {comande.map((comanda) => (\n              <TableRow key={comanda.codice_comanda}>\n                <TableCell>\n                  <Typography variant=\"body2\" fontWeight=\"bold\">\n                    {comanda.codice_comanda}\n                  </Typography>\n                </TableCell>\n                <TableCell>\n                  <Chip\n                    label={getTipoComandaLabel(comanda.tipo_comanda)}\n                    size=\"small\"\n                    variant=\"outlined\"\n                  />\n                </TableCell>\n                <TableCell>\n                  <Chip\n                    label={comanda.priorita || 'NORMALE'}\n                    size=\"small\"\n                    color={getPrioritaColor(comanda.priorita || 'NORMALE')}\n                  />\n                </TableCell>\n                <TableCell>{comanda.responsabile}</TableCell>\n                <TableCell>\n                  {new Date(comanda.data_creazione).toLocaleDateString('it-IT')}\n                </TableCell>\n                <TableCell>\n                  <Chip \n                    label={comanda.stato}\n                    color={getStatoColor(comanda.stato)}\n                    size=\"small\"\n                  />\n                </TableCell>\n                <TableCell>{comanda.numero_cavi_assegnati || 0}</TableCell>\n                <TableCell>\n                  {comanda.percentuale_completamento ? \n                    `${comanda.percentuale_completamento.toFixed(1)}%` : '0%'}\n                </TableCell>\n                <TableCell>\n                  <Tooltip title=\"Visualizza\">\n                    <IconButton \n                      size=\"small\"\n                      onClick={() => handleOpenDialog('view', comanda)}\n                    >\n                      <ViewIcon />\n                    </IconButton>\n                  </Tooltip>\n                  <Tooltip title=\"Modifica\">\n                    <IconButton \n                      size=\"small\"\n                      onClick={() => handleOpenDialog('edit', comanda)}\n                    >\n                      <EditIcon />\n                    </IconButton>\n                  </Tooltip>\n                  <Tooltip title=\"Assegna Cavi\">\n                    <IconButton\n                      size=\"small\"\n                      onClick={() => handleOpenDialog('assign', comanda)}\n                      color=\"primary\"\n                    >\n                      <AssignIcon />\n                    </IconButton>\n                  </Tooltip>\n                  <Tooltip title=\"Elimina\">\n                    <IconButton\n                      size=\"small\"\n                      onClick={() => handleDelete(comanda.codice_comanda)}\n                      color=\"error\"\n                    >\n                      <DeleteIcon />\n                    </IconButton>\n                  </Tooltip>\n                </TableCell>\n              </TableRow>\n            ))}\n          </TableBody>\n        </Table>\n      </TableContainer>\n\n      {comande.length === 0 && !loading && (\n        <Box textAlign=\"center\" py={4}>\n          <Typography variant=\"h6\" color=\"textSecondary\">\n            Nessuna comanda trovata\n          </Typography>\n          <Typography variant=\"body2\" color=\"textSecondary\">\n            Clicca su \"Nuova Comanda\" per iniziare\n          </Typography>\n        </Box>\n      )}\n\n      {/* Dialog per modifica/assegnazione */}\n      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n        <DialogTitle>\n          {dialogMode === 'edit' && 'Modifica Comanda'}\n          {dialogMode === 'view' && 'Dettagli Comanda'}\n          {dialogMode === 'assign' && `Assegna Cavi - ${selectedComanda?.codice_comanda}`}\n        </DialogTitle>\n        <DialogContent>\n          <Box sx={{ pt: 1 }}>\n            {dialogMode === 'assign' ? (\n              <>\n                <Alert severity=\"info\" sx={{ mb: 2 }}>\n                  Inserisci gli ID dei cavi da assegnare alla comanda, separati da virgola.\n                </Alert>\n                <TextField\n                  fullWidth\n                  label=\"ID Cavi (separati da virgola)\"\n                  value={caviAssegnazione}\n                  onChange={(e) => setCaviAssegnazione(e.target.value)}\n                  margin=\"normal\"\n                  placeholder=\"es: CAVO001, CAVO002, CAVO003\"\n                  helperText=\"Esempio: CAVO001, CAVO002, CAVO003\"\n                  multiline\n                  rows={3}\n                />\n              </>\n            ) : dialogMode === 'view' && selectedComanda ? (\n              <>\n                <List>\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Codice Comanda\"\n                      secondary={selectedComanda.codice_comanda}\n                    />\n                  </ListItem>\n                  <Divider />\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Tipo\"\n                      secondary={getTipoComandaLabel(selectedComanda.tipo_comanda)}\n                    />\n                  </ListItem>\n                  <Divider />\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Stato\"\n                      secondary={\n                        <Chip\n                          label={selectedComanda.stato}\n                          color={getStatoColor(selectedComanda.stato)}\n                          size=\"small\"\n                        />\n                      }\n                    />\n                  </ListItem>\n                  <Divider />\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Descrizione\"\n                      secondary={selectedComanda.descrizione || 'Nessuna descrizione'}\n                    />\n                  </ListItem>\n                  <Divider />\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Priorità\"\n                      secondary={\n                        <Chip\n                          label={selectedComanda.priorita || 'NORMALE'}\n                          color={getPrioritaColor(selectedComanda.priorita || 'NORMALE')}\n                          size=\"small\"\n                        />\n                      }\n                    />\n                  </ListItem>\n                  <Divider />\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Responsabile\"\n                      secondary={selectedComanda.responsabile || 'Non assegnato'}\n                    />\n                  </ListItem>\n                  {selectedComanda.note_capo_cantiere && (\n                    <>\n                      <Divider />\n                      <ListItem>\n                        <ListItemText\n                          primary=\"Note Capo Cantiere\"\n                          secondary={selectedComanda.note_capo_cantiere}\n                        />\n                      </ListItem>\n                    </>\n                  )}\n                  <Divider />\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Data Creazione\"\n                      secondary={new Date(selectedComanda.data_creazione).toLocaleDateString('it-IT')}\n                    />\n                  </ListItem>\n                  {selectedComanda.data_scadenza && (\n                    <>\n                      <Divider />\n                      <ListItem>\n                        <ListItemText\n                          primary=\"Data Scadenza\"\n                          secondary={new Date(selectedComanda.data_scadenza).toLocaleDateString('it-IT')}\n                        />\n                      </ListItem>\n                    </>\n                  )}\n                  <Divider />\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Cavi Assegnati\"\n                      secondary={selectedComanda.numero_cavi_assegnati || 0}\n                    />\n                  </ListItem>\n                  <Divider />\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Completamento\"\n                      secondary={`${(selectedComanda.percentuale_completamento || 0).toFixed(1)}%`}\n                    />\n                  </ListItem>\n                </List>\n              </>\n            ) : (\n              <>\n                <TextField\n                  fullWidth\n                  select\n                  label=\"Tipo Comanda\"\n                  value={formData.tipo_comanda}\n                  onChange={(e) => setFormData({ ...formData, tipo_comanda: e.target.value })}\n                  margin=\"normal\"\n                  disabled={dialogMode === 'view'}\n                >\n                  <MenuItem value=\"POSA\">Posa</MenuItem>\n                  <MenuItem value=\"COLLEGAMENTO_PARTENZA\">Collegamento Partenza</MenuItem>\n                  <MenuItem value=\"COLLEGAMENTO_ARRIVO\">Collegamento Arrivo</MenuItem>\n                  <MenuItem value=\"CERTIFICAZIONE\">Certificazione</MenuItem>\n                  <MenuItem value=\"TESTING\">Testing</MenuItem>\n                </TextField>\n\n                <TextField\n                  fullWidth\n                  select\n                  label=\"Priorità\"\n                  value={formData.priorita}\n                  onChange={(e) => setFormData({ ...formData, priorita: e.target.value })}\n                  margin=\"normal\"\n                  disabled={dialogMode === 'view'}\n                >\n                  <MenuItem value=\"BASSA\">Bassa</MenuItem>\n                  <MenuItem value=\"NORMALE\">Normale</MenuItem>\n                  <MenuItem value=\"ALTA\">Alta</MenuItem>\n                  <MenuItem value=\"URGENTE\">Urgente</MenuItem>\n                </TextField>\n\n                <TextField\n                  fullWidth\n                  label=\"Descrizione\"\n                  value={formData.descrizione}\n                  onChange={(e) => setFormData({ ...formData, descrizione: e.target.value })}\n                  margin=\"normal\"\n                  multiline\n                  rows={3}\n                  disabled={dialogMode === 'view'}\n                />\n\n                <TextField\n                  fullWidth\n                  label=\"Responsabile\"\n                  value={formData.responsabile}\n                  onChange={(e) => setFormData({ ...formData, responsabile: e.target.value })}\n                  margin=\"normal\"\n                  disabled={dialogMode === 'view'}\n                  required\n                  helperText=\"Chi eseguirà il lavoro (obbligatorio)\"\n                />\n\n                <TextField\n                  fullWidth\n                  label=\"Note Capo Cantiere\"\n                  value={formData.note_capo_cantiere}\n                  onChange={(e) => setFormData({ ...formData, note_capo_cantiere: e.target.value })}\n                  margin=\"normal\"\n                  multiline\n                  rows={2}\n                  disabled={dialogMode === 'view'}\n                  helperText=\"Istruzioni specifiche per il responsabile\"\n                />\n\n                <TextField\n                  fullWidth\n                  label=\"Data Scadenza\"\n                  type=\"date\"\n                  value={formData.data_scadenza}\n                  onChange={(e) => setFormData({ ...formData, data_scadenza: e.target.value })}\n                  margin=\"normal\"\n                  InputLabelProps={{ shrink: true }}\n                  disabled={dialogMode === 'view'}\n                />\n              </>\n            )}\n          </Box>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handleCloseDialog}>\n            {dialogMode === 'view' ? 'Chiudi' : 'Annulla'}\n          </Button>\n          {dialogMode !== 'view' && (\n            <Button onClick={handleSubmit} variant=\"contained\">\n              {dialogMode === 'edit' ? 'Salva' :\n               dialogMode === 'assign' ? 'Assegna Cavi' : 'Salva'}\n            </Button>\n          )}\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog per creazione comanda con cavi */}\n      <CreaComandaConCavi\n        cantiereId={cantiereId}\n        open={openCreaConCavi}\n        onClose={() => setOpenCreaConCavi(false)}\n        onSuccess={() => {\n          loadComande();\n          loadStatistiche();\n          setOpenCreaConCavi(false);\n        }}\n      />\n\n\n    </Box>\n  );\n};\n\nexport default ComandeList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,gBAAgB,EAChBC,OAAO,EACPC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,OAAO,QACF,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,UAAU,IAAIC,QAAQ,EACtBC,UAAU,IAAIC,UAAU,EACxBC,OAAO,IAAIC,WAAW,EACtBC,MAAM,IAAIC,UAAU,EACpBC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,KAAK,IAAIC,SAAS,EAClBC,UAAU,IAAIC,cAAc,QACvB,qBAAqB;AAC5B,SACEC,SAAS,EACTC,gBAAgB,EAChBC,gBAAgB,EAChBC,IAAI,EACJC,GAAG,QACE,eAAe;AACtB,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,mBAAmB,MAAM,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErE,MAAMC,WAAW,GAAGA,CAAC;EAAEC,UAAU;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACpD,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGrE,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACsE,OAAO,EAAEC,UAAU,CAAC,GAAGvE,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwE,KAAK,EAAEC,QAAQ,CAAC,GAAGzE,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC0E,UAAU,EAAEC,aAAa,CAAC,GAAG3E,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC4E,eAAe,EAAEC,kBAAkB,CAAC,GAAG7E,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC8E,UAAU,EAAEC,aAAa,CAAC,GAAG/E,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;EACtD,MAAM,CAACgF,QAAQ,EAAEC,WAAW,CAAC,GAAGjF,QAAQ,CAAC;IACvCkF,YAAY,EAAE,MAAM;IACpBC,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE,EAAE;IACjBC,QAAQ,EAAE,SAAS;IACnBC,kBAAkB,EAAE;EACtB,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGzF,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC0F,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3F,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC4F,eAAe,EAAEC,kBAAkB,CAAC,GAAG7F,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAM,CAAC8F,YAAY,EAAEC,eAAe,CAAC,GAAG/F,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACgG,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGjG,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACkG,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGnG,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxE,MAAM,CAACoG,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGrG,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAACsG,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGvG,QAAQ,CAAC,QAAQ,CAAC;EAC9E,MAAM,CAACwG,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGzG,QAAQ,CAAC,IAAI,CAAC;EACtE,MAAM,CAAC0G,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG3G,QAAQ,CAAC;IAC/D4G,iBAAiB,EAAE,EAAE;IACrBC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGhH,QAAQ,CAAC,CAAC,CAAC;;EAE7C;EACAC,SAAS,CAAC,MAAM;IACd,IAAIgE,UAAU,EAAE;MACdgD,WAAW,CAAC,CAAC;MACbC,eAAe,CAAC,CAAC;MACjBC,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAAClD,UAAU,CAAC,CAAC;EAEhB,MAAMgD,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF1C,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM6C,QAAQ,GAAG,MAAM3D,cAAc,CAAC4D,UAAU,CAACpD,UAAU,CAAC;MAC5DI,UAAU,CAAC+C,QAAQ,CAAChD,OAAO,IAAI,EAAE,CAAC;MAClCK,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAO6C,GAAG,EAAE;MACZC,OAAO,CAAC/C,KAAK,CAAC,uCAAuC,EAAE8C,GAAG,CAAC;MAC3D7C,QAAQ,CAAC,sCAAsC,CAAC;IAClD,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM2C,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMM,KAAK,GAAG,MAAM/D,cAAc,CAACgE,qBAAqB,CAACxD,UAAU,CAAC;MACpEwB,cAAc,CAAC+B,KAAK,CAAC;IACvB,CAAC,CAAC,OAAOF,GAAG,EAAE;MACZC,OAAO,CAAC/C,KAAK,CAAC,2CAA2C,EAAE8C,GAAG,CAAC;IACjE;EACF,CAAC;EAED,MAAMH,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFlB,sBAAsB,CAAC,IAAI,CAAC;MAC5BxB,QAAQ,CAAC,IAAI,CAAC;MACd,MAAMiD,IAAI,GAAG,MAAM/D,mBAAmB,CAACgE,uBAAuB,CAAC1D,UAAU,CAAC;MAC1E8B,eAAe,CAAC2B,IAAI,IAAI,EAAE,CAAC;;MAE3B;MACA,MAAME,0BAA0B,CAACF,IAAI,IAAI,EAAE,CAAC;IAC9C,CAAC,CAAC,OAAOJ,GAAG,EAAE;MACZC,OAAO,CAAC/C,KAAK,CAAC,0CAA0C,EAAE8C,GAAG,CAAC;MAC9D7C,QAAQ,CAAC,yCAAyC,CAAC;IACrD,CAAC,SAAS;MACRwB,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;EAED,MAAM2B,0BAA0B,GAAG,MAAOC,gBAAgB,IAAK;IAC7D,IAAI;MACF,MAAMC,UAAU,GAAG,CAAC,CAAC;MAErB,KAAK,MAAM1C,YAAY,IAAIyC,gBAAgB,EAAE;QAC3C,IAAI;UACF,MAAMzD,OAAO,GAAG,MAAMX,cAAc,CAACsE,wBAAwB,CAAC9D,UAAU,EAAEmB,YAAY,CAACwB,iBAAiB,CAAC;UACzGkB,UAAU,CAAC1C,YAAY,CAAC4C,eAAe,CAAC,GAAG5D,OAAO,IAAI,EAAE;QAC1D,CAAC,CAAC,OAAOkD,GAAG,EAAE;UACZC,OAAO,CAAC/C,KAAK,CAAC,sCAAsCY,YAAY,CAACwB,iBAAiB,GAAG,EAAEU,GAAG,CAAC;UAC3FQ,UAAU,CAAC1C,YAAY,CAAC4C,eAAe,CAAC,GAAG,EAAE;QAC/C;MACF;MAEA7B,yBAAyB,CAAC2B,UAAU,CAAC;IACvC,CAAC,CAAC,OAAOR,GAAG,EAAE;MACZC,OAAO,CAAC/C,KAAK,CAAC,uCAAuC,EAAE8C,GAAG,CAAC;IAC7D;EACF,CAAC;EAED,MAAMW,gBAAgB,GAAGA,CAACC,IAAI,EAAEC,OAAO,GAAG,IAAI,KAAK;IACjDpD,aAAa,CAACmD,IAAI,CAAC;IACnBrD,kBAAkB,CAACsD,OAAO,CAAC;IAE3B,IAAID,IAAI,KAAK,MAAM,IAAIC,OAAO,EAAE;MAC9BlD,WAAW,CAAC;QACVC,YAAY,EAAEiD,OAAO,CAACjD,YAAY;QAClCC,WAAW,EAAEgD,OAAO,CAAChD,WAAW,IAAI,EAAE;QACtCC,YAAY,EAAE+C,OAAO,CAAC/C,YAAY,IAAI,EAAE;QACxCC,aAAa,EAAE8C,OAAO,CAAC9C,aAAa,IAAI,EAAE;QAC1CC,QAAQ,EAAE6C,OAAO,CAAC7C,QAAQ,IAAI,SAAS;QACvCC,kBAAkB,EAAE4C,OAAO,CAAC5C,kBAAkB,IAAI;MACpD,CAAC,CAAC;IACJ;IAEAZ,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMyD,iBAAiB,GAAGA,CAAA,KAAM;IAC9BzD,aAAa,CAAC,KAAK,CAAC;IACpBE,kBAAkB,CAAC,IAAI,CAAC;IACxBc,mBAAmB,CAAC,EAAE,CAAC;IACvBV,WAAW,CAAC;MACVC,YAAY,EAAE,MAAM;MACpBC,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,EAAE;MACjBC,QAAQ,EAAE,SAAS;MACnBC,kBAAkB,EAAE;IACtB,CAAC,CAAC;EACJ,CAAC;EAED,MAAM8C,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,IAAIvD,UAAU,KAAK,MAAM,EAAE;QACzB;QACA,MAAMsC,QAAQ,GAAG,MAAM3D,cAAc,CAAC6E,aAAa,CAAC1D,eAAe,CAAC2D,cAAc,EAAEvD,QAAQ,CAAC;QAC7FuC,OAAO,CAACiB,GAAG,CAAC,qBAAqB,EAAEpB,QAAQ,CAAC;MAC9C,CAAC,MAAM,IAAItC,UAAU,KAAK,QAAQ,EAAE;QAClC;QACA,IAAI,CAACY,gBAAgB,CAAC+C,IAAI,CAAC,CAAC,EAAE;UAC5BhE,QAAQ,CAAC,6BAA6B,CAAC;UACvC;QACF;QAEA,MAAMiE,WAAW,GAAGhD,gBAAgB,CAACiD,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,EAAE,IAAIA,EAAE,CAACJ,IAAI,CAAC,CAAC,CAAC,CAACK,MAAM,CAACD,EAAE,IAAIA,EAAE,CAAC;QACrF,MAAMpF,cAAc,CAACsF,WAAW,CAACnE,eAAe,CAAC2D,cAAc,EAAEG,WAAW,CAAC;MAC/E;MAEAN,iBAAiB,CAAC,CAAC;MACnBnB,WAAW,CAAC,CAAC;MACbC,eAAe,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZC,OAAO,CAAC/C,KAAK,CAAC,yBAAyB,EAAE8C,GAAG,CAAC;MAC7C7C,QAAQ,CAAC,sCAAsC,CAAC;IAClD;EACF,CAAC;EAED,MAAMuE,YAAY,GAAG,MAAOC,aAAa,IAAK;IAC5C,IAAIC,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC,EAAE;MACnE,IAAI;QACF,MAAM1F,cAAc,CAAC2F,aAAa,CAACH,aAAa,CAAC;QACjDhC,WAAW,CAAC,CAAC;QACbC,eAAe,CAAC,CAAC;MACnB,CAAC,CAAC,OAAOI,GAAG,EAAE;QACZC,OAAO,CAAC/C,KAAK,CAAC,4BAA4B,EAAE8C,GAAG,CAAC;QAChD7C,QAAQ,CAAC,yCAAyC,CAAC;MACrD;IACF;EACF,CAAC;;EAED;EACA,MAAM4E,4BAA4B,GAAGA,CAACnB,IAAI,EAAE9C,YAAY,GAAG,IAAI,KAAK;IAClEmB,yBAAyB,CAAC2B,IAAI,CAAC;IAC/BzB,uBAAuB,CAACrB,YAAY,CAAC;IAErC,IAAI8C,IAAI,KAAK,MAAM,IAAI9C,YAAY,EAAE;MACnCuB,uBAAuB,CAAC;QACtBC,iBAAiB,EAAExB,YAAY,CAACwB,iBAAiB,IAAI,EAAE;QACvDC,KAAK,EAAEzB,YAAY,CAACyB,KAAK,IAAI,EAAE;QAC/BC,QAAQ,EAAE1B,YAAY,CAAC0B,QAAQ,IAAI;MACrC,CAAC,CAAC;IACJ,CAAC,MAAM;MACLH,uBAAuB,CAAC;QACtBC,iBAAiB,EAAE,EAAE;QACrBC,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;IAEAT,yBAAyB,CAAC,IAAI,CAAC;EACjC,CAAC;EAED,MAAMiD,6BAA6B,GAAGA,CAAA,KAAM;IAC1CjD,yBAAyB,CAAC,KAAK,CAAC;IAChCI,uBAAuB,CAAC,IAAI,CAAC;IAC7BhC,QAAQ,CAAC,IAAI,CAAC;EAChB,CAAC;EAED,MAAM8E,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C,IAAI;MACF9E,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,IAAI,CAACiC,oBAAoB,CAACE,iBAAiB,CAAC6B,IAAI,CAAC,CAAC,EAAE;QAClDhE,QAAQ,CAAC,yCAAyC,CAAC;QACnD;MACF;MAEA,IAAI,CAACiC,oBAAoB,CAACG,KAAK,IAAI,CAACH,oBAAoB,CAACI,QAAQ,EAAE;QACjErC,QAAQ,CAAC,yDAAyD,CAAC;QACnE;MACF;MAEA,IAAI6B,sBAAsB,KAAK,QAAQ,EAAE;QACvC,MAAM3C,mBAAmB,CAAC6F,kBAAkB,CAACvF,UAAU,EAAEyC,oBAAoB,CAAC;MAChF,CAAC,MAAM,IAAIJ,sBAAsB,KAAK,MAAM,EAAE;QAC5C,MAAM3C,mBAAmB,CAAC8F,kBAAkB,CAACjD,oBAAoB,CAACwB,eAAe,EAAEtB,oBAAoB,CAAC;MAC1G;MAEA4C,6BAA6B,CAAC,CAAC;MAC/B,MAAMnC,gBAAgB,CAAC,CAAC;IAC1B,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZC,OAAO,CAAC/C,KAAK,CAAC,yBAAyB,EAAE8C,GAAG,CAAC;MAC7C7C,QAAQ,CAAC6C,GAAG,CAACoC,MAAM,IAAI,yCAAyC,CAAC;IACnE;EACF,CAAC;EAED,MAAMC,wBAAwB,GAAG,MAAOC,cAAc,IAAK;IACzD,IAAI,CAACV,MAAM,CAACC,OAAO,CAAC,oDAAoD,CAAC,EAAE;MACzE;IACF;IAEA,IAAI;MACF,MAAMxF,mBAAmB,CAACkG,kBAAkB,CAACD,cAAc,CAAC;MAC5D,MAAMzC,gBAAgB,CAAC,CAAC;IAC1B,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZC,OAAO,CAAC/C,KAAK,CAAC,4BAA4B,EAAE8C,GAAG,CAAC;MAChD7C,QAAQ,CAAC,4CAA4C,CAAC;IACxD;EACF,CAAC;EAED,MAAMqF,aAAa,GAAIC,KAAK,IAAK;IAC/B,QAAQA,KAAK;MACX,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,WAAW;QAAE,OAAO,SAAS;MAClC,KAAK,UAAU;QAAE,OAAO,SAAS;MACjC,KAAK,YAAY;QAAE,OAAO,SAAS;MACnC,KAAK,WAAW;QAAE,OAAO,OAAO;MAChC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMC,mBAAmB,GAAIC,IAAI,IAAK;IACpC,QAAQA,IAAI;MACV,KAAK,MAAM;QAAE,OAAO,MAAM;MAC1B,KAAK,uBAAuB;QAAE,OAAO,uBAAuB;MAC5D,KAAK,qBAAqB;QAAE,OAAO,qBAAqB;MACxD,KAAK,gBAAgB;QAAE,OAAO,gBAAgB;MAC9C,KAAK,SAAS;QAAE,OAAO,wBAAwB;MAC/C;QAAS,OAAOA,IAAI;IACtB;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAI5E,QAAQ,IAAK;IACrC,QAAQA,QAAQ;MACd,KAAK,OAAO;QAAE,OAAO,SAAS;MAC9B,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,MAAM;QAAE,OAAO,SAAS;MAC7B,KAAK,SAAS;QAAE,OAAO,OAAO;MAC9B;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,IAAIhB,OAAO,EAAE;IACX,oBACET,OAAA,CAAC3D,GAAG;MAACiK,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,QAAQ;MAACC,UAAU,EAAC,QAAQ;MAACC,SAAS,EAAC,OAAO;MAAAC,QAAA,eAC/E1G,OAAA,CAACtC,gBAAgB;QAAAiJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC;EAEV;EAEA,oBACE9G,OAAA,CAAC3D,GAAG;IAAAqK,QAAA,gBAEF1G,OAAA,CAAC3D,GAAG;MAAC0K,EAAE,EAAE,CAAE;MAAAL,QAAA,EAER/E,WAAW,iBACV3B,OAAA,CAACpC,IAAI;QAACoJ,SAAS;QAACC,OAAO,EAAE,CAAE;QAACF,EAAE,EAAE,CAAE;QAAAL,QAAA,gBAChC1G,OAAA,CAACpC,IAAI;UAACsJ,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAX,QAAA,eAC9B1G,OAAA,CAAC1D,IAAI;YAAAoK,QAAA,eACH1G,OAAA,CAACzD,WAAW;cAAAmK,QAAA,gBACV1G,OAAA,CAACxD,UAAU;gBAAC8K,KAAK,EAAC,eAAe;gBAACC,YAAY;gBAAAb,QAAA,EAAC;cAE/C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb9G,OAAA,CAACxD,UAAU;gBAACgL,OAAO,EAAC,IAAI;gBAAAd,QAAA,EACrB/E,WAAW,CAAC8F;cAAc;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACP9G,OAAA,CAACpC,IAAI;UAACsJ,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAX,QAAA,eAC9B1G,OAAA,CAAC1D,IAAI;YAAAoK,QAAA,eACH1G,OAAA,CAACzD,WAAW;cAAAmK,QAAA,gBACV1G,OAAA,CAACxD,UAAU;gBAAC8K,KAAK,EAAC,eAAe;gBAACC,YAAY;gBAAAb,QAAA,EAAC;cAE/C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb9G,OAAA,CAACxD,UAAU;gBAACgL,OAAO,EAAC,IAAI;gBAAAd,QAAA,EACrB/E,WAAW,CAAC+F;cAAc;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACP9G,OAAA,CAACpC,IAAI;UAACsJ,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAX,QAAA,eAC9B1G,OAAA,CAAC1D,IAAI;YAAAoK,QAAA,eACH1G,OAAA,CAACzD,WAAW;cAAAmK,QAAA,gBACV1G,OAAA,CAACxD,UAAU;gBAAC8K,KAAK,EAAC,eAAe;gBAACC,YAAY;gBAAAb,QAAA,EAAC;cAE/C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb9G,OAAA,CAACxD,UAAU;gBAACgL,OAAO,EAAC,IAAI;gBAAAd,QAAA,EACrB/E,WAAW,CAACgG;cAAiB;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACP9G,OAAA,CAACpC,IAAI;UAACsJ,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAX,QAAA,eAC9B1G,OAAA,CAAC1D,IAAI;YAAAoK,QAAA,eACH1G,OAAA,CAACzD,WAAW;cAAAmK,QAAA,gBACV1G,OAAA,CAACxD,UAAU;gBAAC8K,KAAK,EAAC,eAAe;gBAACC,YAAY;gBAAAb,QAAA,EAAC;cAE/C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb9G,OAAA,CAACxD,UAAU;gBAACgL,OAAO,EAAC,IAAI;gBAAAd,QAAA,EACrB/E,WAAW,CAACiG;cAAgB;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACP9G,OAAA,CAACpC,IAAI;UAACsJ,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAX,QAAA,eAC9B1G,OAAA,CAAC1D,IAAI;YAAAoK,QAAA,eACH1G,OAAA,CAACzD,WAAW;cAAAmK,QAAA,gBACV1G,OAAA,CAACxD,UAAU;gBAAC8K,KAAK,EAAC,eAAe;gBAACC,YAAY;gBAAAb,QAAA,EAAC;cAE/C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb9G,OAAA,CAACxD,UAAU;gBAACgL,OAAO,EAAC,IAAI;gBAAAd,QAAA,EACrB/E,WAAW,CAACkG;cAAkB;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACP9G,OAAA,CAACpC,IAAI;UAACsJ,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAX,QAAA,eAC9B1G,OAAA,CAAC1D,IAAI;YAAAoK,QAAA,eACH1G,OAAA,CAACzD,WAAW;cAAAmK,QAAA,gBACV1G,OAAA,CAACxD,UAAU;gBAAC8K,KAAK,EAAC,eAAe;gBAACC,YAAY;gBAAAb,QAAA,EAAC;cAE/C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb9G,OAAA,CAACxD,UAAU;gBAACgL,OAAO,EAAC,IAAI;gBAAAd,QAAA,GACrB/E,WAAW,CAACmG,+BAA+B,CAACC,OAAO,CAAC,CAAC,CAAC,EAAC,GAC1D;cAAA;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACP;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGN9G,OAAA,CAAC3D,GAAG;MAACiK,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,eAAe;MAACC,UAAU,EAAC,QAAQ;MAACO,EAAE,EAAE,CAAE;MAACiB,QAAQ,EAAC,MAAM;MAACC,GAAG,EAAE,CAAE;MAAAvB,QAAA,gBACnG1G,OAAA,CAAC3D,GAAG;QAACiK,OAAO,EAAC,MAAM;QAAC2B,GAAG,EAAE,CAAE;QAACD,QAAQ,EAAC,MAAM;QAAAtB,QAAA,gBACzC1G,OAAA,CAACvD,MAAM;UACL+K,OAAO,EAAC,WAAW;UACnBU,SAAS,eAAElI,OAAA,CAAC9B,OAAO;YAAAyI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBqB,OAAO,EAAEA,CAAA,KAAMnG,kBAAkB,CAAC,IAAI,CAAE;UACxCsF,KAAK,EAAC,SAAS;UAAAZ,QAAA,EAChB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAIT9G,OAAA,CAACvD,MAAM;UACL+K,OAAO,EAAC,UAAU;UAClBU,SAAS,eAAElI,OAAA,CAACtB,UAAU;YAAAiI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1BqB,OAAO,EAAEA,CAAA,KAAM;YACb,IAAI5H,OAAO,CAAC6H,MAAM,KAAK,CAAC,EAAE;cACxBxH,QAAQ,CAAC,iDAAiD,CAAC;cAC3D;YACF;YACA;YACAA,QAAQ,CAAC,yEAAyE,CAAC;UACrF,CAAE;UACFyH,QAAQ,EAAE9H,OAAO,CAAC6H,MAAM,KAAK,CAAE;UAAA1B,QAAA,EAChC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN9G,OAAA,CAACvD,MAAM;QACL+K,OAAO,EAAC,UAAU;QAClBU,SAAS,eAAElI,OAAA,CAACpB,WAAW;UAAA+H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC3BqB,OAAO,EAAEA,CAAA,KAAM;UACb/E,WAAW,CAAC,CAAC;UACbC,eAAe,CAAC,CAAC;QACnB,CAAE;QAAAqD,QAAA,EACH;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAGLnG,KAAK,iBACJX,OAAA,CAACvC,KAAK;MAAC6K,QAAQ,EAAC,OAAO;MAACC,EAAE,EAAE;QAAExB,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,EACnC/F;IAAK;MAAAgG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,eAGD9G,OAAA,CAACnD,cAAc;MAAC2L,SAAS,EAAExL,KAAM;MAAA0J,QAAA,eAC/B1G,OAAA,CAACtD,KAAK;QAAAgK,QAAA,gBACJ1G,OAAA,CAAClD,SAAS;UAAA4J,QAAA,eACR1G,OAAA,CAACjD,QAAQ;YAAA2J,QAAA,gBACP1G,OAAA,CAACpD,SAAS;cAAA8J,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC7B9G,OAAA,CAACpD,SAAS;cAAA8J,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC3B9G,OAAA,CAACpD,SAAS;cAAA8J,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC/B9G,OAAA,CAACpD,SAAS;cAAA8J,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACnC9G,OAAA,CAACpD,SAAS;cAAA8J,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACrC9G,OAAA,CAACpD,SAAS;cAAA8J,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC5B9G,OAAA,CAACpD,SAAS;cAAA8J,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACrC9G,OAAA,CAACpD,SAAS;cAAA8J,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACpC9G,OAAA,CAACpD,SAAS;cAAA8J,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZ9G,OAAA,CAACrD,SAAS;UAAA+J,QAAA,EACPnG,OAAO,CAACwE,GAAG,CAAET,OAAO,iBACnBtE,OAAA,CAACjD,QAAQ;YAAA2J,QAAA,gBACP1G,OAAA,CAACpD,SAAS;cAAA8J,QAAA,eACR1G,OAAA,CAACxD,UAAU;gBAACgL,OAAO,EAAC,OAAO;gBAACiB,UAAU,EAAC,MAAM;gBAAA/B,QAAA,EAC1CpC,OAAO,CAACI;cAAc;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACZ9G,OAAA,CAACpD,SAAS;cAAA8J,QAAA,eACR1G,OAAA,CAAC/C,IAAI;gBACHyL,KAAK,EAAEvC,mBAAmB,CAAC7B,OAAO,CAACjD,YAAY,CAAE;gBACjDsH,IAAI,EAAC,OAAO;gBACZnB,OAAO,EAAC;cAAU;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eACZ9G,OAAA,CAACpD,SAAS;cAAA8J,QAAA,eACR1G,OAAA,CAAC/C,IAAI;gBACHyL,KAAK,EAAEpE,OAAO,CAAC7C,QAAQ,IAAI,SAAU;gBACrCkH,IAAI,EAAC,OAAO;gBACZrB,KAAK,EAAEjB,gBAAgB,CAAC/B,OAAO,CAAC7C,QAAQ,IAAI,SAAS;cAAE;gBAAAkF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eACZ9G,OAAA,CAACpD,SAAS;cAAA8J,QAAA,EAAEpC,OAAO,CAAC/C;YAAY;cAAAoF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC7C9G,OAAA,CAACpD,SAAS;cAAA8J,QAAA,EACP,IAAIkC,IAAI,CAACtE,OAAO,CAACuE,cAAc,CAAC,CAACC,kBAAkB,CAAC,OAAO;YAAC;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eACZ9G,OAAA,CAACpD,SAAS;cAAA8J,QAAA,eACR1G,OAAA,CAAC/C,IAAI;gBACHyL,KAAK,EAAEpE,OAAO,CAAC4B,KAAM;gBACrBoB,KAAK,EAAErB,aAAa,CAAC3B,OAAO,CAAC4B,KAAK,CAAE;gBACpCyC,IAAI,EAAC;cAAO;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eACZ9G,OAAA,CAACpD,SAAS;cAAA8J,QAAA,EAAEpC,OAAO,CAACyE,qBAAqB,IAAI;YAAC;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC3D9G,OAAA,CAACpD,SAAS;cAAA8J,QAAA,EACPpC,OAAO,CAAC0E,yBAAyB,GAChC,GAAG1E,OAAO,CAAC0E,yBAAyB,CAACjB,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;YAAI;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eACZ9G,OAAA,CAACpD,SAAS;cAAA8J,QAAA,gBACR1G,OAAA,CAACrC,OAAO;gBAACsL,KAAK,EAAC,YAAY;gBAAAvC,QAAA,eACzB1G,OAAA,CAAC9C,UAAU;kBACTyL,IAAI,EAAC,OAAO;kBACZR,OAAO,EAAEA,CAAA,KAAM/D,gBAAgB,CAAC,MAAM,EAAEE,OAAO,CAAE;kBAAAoC,QAAA,eAEjD1G,OAAA,CAACxB,QAAQ;oBAAAmI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACV9G,OAAA,CAACrC,OAAO;gBAACsL,KAAK,EAAC,UAAU;gBAAAvC,QAAA,eACvB1G,OAAA,CAAC9C,UAAU;kBACTyL,IAAI,EAAC,OAAO;kBACZR,OAAO,EAAEA,CAAA,KAAM/D,gBAAgB,CAAC,MAAM,EAAEE,OAAO,CAAE;kBAAAoC,QAAA,eAEjD1G,OAAA,CAAC5B,QAAQ;oBAAAuI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACV9G,OAAA,CAACrC,OAAO;gBAACsL,KAAK,EAAC,cAAc;gBAAAvC,QAAA,eAC3B1G,OAAA,CAAC9C,UAAU;kBACTyL,IAAI,EAAC,OAAO;kBACZR,OAAO,EAAEA,CAAA,KAAM/D,gBAAgB,CAAC,QAAQ,EAAEE,OAAO,CAAE;kBACnDgD,KAAK,EAAC,SAAS;kBAAAZ,QAAA,eAEf1G,OAAA,CAACtB,UAAU;oBAAAiI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACV9G,OAAA,CAACrC,OAAO;gBAACsL,KAAK,EAAC,SAAS;gBAAAvC,QAAA,eACtB1G,OAAA,CAAC9C,UAAU;kBACTyL,IAAI,EAAC,OAAO;kBACZR,OAAO,EAAEA,CAAA,KAAMhD,YAAY,CAACb,OAAO,CAACI,cAAc,CAAE;kBACpD4C,KAAK,EAAC,OAAO;kBAAAZ,QAAA,eAEb1G,OAAA,CAAC1B,UAAU;oBAAAqI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA,GAvECxC,OAAO,CAACI,cAAc;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwE3B,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,EAEhBvG,OAAO,CAAC6H,MAAM,KAAK,CAAC,IAAI,CAAC3H,OAAO,iBAC/BT,OAAA,CAAC3D,GAAG;MAAC6M,SAAS,EAAC,QAAQ;MAACC,EAAE,EAAE,CAAE;MAAAzC,QAAA,gBAC5B1G,OAAA,CAACxD,UAAU;QAACgL,OAAO,EAAC,IAAI;QAACF,KAAK,EAAC,eAAe;QAAAZ,QAAA,EAAC;MAE/C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb9G,OAAA,CAACxD,UAAU;QAACgL,OAAO,EAAC,OAAO;QAACF,KAAK,EAAC,eAAe;QAAAZ,QAAA,EAAC;MAElD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN,eAGD9G,OAAA,CAAC7C,MAAM;MAACiM,IAAI,EAAEvI,UAAW;MAACwI,OAAO,EAAE9E,iBAAkB;MAAC+E,QAAQ,EAAC,IAAI;MAACC,SAAS;MAAA7C,QAAA,gBAC3E1G,OAAA,CAAC5C,WAAW;QAAAsJ,QAAA,GACTzF,UAAU,KAAK,MAAM,IAAI,kBAAkB,EAC3CA,UAAU,KAAK,MAAM,IAAI,kBAAkB,EAC3CA,UAAU,KAAK,QAAQ,IAAI,kBAAkBF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE2D,cAAc,EAAE;MAAA;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpE,CAAC,eACd9G,OAAA,CAAC3C,aAAa;QAAAqJ,QAAA,eACZ1G,OAAA,CAAC3D,GAAG;UAACkM,EAAE,EAAE;YAAEiB,EAAE,EAAE;UAAE,CAAE;UAAA9C,QAAA,EAChBzF,UAAU,KAAK,QAAQ,gBACtBjB,OAAA,CAAAE,SAAA;YAAAwG,QAAA,gBACE1G,OAAA,CAACvC,KAAK;cAAC6K,QAAQ,EAAC,MAAM;cAACC,EAAE,EAAE;gBAAExB,EAAE,EAAE;cAAE,CAAE;cAAAL,QAAA,EAAC;YAEtC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR9G,OAAA,CAACzC,SAAS;cACRgM,SAAS;cACTb,KAAK,EAAC,+BAA+B;cACrCe,KAAK,EAAE5H,gBAAiB;cACxB6H,QAAQ,EAAGC,CAAC,IAAK7H,mBAAmB,CAAC6H,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cACrDI,MAAM,EAAC,QAAQ;cACfC,WAAW,EAAC,+BAA+B;cAC3CC,UAAU,EAAC,oCAAoC;cAC/CC,SAAS;cACTC,IAAI,EAAE;YAAE;cAAAtD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA,eACF,CAAC,GACD7F,UAAU,KAAK,MAAM,IAAIF,eAAe,gBAC1Cf,OAAA,CAAAE,SAAA;YAAAwG,QAAA,eACE1G,OAAA,CAACnC,IAAI;cAAA6I,QAAA,gBACH1G,OAAA,CAAClC,QAAQ;gBAAA4I,QAAA,eACP1G,OAAA,CAACjC,YAAY;kBACXmM,OAAO,EAAC,gBAAgB;kBACxBC,SAAS,EAAEpJ,eAAe,CAAC2D;gBAAe;kBAAAiC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC,eACX9G,OAAA,CAAChC,OAAO;gBAAA2I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACX9G,OAAA,CAAClC,QAAQ;gBAAA4I,QAAA,eACP1G,OAAA,CAACjC,YAAY;kBACXmM,OAAO,EAAC,MAAM;kBACdC,SAAS,EAAEhE,mBAAmB,CAACpF,eAAe,CAACM,YAAY;gBAAE;kBAAAsF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC,eACX9G,OAAA,CAAChC,OAAO;gBAAA2I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACX9G,OAAA,CAAClC,QAAQ;gBAAA4I,QAAA,eACP1G,OAAA,CAACjC,YAAY;kBACXmM,OAAO,EAAC,OAAO;kBACfC,SAAS,eACPnK,OAAA,CAAC/C,IAAI;oBACHyL,KAAK,EAAE3H,eAAe,CAACmF,KAAM;oBAC7BoB,KAAK,EAAErB,aAAa,CAAClF,eAAe,CAACmF,KAAK,CAAE;oBAC5CyC,IAAI,EAAC;kBAAO;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBACF;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC,eACX9G,OAAA,CAAChC,OAAO;gBAAA2I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACX9G,OAAA,CAAClC,QAAQ;gBAAA4I,QAAA,eACP1G,OAAA,CAACjC,YAAY;kBACXmM,OAAO,EAAC,aAAa;kBACrBC,SAAS,EAAEpJ,eAAe,CAACO,WAAW,IAAI;gBAAsB;kBAAAqF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC,eACX9G,OAAA,CAAChC,OAAO;gBAAA2I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACX9G,OAAA,CAAClC,QAAQ;gBAAA4I,QAAA,eACP1G,OAAA,CAACjC,YAAY;kBACXmM,OAAO,EAAC,aAAU;kBAClBC,SAAS,eACPnK,OAAA,CAAC/C,IAAI;oBACHyL,KAAK,EAAE3H,eAAe,CAACU,QAAQ,IAAI,SAAU;oBAC7C6F,KAAK,EAAEjB,gBAAgB,CAACtF,eAAe,CAACU,QAAQ,IAAI,SAAS,CAAE;oBAC/DkH,IAAI,EAAC;kBAAO;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBACF;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC,eACX9G,OAAA,CAAChC,OAAO;gBAAA2I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACX9G,OAAA,CAAClC,QAAQ;gBAAA4I,QAAA,eACP1G,OAAA,CAACjC,YAAY;kBACXmM,OAAO,EAAC,cAAc;kBACtBC,SAAS,EAAEpJ,eAAe,CAACQ,YAAY,IAAI;gBAAgB;kBAAAoF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC,EACV/F,eAAe,CAACW,kBAAkB,iBACjC1B,OAAA,CAAAE,SAAA;gBAAAwG,QAAA,gBACE1G,OAAA,CAAChC,OAAO;kBAAA2I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACX9G,OAAA,CAAClC,QAAQ;kBAAA4I,QAAA,eACP1G,OAAA,CAACjC,YAAY;oBACXmM,OAAO,EAAC,oBAAoB;oBAC5BC,SAAS,EAAEpJ,eAAe,CAACW;kBAAmB;oBAAAiF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CAAC;cAAA,eACX,CACH,eACD9G,OAAA,CAAChC,OAAO;gBAAA2I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACX9G,OAAA,CAAClC,QAAQ;gBAAA4I,QAAA,eACP1G,OAAA,CAACjC,YAAY;kBACXmM,OAAO,EAAC,gBAAgB;kBACxBC,SAAS,EAAE,IAAIvB,IAAI,CAAC7H,eAAe,CAAC8H,cAAc,CAAC,CAACC,kBAAkB,CAAC,OAAO;gBAAE;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC,EACV/F,eAAe,CAACS,aAAa,iBAC5BxB,OAAA,CAAAE,SAAA;gBAAAwG,QAAA,gBACE1G,OAAA,CAAChC,OAAO;kBAAA2I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACX9G,OAAA,CAAClC,QAAQ;kBAAA4I,QAAA,eACP1G,OAAA,CAACjC,YAAY;oBACXmM,OAAO,EAAC,eAAe;oBACvBC,SAAS,EAAE,IAAIvB,IAAI,CAAC7H,eAAe,CAACS,aAAa,CAAC,CAACsH,kBAAkB,CAAC,OAAO;kBAAE;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CAAC;cAAA,eACX,CACH,eACD9G,OAAA,CAAChC,OAAO;gBAAA2I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACX9G,OAAA,CAAClC,QAAQ;gBAAA4I,QAAA,eACP1G,OAAA,CAACjC,YAAY;kBACXmM,OAAO,EAAC,gBAAgB;kBACxBC,SAAS,EAAEpJ,eAAe,CAACgI,qBAAqB,IAAI;gBAAE;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC,eACX9G,OAAA,CAAChC,OAAO;gBAAA2I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACX9G,OAAA,CAAClC,QAAQ;gBAAA4I,QAAA,eACP1G,OAAA,CAACjC,YAAY;kBACXmM,OAAO,EAAC,eAAe;kBACvBC,SAAS,EAAE,GAAG,CAACpJ,eAAe,CAACiI,yBAAyB,IAAI,CAAC,EAAEjB,OAAO,CAAC,CAAC,CAAC;gBAAI;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9E;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP;UAAC,gBACP,CAAC,gBAEH9G,OAAA,CAAAE,SAAA;YAAAwG,QAAA,gBACE1G,OAAA,CAACzC,SAAS;cACRgM,SAAS;cACTa,MAAM;cACN1B,KAAK,EAAC,cAAc;cACpBe,KAAK,EAAEtI,QAAQ,CAACE,YAAa;cAC7BqI,QAAQ,EAAGC,CAAC,IAAKvI,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEE,YAAY,EAAEsI,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAC5EI,MAAM,EAAC,QAAQ;cACfxB,QAAQ,EAAEpH,UAAU,KAAK,MAAO;cAAAyF,QAAA,gBAEhC1G,OAAA,CAACxC,QAAQ;gBAACiM,KAAK,EAAC,MAAM;gBAAA/C,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACtC9G,OAAA,CAACxC,QAAQ;gBAACiM,KAAK,EAAC,uBAAuB;gBAAA/C,QAAA,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACxE9G,OAAA,CAACxC,QAAQ;gBAACiM,KAAK,EAAC,qBAAqB;gBAAA/C,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACpE9G,OAAA,CAACxC,QAAQ;gBAACiM,KAAK,EAAC,gBAAgB;gBAAA/C,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC1D9G,OAAA,CAACxC,QAAQ;gBAACiM,KAAK,EAAC,SAAS;gBAAA/C,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eAEZ9G,OAAA,CAACzC,SAAS;cACRgM,SAAS;cACTa,MAAM;cACN1B,KAAK,EAAC,aAAU;cAChBe,KAAK,EAAEtI,QAAQ,CAACM,QAAS;cACzBiI,QAAQ,EAAGC,CAAC,IAAKvI,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEM,QAAQ,EAAEkI,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cACxEI,MAAM,EAAC,QAAQ;cACfxB,QAAQ,EAAEpH,UAAU,KAAK,MAAO;cAAAyF,QAAA,gBAEhC1G,OAAA,CAACxC,QAAQ;gBAACiM,KAAK,EAAC,OAAO;gBAAA/C,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACxC9G,OAAA,CAACxC,QAAQ;gBAACiM,KAAK,EAAC,SAAS;gBAAA/C,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC5C9G,OAAA,CAACxC,QAAQ;gBAACiM,KAAK,EAAC,MAAM;gBAAA/C,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACtC9G,OAAA,CAACxC,QAAQ;gBAACiM,KAAK,EAAC,SAAS;gBAAA/C,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eAEZ9G,OAAA,CAACzC,SAAS;cACRgM,SAAS;cACTb,KAAK,EAAC,aAAa;cACnBe,KAAK,EAAEtI,QAAQ,CAACG,WAAY;cAC5BoI,QAAQ,EAAGC,CAAC,IAAKvI,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEG,WAAW,EAAEqI,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAC3EI,MAAM,EAAC,QAAQ;cACfG,SAAS;cACTC,IAAI,EAAE,CAAE;cACR5B,QAAQ,EAAEpH,UAAU,KAAK;YAAO;cAAA0F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,eAEF9G,OAAA,CAACzC,SAAS;cACRgM,SAAS;cACTb,KAAK,EAAC,cAAc;cACpBe,KAAK,EAAEtI,QAAQ,CAACI,YAAa;cAC7BmI,QAAQ,EAAGC,CAAC,IAAKvI,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEI,YAAY,EAAEoI,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAC5EI,MAAM,EAAC,QAAQ;cACfxB,QAAQ,EAAEpH,UAAU,KAAK,MAAO;cAChCoJ,QAAQ;cACRN,UAAU,EAAC;YAAuC;cAAApD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,eAEF9G,OAAA,CAACzC,SAAS;cACRgM,SAAS;cACTb,KAAK,EAAC,oBAAoB;cAC1Be,KAAK,EAAEtI,QAAQ,CAACO,kBAAmB;cACnCgI,QAAQ,EAAGC,CAAC,IAAKvI,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEO,kBAAkB,EAAEiI,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAClFI,MAAM,EAAC,QAAQ;cACfG,SAAS;cACTC,IAAI,EAAE,CAAE;cACR5B,QAAQ,EAAEpH,UAAU,KAAK,MAAO;cAChC8I,UAAU,EAAC;YAA2C;cAAApD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC,eAEF9G,OAAA,CAACzC,SAAS;cACRgM,SAAS;cACTb,KAAK,EAAC,eAAe;cACrB4B,IAAI,EAAC,MAAM;cACXb,KAAK,EAAEtI,QAAQ,CAACK,aAAc;cAC9BkI,QAAQ,EAAGC,CAAC,IAAKvI,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEK,aAAa,EAAEmI,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAC7EI,MAAM,EAAC,QAAQ;cACfU,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK,CAAE;cAClCnC,QAAQ,EAAEpH,UAAU,KAAK;YAAO;cAAA0F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC;UAAA,eACF;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChB9G,OAAA,CAAC1C,aAAa;QAAAoJ,QAAA,gBACZ1G,OAAA,CAACvD,MAAM;UAAC0L,OAAO,EAAE5D,iBAAkB;UAAAmC,QAAA,EAChCzF,UAAU,KAAK,MAAM,GAAG,QAAQ,GAAG;QAAS;UAAA0F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,EACR7F,UAAU,KAAK,MAAM,iBACpBjB,OAAA,CAACvD,MAAM;UAAC0L,OAAO,EAAE3D,YAAa;UAACgD,OAAO,EAAC,WAAW;UAAAd,QAAA,EAC/CzF,UAAU,KAAK,MAAM,GAAG,OAAO,GAC/BA,UAAU,KAAK,QAAQ,GAAG,cAAc,GAAG;QAAO;UAAA0F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT9G,OAAA,CAACH,kBAAkB;MACjBO,UAAU,EAAEA,UAAW;MACvBgJ,IAAI,EAAErH,eAAgB;MACtBsH,OAAO,EAAEA,CAAA,KAAMrH,kBAAkB,CAAC,KAAK,CAAE;MACzCyI,SAAS,EAAEA,CAAA,KAAM;QACfrH,WAAW,CAAC,CAAC;QACbC,eAAe,CAAC,CAAC;QACjBrB,kBAAkB,CAAC,KAAK,CAAC;MAC3B;IAAE;MAAA2E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGC,CAAC;AAEV,CAAC;AAACxG,EAAA,CA1vBIH,WAAW;AAAAuK,EAAA,GAAXvK,WAAW;AA4vBjB,eAAeA,WAAW;AAAC,IAAAuK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}