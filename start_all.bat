@echo off
echo ============================================================
echo 🚀 CABLYS - Cable Installation Advance System
echo    Sistema Completo di Gestione Cantieri
echo ============================================================
echo.

echo 🔍 Avvio sistema completo...
echo.

REM Avvia backend in una nuova finestra
echo 🔧 Avvio backend FastAPI...
start "CABLYS Backend" cmd /k "cd webapp && python backend/main.py --port 8002"

REM Attendi 3 secondi
timeout /t 3 /nobreak >nul

REM Avvia frontend in una nuova finestra
echo ⚛️ Avvio frontend React...
start "CABLYS Frontend" cmd /k "cd webapp/frontend && npm start"

REM Attendi 2 secondi
timeout /t 2 /nobreak >nul

REM Avvia simulatore mobile in una nuova finestra
echo 📱 Avvio simulatore mobile...
start "CABLYS Mobile" cmd /k "cd mobile_simulator && python server.py"

REM Attendi 3 secondi
timeout /t 3 /nobreak >nul

echo.
echo ============================================================
echo 🎉 CABLYS Sistema Avviato!
echo ============================================================
echo.
echo 📋 Servizi Attivi:
echo    🔧 Backend API:      http://localhost:8002
echo    ⚛️ Frontend CMS:     http://localhost:3000
echo    📱 App Mobile:       http://localhost:3001
echo    📚 API Docs:         http://localhost:8002/docs
echo.
echo 🧪 Test Workflow:
echo    1. Accedi al CMS:    http://localhost:3000/login
echo    2. Crea una comanda con responsabile + email/telefono
echo    3. Usa il codice comanda nel simulatore mobile
echo    4. Gestisci i lavori da mobile
echo.
echo ⚠️ Per fermare i servizi: chiudi le finestre dei terminali
echo ============================================================

REM Apri i browser automaticamente
echo 🌐 Apertura browser...
timeout /t 2 /nobreak >nul

start http://localhost:3000
timeout /t 1 /nobreak >nul
start http://localhost:3001
timeout /t 1 /nobreak >nul
start http://localhost:8002/docs

echo.
echo ✅ Sistema completamente avviato!
echo 👋 Premi un tasto per chiudere questa finestra...
pause >nul
