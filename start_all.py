#!/usr/bin/env python3
"""
Script per avviare tutti i servizi CABLYS contemporaneamente.
Avvia backend, frontend e simulatore mobile in processi separati.
"""

import subprocess
import sys
import time
import os
import webbrowser
from pathlib import Path

def print_banner():
    """Stampa il banner di avvio CABLYS."""
    print("=" * 60)
    print("🚀 CABLYS - Cable Installation Advance System")
    print("   Sistema Completo di Gestione Cantieri")
    print("=" * 60)
    print()

def check_requirements():
    """Verifica che tutti i requisiti siano soddisfatti."""
    print("🔍 Verifica requisiti...")
    
    # Verifica Python
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ richiesto")
        return False
    
    # Verifica directory
    required_dirs = [
        "webapp/backend",
        "webapp/frontend", 
        "mobile_simulator"
    ]
    
    for dir_path in required_dirs:
        if not Path(dir_path).exists():
            print(f"❌ Directory mancante: {dir_path}")
            return False
    
    print("✅ Requisiti soddisfatti")
    return True

def start_backend():
    """Avvia il backend FastAPI."""
    print("🔧 Avvio backend FastAPI...")
    
    try:
        # Cambia directory e avvia backend
        backend_cmd = [
            sys.executable, "backend/main.py", "--port", "8002"
        ]
        
        process = subprocess.Popen(
            backend_cmd,
            cwd="webapp",
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Attendi che il backend si avvii
        time.sleep(3)
        
        if process.poll() is None:
            print("✅ Backend avviato su http://localhost:8002")
            return process
        else:
            print("❌ Errore nell'avvio del backend")
            return None
            
    except Exception as e:
        print(f"❌ Errore backend: {e}")
        return None

def start_frontend():
    """Avvia il frontend React."""
    print("⚛️  Avvio frontend React...")
    
    try:
        # Verifica se node_modules esiste
        if not Path("webapp/frontend/node_modules").exists():
            print("📦 Installazione dipendenze npm...")
            npm_install = subprocess.run(
                ["npm", "install"],
                cwd="webapp/frontend",
                capture_output=True,
                text=True
            )
            
            if npm_install.returncode != 0:
                print("❌ Errore installazione npm")
                return None
        
        # Avvia frontend
        frontend_cmd = ["npm", "start"]
        
        process = subprocess.Popen(
            frontend_cmd,
            cwd="webapp/frontend",
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Attendi che il frontend si avvii
        time.sleep(5)
        
        if process.poll() is None:
            print("✅ Frontend avviato su http://localhost:3000")
            return process
        else:
            print("❌ Errore nell'avvio del frontend")
            return None
            
    except Exception as e:
        print(f"❌ Errore frontend: {e}")
        return None

def start_mobile_simulator():
    """Avvia il simulatore mobile."""
    print("📱 Avvio simulatore mobile...")
    
    try:
        mobile_cmd = [sys.executable, "server.py"]
        
        process = subprocess.Popen(
            mobile_cmd,
            cwd="mobile_simulator",
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Attendi che il simulatore si avvii
        time.sleep(2)
        
        if process.poll() is None:
            print("✅ Simulatore mobile avviato su http://localhost:3001")
            return process
        else:
            print("❌ Errore nell'avvio del simulatore mobile")
            return None
            
    except Exception as e:
        print(f"❌ Errore simulatore mobile: {e}")
        return None

def open_browsers():
    """Apre i browser con tutti i servizi."""
    print("🌐 Apertura browser...")
    
    urls = [
        "http://localhost:3000",  # Frontend React
        "http://localhost:3001",  # Simulatore Mobile
        "http://localhost:8002/docs",  # API Docs
    ]
    
    for url in urls:
        try:
            webbrowser.open(url)
            time.sleep(1)
        except:
            print(f"⚠️ Impossibile aprire {url}")

def print_status():
    """Stampa lo stato dei servizi."""
    print("\n" + "=" * 60)
    print("🎉 CABLYS Sistema Avviato Completamente!")
    print("=" * 60)
    print()
    print("📋 Servizi Attivi:")
    print("   🔧 Backend API:      http://localhost:8002")
    print("   ⚛️  Frontend CMS:     http://localhost:3000")
    print("   📱 App Mobile:       http://localhost:3001")
    print("   📚 API Docs:         http://localhost:8002/docs")
    print()
    print("🧪 Test Workflow:")
    print("   1. Accedi al CMS:    http://localhost:3000/login")
    print("   2. Crea una comanda con responsabile + email/telefono")
    print("   3. Usa il codice comanda nel simulatore mobile")
    print("   4. Gestisci i lavori da mobile")
    print()
    print("⚠️  Per fermare tutti i servizi: Ctrl+C")
    print("=" * 60)

def main():
    """Funzione principale."""
    print_banner()
    
    # Verifica requisiti
    if not check_requirements():
        sys.exit(1)
    
    processes = []
    
    try:
        # Avvia backend
        backend_process = start_backend()
        if backend_process:
            processes.append(backend_process)
        
        # Avvia frontend
        frontend_process = start_frontend()
        if frontend_process:
            processes.append(frontend_process)
        
        # Avvia simulatore mobile
        mobile_process = start_mobile_simulator()
        if mobile_process:
            processes.append(mobile_process)
        
        # Verifica che almeno un servizio sia attivo
        if not processes:
            print("❌ Nessun servizio avviato con successo")
            sys.exit(1)
        
        # Apri browser
        time.sleep(2)
        open_browsers()
        
        # Stampa stato
        print_status()
        
        # Mantieni i processi attivi
        try:
            while True:
                time.sleep(1)
                # Verifica che i processi siano ancora attivi
                active_processes = [p for p in processes if p.poll() is None]
                if not active_processes:
                    print("\n⚠️ Tutti i processi sono terminati")
                    break
        except KeyboardInterrupt:
            print("\n\n🛑 Arresto servizi in corso...")
    
    except Exception as e:
        print(f"\n❌ Errore generale: {e}")
    
    finally:
        # Termina tutti i processi
        for process in processes:
            try:
                process.terminate()
                process.wait(timeout=5)
            except:
                try:
                    process.kill()
                except:
                    pass
        
        print("✅ Tutti i servizi sono stati fermati")
        print("👋 Grazie per aver usato CABLYS!")

if __name__ == "__main__":
    main()
