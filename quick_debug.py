#!/usr/bin/env python3
"""
Quick debug per verificare responsabili e comande.
"""

import psycopg2
import psycopg2.extras

def quick_debug():
    """Debug veloce."""
    
    try:
        # Connessione diretta al database
        conn = psycopg2.connect(
            host="localhost",
            database="cantieri",
            user="postgres",
            password="Taranto",
            cursor_factory=psycopg2.extras.RealDictCursor
        )
        
        cursor = conn.cursor()
        
        print("🔍 QUICK DEBUG")
        print("=" * 30)
        
        # 1. Responsabili per cantiere
        print("\n📋 1. RESPONSABILI:")
        cursor.execute("""
            SELECT id_cantiere, COUNT(*) as count
            FROM responsabili
            WHERE attivo = TRUE
            GROUP BY id_cantiere
            ORDER BY id_cantiere;
        """)
        
        responsabili_data = cursor.fetchall()
        for row in responsabili_data:
            print(f"  Cantiere {row['id_cantiere']}: {row['count']} responsabili")
        
        # 2. Comande per cantiere
        print("\n📋 2. COMANDE:")
        cursor.execute("""
            SELECT id_cantiere, stato, COUNT(*) as count
            FROM comande
            GROUP BY id_cantiere, stato
            ORDER BY id_cantiere, stato;
        """)
        
        comande_data = cursor.fetchall()
        for row in comande_data:
            print(f"  Cantiere {row['id_cantiere']}: {row['count']} comande {row['stato']}")
        
        # 3. Cavi assegnati alle comande specifiche
        print("\n📋 3. CAVI COMANDE POS007/POS008:")
        cursor.execute("""
            SELECT 
                c.codice_comanda,
                COUNT(cv.id_cavo) as cavi_assegnati
            FROM comande c
            LEFT JOIN cavi cv ON (
                (c.tipo_comanda = 'POSA' AND cv.comanda_posa = c.codice_comanda) OR
                (c.tipo_comanda = 'COLLEGAMENTO_PARTENZA' AND cv.comanda_partenza = c.codice_comanda) OR
                (c.tipo_comanda = 'COLLEGAMENTO_ARRIVO' AND cv.comanda_arrivo = c.codice_comanda) OR
                (c.tipo_comanda = 'CERTIFICAZIONE' AND cv.comanda_certificazione = c.codice_comanda)
            )
            WHERE c.codice_comanda IN ('POS007', 'POS008')
            GROUP BY c.codice_comanda
            ORDER BY c.codice_comanda;
        """)
        
        cavi_data = cursor.fetchall()
        for row in cavi_data:
            print(f"  {row['codice_comanda']}: {row['cavi_assegnati']} cavi")
        
        conn.close()
        print("\n✅ Debug completato!")
        
    except Exception as e:
        print(f"❌ Errore: {e}")

if __name__ == "__main__":
    quick_debug()
