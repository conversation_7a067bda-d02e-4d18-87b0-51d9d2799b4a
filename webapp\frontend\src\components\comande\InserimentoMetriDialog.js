import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  Typography,
  Box,
  Alert,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  Autocomplete,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  ListItemText,
  ListItemIcon
} from '@mui/material';
import {
  Save as SaveIcon,
  Cancel as CancelIcon,
  Cable as CableIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Storage as BobinaIcon,
  Error as ErrorIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import comandeService from '../../services/comandeService';
import caviService from '../../services/caviService';

const InserimentoMetriDialog = ({
  open,
  onClose,
  comanda,
  onSuccess
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [cavi, setCavi] = useState([]);
  const [datiPosa, setDatiPosa] = useState({});
  const [validationErrors, setValidationErrors] = useState({});
  const [bobineDisponibili, setBobineDisponibili] = useState([]);
  const [loadingBobine, setLoadingBobine] = useState(false);

  useEffect(() => {
    if (open && comanda) {
      loadCaviComanda();
      loadBobineDisponibili();
    }
  }, [open, comanda]);

  const loadCaviComanda = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const caviData = await comandeService.getCaviComanda(comanda.codice_comanda);
      setCavi(caviData);
      
      // Inizializza i dati di posa per ogni cavo
      const initialDati = {};
      caviData.forEach(cavo => {
        initialDati[cavo.id_cavo] = {
          // Per comande POSA: metri reali devono essere 0 (azzerati)
          // Per altre comande: mantieni il valore esistente
          metratura_reale: comanda.tipo_comanda === 'POSA' ? 0 : (cavo.metratura_reale || 0),
          data_posa: new Date().toISOString().split('T')[0],
          responsabile_posa: comanda.responsabile || '',
          note: '',
          id_bobina: cavo.id_bobina || '', // Bobina attualmente associata
          force_over: false // Flag per forzare associazione anche se metri insufficienti
        };
      });
      setDatiPosa(initialDati);
      
    } catch (err) {
      console.error('Errore nel caricamento cavi:', err);
      setError('Errore nel caricamento dei cavi della comanda');
    } finally {
      setLoading(false);
    }
  };

  const loadBobineDisponibili = async () => {
    try {
      setLoadingBobine(true);

      // Verifica che ci sia un cantiere valido
      if (!comanda?.id_cantiere) {
        console.warn('ID cantiere non disponibile per il caricamento bobine');
        return;
      }

      console.log(`Caricamento bobine per cantiere ${comanda.id_cantiere}`);

      // Carica tutte le bobine disponibili per il cantiere
      const bobineData = await caviService.getBobineDisponibili(comanda.id_cantiere);
      setBobineDisponibili(bobineData);

      console.log(`Bobine caricate: ${bobineData.length}`);

    } catch (err) {
      console.error('Errore nel caricamento bobine:', err);

      // Gestione specifica per errori di autenticazione
      if (err.status === 401) {
        console.warn('Errore 401: Sessione scaduta o non autenticato');
        setError('Sessione scaduta. Effettua nuovamente il login per caricare le bobine.');
      } else if (err.isNetworkError) {
        console.warn('Errore di rete nel caricamento bobine');
        setError('Impossibile connettersi al server per caricare le bobine.');
      } else {
        console.warn('Errore generico nel caricamento bobine:', err);
        setError('Errore nel caricamento delle bobine. Puoi comunque usare BOBINA_VUOTA.');
      }

      // Imposta array vuoto per evitare errori nell'interfaccia
      setBobineDisponibili([]);
    } finally {
      setLoadingBobine(false);
    }
  };

  const handleMetriChange = (idCavo, value) => {
    const numericValue = parseFloat(value) || 0;

    // Aggiorna i dati di posa
    setDatiPosa(prev => {
      const newDatiPosa = {
        ...prev,
        [idCavo]: {
          ...prev[idCavo],
          metratura_reale: numericValue
        }
      };

      // Validazione metri vs teorico
      const cavo = cavi.find(c => c.id_cavo === idCavo);
      let errors = [];

      if (cavo && numericValue > cavo.metratura_teorica * 1.1) {
        errors.push('Metratura superiore del 10% rispetto al teorico');
      }

      // Validazione metri vs bobina selezionata
      const datiCavo = newDatiPosa[idCavo];
      if (datiCavo?.id_bobina && datiCavo.id_bobina !== 'BOBINA_VUOTA') {
        const bobina = bobineDisponibili.find(b => b.id_bobina === datiCavo.id_bobina);
        if (bobina && numericValue > bobina.metri_residui && !datiCavo.force_over) {
          errors.push(`Bobina ha solo ${bobina.metri_residui}m residui (richiesti ${numericValue}m)`);
        }
      }

      // Aggiorna gli errori di validazione
      setValidationErrors(prevErrors => {
        if (errors.length > 0) {
          return {
            ...prevErrors,
            [idCavo]: errors.join('; ')
          };
        } else {
          const newErrors = { ...prevErrors };
          delete newErrors[idCavo];
          return newErrors;
        }
      });

      return newDatiPosa;
    });
  };

  const handleNoteChange = (idCavo, value) => {
    setDatiPosa(prev => ({
      ...prev,
      [idCavo]: {
        ...prev[idCavo],
        note: value
      }
    }));
  };

  const handleBobinaChange = (idCavo, bobinaId) => {
    setDatiPosa(prev => {
      const newDatiPosa = {
        ...prev,
        [idCavo]: {
          ...prev[idCavo],
          id_bobina: bobinaId,
          force_over: false // Reset force_over quando cambia bobina
        }
      };

      // Rivalidazione dopo cambio bobina
      const datiCavo = newDatiPosa[idCavo];
      if (datiCavo?.metratura_reale) {
        // Usa setTimeout per evitare problemi di stato asincrono
        setTimeout(() => {
          handleMetriChange(idCavo, datiCavo.metratura_reale);
        }, 0);
      }

      return newDatiPosa;
    });
  };

  const handleForceOverChange = (idCavo, forceOver) => {
    setDatiPosa(prev => {
      const newDatiPosa = {
        ...prev,
        [idCavo]: {
          ...prev[idCavo],
          force_over: forceOver
        }
      };

      // Rivalidazione dopo cambio force_over
      const datiCavo = newDatiPosa[idCavo];
      if (datiCavo?.metratura_reale) {
        // Usa setTimeout per evitare problemi di stato asincrono
        setTimeout(() => {
          handleMetriChange(idCavo, datiCavo.metratura_reale);
        }, 0);
      }

      return newDatiPosa;
    });
  };

  const handleSave = async () => {
    try {
      setLoading(true);
      setError(null);

      // Validazione finale
      const hasErrors = Object.keys(validationErrors).length > 0;
      if (hasErrors) {
        setError('Correggere gli errori di validazione prima di salvare');
        return;
      }

      // Salva i dati di posa con associazioni bobine
      await comandeService.aggiornaDatiPosaConBobine(comanda.codice_comanda, datiPosa);
      
      onSuccess?.('Dati di posa salvati con successo');
      onClose();
      
    } catch (err) {
      console.error('Errore nel salvataggio:', err);
      setError(err.message || 'Errore nel salvataggio dei dati');
    } finally {
      setLoading(false);
    }
  };

  const getTipoComandaColor = (tipo) => {
    switch (tipo) {
      case 'POSA': return 'primary';
      case 'COLLEGAMENTO_PARTENZA': return 'warning';
      case 'COLLEGAMENTO_ARRIVO': return 'info';
      case 'CERTIFICAZIONE': return 'success';
      default: return 'default';
    }
  };

  const getTipoComandaLabel = (tipo) => {
    switch (tipo) {
      case 'POSA': return 'Posa';
      case 'COLLEGAMENTO_PARTENZA': return 'Coll. Partenza';
      case 'COLLEGAMENTO_ARRIVO': return 'Coll. Arrivo';
      case 'CERTIFICAZIONE': return 'Certificazione';
      default: return tipo;
    }
  };

  // Filtra bobine compatibili per un cavo specifico
  const getBobineCompatibili = (cavo) => {
    if (!bobineDisponibili || bobineDisponibili.length === 0) {
      console.log('Nessuna bobina disponibile per il filtro');
      return [];
    }

    console.log('Filtro compatibilità per cavo:', {
      id_cavo: cavo.id_cavo,
      tipologia: cavo.tipologia,
      formazione: cavo.formazione
    });

    console.log('Bobine disponibili:', bobineDisponibili.map(b => ({
      id_bobina: b.id_bobina,
      tipologia: b.tipologia,
      sezione: b.sezione,
      metri_residui: b.metri_residui
    })));

    const bobineCompatibili = bobineDisponibili.filter(bobina => {
      // Confronta tipologia
      const tipologiaMatch = bobina.tipologia === cavo.tipologia;

      // Confronta sezione - il backend usa 'sezione'
      const sezioneMatch = bobina.sezione === cavo.formazione;

      // Verifica metri residui
      const metriOk = bobina.metri_residui > 0;

      console.log(`Bobina ${bobina.id_bobina}:`, {
        tipologiaMatch,
        sezioneMatch,
        metriOk,
        bobina_tipologia: bobina.tipologia,
        cavo_tipologia: cavo.tipologia,
        bobina_sezione: bobina.sezione,
        cavo_formazione: cavo.formazione
      });

      return tipologiaMatch && sezioneMatch && metriOk;
    });

    console.log(`Bobine compatibili trovate: ${bobineCompatibili.length}`, bobineCompatibili);
    return bobineCompatibili;
  };

  // Verifica se una bobina è compatibile con un cavo
  const isBobinaCompatibile = (bobina, cavo) => {
    return bobina.tipologia === cavo.tipologia && bobina.sezione === cavo.formazione;
  };

  // Ottiene il colore per lo stato della bobina
  const getBobinaStatusColor = (bobina, metriRichiesti) => {
    if (!bobina || bobina.id_bobina === 'BOBINA_VUOTA') return 'default';
    if (metriRichiesti > bobina.metri_residui) return 'error';
    if (bobina.metri_residui < bobina.metri_totali * 0.1) return 'warning';
    return 'success';
  };

  if (!comanda) return null;

  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: { minHeight: '70vh' }
      }}
    >
      <DialogTitle>
        <Box display="flex" alignItems="center" gap={2}>
          <CableIcon color="primary" />
          <Box>
            <Typography variant="h6">
              Inserimento Metri Posati
            </Typography>
            <Box display="flex" alignItems="center" gap={1} mt={1}>
              <Typography variant="body2" color="textSecondary">
                Comanda: {comanda.codice_comanda}
              </Typography>
              <Chip 
                label={getTipoComandaLabel(comanda.tipo_comanda)}
                color={getTipoComandaColor(comanda.tipo_comanda)}
                size="small"
              />
              <Typography variant="body2" color="textSecondary">
                Responsabile: {comanda.responsabile}
              </Typography>
            </Box>
          </Box>
        </Box>
      </DialogTitle>

      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {loading ? (
          <Box display="flex" justifyContent="center" p={3}>
            <Typography>Caricamento cavi...</Typography>
          </Box>
        ) : (
          <TableContainer component={Paper} variant="outlined">
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>ID Cavo</TableCell>
                  <TableCell>Tipologia</TableCell>
                  <TableCell>Formazione</TableCell>
                  <TableCell>Metri Teorici</TableCell>
                  <TableCell>Metri Reali</TableCell>
                  <TableCell>Bobina</TableCell>
                  <TableCell>Stato</TableCell>
                  <TableCell>Note</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {cavi.map((cavo) => (
                  <TableRow key={cavo.id_cavo}>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold">
                        {cavo.id_cavo}
                      </Typography>
                    </TableCell>
                    <TableCell>{cavo.tipologia || 'N/A'}</TableCell>
                    <TableCell>{cavo.formazione || 'N/A'}</TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {cavo.metratura_teorica?.toFixed(1) || '0.0'} m
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <TextField
                        type="number"
                        size="small"
                        value={datiPosa[cavo.id_cavo]?.metratura_reale || 0}
                        onChange={(e) => handleMetriChange(cavo.id_cavo, e.target.value)}
                        error={!!validationErrors[cavo.id_cavo]}
                        helperText={validationErrors[cavo.id_cavo]}
                        inputProps={{
                          min: 0,
                          step: 0.1,
                          style: { textAlign: 'right' }
                        }}
                        sx={{ width: 100 }}
                      />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ minWidth: 200 }}>
                        <Autocomplete
                          size="small"
                          value={
                            datiPosa[cavo.id_cavo]?.id_bobina === 'BOBINA_VUOTA'
                              ? { id_bobina: 'BOBINA_VUOTA', tipologia: 'Vuota', metri_residui: '∞' }
                              : bobineDisponibili.find(b => b.id_bobina === datiPosa[cavo.id_cavo]?.id_bobina) || null
                          }
                          onChange={(event, newValue) => {
                            handleBobinaChange(cavo.id_cavo, newValue?.id_bobina || '');
                          }}
                          options={[
                            { id_bobina: 'BOBINA_VUOTA', tipologia: 'Vuota', metri_residui: '∞' },
                            ...getBobineCompatibili(cavo)
                          ]}
                          noOptionsText={
                            loadingBobine
                              ? "Caricamento bobine..."
                              : bobineDisponibili.length === 0
                                ? "Bobine non disponibili - usa BOBINA_VUOTA"
                                : "Nessuna bobina compatibile - usa BOBINA_VUOTA"
                          }
                          getOptionLabel={(option) => {
                            if (option.id_bobina === 'BOBINA_VUOTA') return 'BOBINA_VUOTA';
                            return `${option.id_bobina} (${option.metri_residui}m)`;
                          }}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              placeholder="Seleziona bobina..."
                              variant="outlined"
                              size="small"
                            />
                          )}
                          renderOption={(props, option) => (
                            <Box component="li" {...props}>
                              <ListItemIcon>
                                <BobinaIcon
                                  fontSize="small"
                                  color={
                                    option.id_bobina === 'BOBINA_VUOTA'
                                      ? 'default'
                                      : getBobinaStatusColor(option, datiPosa[cavo.id_cavo]?.metratura_reale || 0)
                                  }
                                />
                              </ListItemIcon>
                              <ListItemText
                                primary={option.id_bobina === 'BOBINA_VUOTA' ? 'BOBINA_VUOTA' : option.id_bobina}
                                secondary={
                                  option.id_bobina === 'BOBINA_VUOTA'
                                    ? 'Nessuna bobina associata'
                                    : `${option.tipologia} ${option.sezione} - ${option.metri_residui}m residui`
                                }
                              />
                            </Box>
                          )}
                          loading={loadingBobine}
                          disabled={loadingBobine}
                          sx={{ width: '100%' }}
                        />
                        {/* Checkbox per force_over se necessario */}
                        {datiPosa[cavo.id_cavo]?.id_bobina &&
                         datiPosa[cavo.id_cavo]?.id_bobina !== 'BOBINA_VUOTA' &&
                         (() => {
                           const bobina = bobineDisponibili.find(b => b.id_bobina === datiPosa[cavo.id_cavo]?.id_bobina);
                           const metriRichiesti = datiPosa[cavo.id_cavo]?.metratura_reale || 0;
                           return bobina && metriRichiesti > bobina.metri_residui;
                         })() && (
                          <Box sx={{ mt: 1 }}>
                            <Button
                              size="small"
                              variant={datiPosa[cavo.id_cavo]?.force_over ? "contained" : "outlined"}
                              color="warning"
                              onClick={() => handleForceOverChange(cavo.id_cavo, !datiPosa[cavo.id_cavo]?.force_over)}
                              startIcon={<WarningIcon />}
                            >
                              Forza Over
                            </Button>
                          </Box>
                        )}
                      </Box>
                    </TableCell>
                    <TableCell>
                      {cavo.stato_installazione === 'Installato' ? (
                        <Chip 
                          icon={<CheckCircleIcon />}
                          label="Installato" 
                          color="success" 
                          size="small" 
                        />
                      ) : (
                        <Chip 
                          icon={<WarningIcon />}
                          label="Da Installare" 
                          color="warning" 
                          size="small" 
                        />
                      )}
                    </TableCell>
                    <TableCell>
                      <TextField
                        size="small"
                        placeholder="Note opzionali..."
                        value={datiPosa[cavo.id_cavo]?.note || ''}
                        onChange={(e) => handleNoteChange(cavo.id_cavo, e.target.value)}
                        sx={{ width: 150 }}
                      />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}

        {cavi.length === 0 && !loading && (
          <Alert severity="info" sx={{ mt: 2 }}>
            Nessun cavo assegnato a questa comanda.
          </Alert>
        )}
      </DialogContent>

      <DialogActions sx={{ p: 2, gap: 1 }}>
        <Button
          onClick={onClose}
          startIcon={<CancelIcon />}
          disabled={loading}
        >
          Annulla
        </Button>
        <Button
          onClick={handleSave}
          variant="contained"
          startIcon={<SaveIcon />}
          disabled={loading || cavi.length === 0}
        >
          Salva Dati Posa
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default InserimentoMetriDialog;
