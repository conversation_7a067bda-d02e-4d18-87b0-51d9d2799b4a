#!/usr/bin/env python3
"""
Script per aggiornare i dati di localizzazione del cantiere nel database SQLite.
Questo permetterà al sistema di utilizzare dati meteorologici reali invece di quelli demo.
"""

import sqlite3
import os

def update_cantiere_location():
    """Aggiorna i dati di localizzazione del cantiere nel database SQLite."""
    
    # Percorso del database
    db_path = "CMS/cantieri.db"
    
    if not os.path.exists(db_path):
        print(f"❌ Database non trovato: {db_path}")
        return False
    
    try:
        # Connetti al database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Verifica la struttura della tabella cantieri
        cursor.execute("PRAGMA table_info(cantieri)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        print("📋 Struttura tabella cantieri:")
        for col in columns:
            print(f"   {col[1]} ({col[2]})")
        
        # Verifica se esistono le colonne necessarie
        required_columns = ['citta_cantiere', 'nazione_cantiere', 'nome_cliente', 'indirizzo_cantiere']
        missing_columns = [col for col in required_columns if col not in column_names]
        
        if missing_columns:
            print(f"\n⚠️ Colonne mancanti: {missing_columns}")
            print("Aggiunta delle colonne mancanti...")
            
            # Aggiungi le colonne mancanti
            for col in missing_columns:
                try:
                    cursor.execute(f"ALTER TABLE cantieri ADD COLUMN {col} TEXT")
                    print(f"✅ Aggiunta colonna: {col}")
                except sqlite3.Error as e:
                    print(f"❌ Errore aggiunta colonna {col}: {e}")
        
        # Recupera i dati attuali del cantiere
        cursor.execute("SELECT id_cantiere, nome, descrizione, citta_cantiere, nazione_cantiere FROM cantieri WHERE id_cantiere = 1")
        result = cursor.fetchone()
        
        if not result:
            print("❌ Cantiere con ID 1 non trovato")
            return False
        
        print(f"\n🏗️ Cantiere trovato:")
        print(f"   ID: {result[0]}")
        print(f"   Nome: {result[1]}")
        print(f"   Descrizione: {result[2]}")
        print(f"   Città attuale: {result[3]}")
        print(f"   Nazione attuale: {result[4]}")
        
        # Aggiorna i dati di localizzazione
        update_query = """
        UPDATE cantieri 
        SET citta_cantiere = ?, 
            nazione_cantiere = ?, 
            nome_cliente = ?, 
            indirizzo_cantiere = ?
        WHERE id_cantiere = 1
        """
        
        # Dati di esempio - puoi modificarli secondo le tue necessità
        new_data = (
            "Milano",  # citta_cantiere
            "Italia",  # nazione_cantiere
            "Cliente Demo",  # nome_cliente
            "Via Roma 123, Milano"  # indirizzo_cantiere
        )
        
        cursor.execute(update_query, new_data)
        
        # Verifica l'aggiornamento
        cursor.execute("SELECT id_cantiere, nome, citta_cantiere, nazione_cantiere, nome_cliente, indirizzo_cantiere FROM cantieri WHERE id_cantiere = 1")
        updated_result = cursor.fetchone()
        
        print(f"\n✅ Dati aggiornati:")
        print(f"   ID: {updated_result[0]}")
        print(f"   Nome: {updated_result[1]}")
        print(f"   Città: {updated_result[2]}")
        print(f"   Nazione: {updated_result[3]}")
        print(f"   Cliente: {updated_result[4]}")
        print(f"   Indirizzo: {updated_result[5]}")
        
        # Salva le modifiche
        conn.commit()
        
        print("\n🌤️ Ora il sistema dovrebbe utilizzare dati meteorologici del cantiere")
        print("   Riavvia il server e testa la certificazione cavi")
        
        return True
        
    except sqlite3.Error as e:
        print(f"❌ Errore database: {e}")
        return False
        
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    print("🏗️ Aggiornamento dati localizzazione cantiere")
    print("=" * 50)
    
    success = update_cantiere_location()
    
    if not success:
        print("\n❌ Aggiornamento fallito")
        print("Verifica che il database CMS/cantieri.db esista")
        
    print("\n" + "=" * 50)
