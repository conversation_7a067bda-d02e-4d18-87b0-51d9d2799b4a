#!/usr/bin/env python3
"""
Script per aggiornare i dati di localizzazione del cantiere nel database.
Questo permetterà al sistema di utilizzare dati meteorologici reali invece di quelli demo.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backend.database import get_db
from backend.models.cantiere import Cantiere
from sqlalchemy.orm import Session

def update_cantiere_location():
    """Aggiorna i dati di localizzazione del cantiere."""
    
    # Ottieni una sessione del database
    db_session = next(get_db())
    
    try:
        # Recupera il cantiere con ID 1
        cantiere = db_session.query(Cantiere).filter(Cantiere.id_cantiere == 1).first()
        
        if not cantiere:
            print("❌ Cantiere con ID 1 non trovato")
            return False
        
        print(f"📋 Cantiere trovato: {cantiere.commessa}")
        print(f"   Città attuale: {cantiere.citta_cantiere}")
        print(f"   Nazione attuale: {cantiere.nazione_cantiere}")
        
        # Aggiorna i dati di localizzazione
        # Puoi modificare questi valori secondo le tue necessità
        cantiere.citta_cantiere = "Milano"
        cantiere.nazione_cantiere = "Italia"
        cantiere.nome_cliente = "Cliente Demo"
        cantiere.indirizzo_cantiere = "Via Roma 123, Milano"
        
        # Salva le modifiche
        db_session.commit()
        
        print("✅ Dati di localizzazione aggiornati:")
        print(f"   Città: {cantiere.citta_cantiere}")
        print(f"   Nazione: {cantiere.nazione_cantiere}")
        print(f"   Cliente: {cantiere.nome_cliente}")
        print(f"   Indirizzo: {cantiere.indirizzo_cantiere}")
        
        return True
        
    except Exception as e:
        print(f"❌ Errore durante l'aggiornamento: {e}")
        db_session.rollback()
        return False
        
    finally:
        db_session.close()

def verify_cantiere_data():
    """Verifica i dati del cantiere dopo l'aggiornamento."""
    
    db_session = next(get_db())
    
    try:
        cantiere = db_session.query(Cantiere).filter(Cantiere.id_cantiere == 1).first()
        
        if cantiere:
            print("\n🔍 Verifica dati cantiere:")
            print(f"   ID: {cantiere.id_cantiere}")
            print(f"   Commessa: {cantiere.commessa}")
            print(f"   Descrizione: {cantiere.descrizione}")
            print(f"   Cliente: {cantiere.nome_cliente}")
            print(f"   Indirizzo: {cantiere.indirizzo_cantiere}")
            print(f"   Città: {cantiere.citta_cantiere}")
            print(f"   Nazione: {cantiere.nazione_cantiere}")
            
            # Verifica se i dati sono sufficienti per il weather service
            if cantiere.citta_cantiere and cantiere.nazione_cantiere:
                print("✅ Dati sufficienti per il recupero meteo")
                return True
            else:
                print("⚠️ Dati insufficienti per il recupero meteo")
                return False
        else:
            print("❌ Cantiere non trovato")
            return False
            
    except Exception as e:
        print(f"❌ Errore durante la verifica: {e}")
        return False
        
    finally:
        db_session.close()

if __name__ == "__main__":
    print("🏗️ Aggiornamento dati localizzazione cantiere")
    print("=" * 50)
    
    # Aggiorna i dati
    success = update_cantiere_location()
    
    if success:
        # Verifica i dati aggiornati
        verify_cantiere_data()
        
        print("\n🌤️ Ora il sistema dovrebbe utilizzare dati meteorologici reali")
        print("   Riavvia il server e testa la certificazione cavi")
    else:
        print("\n❌ Aggiornamento fallito")
        
    print("\n" + "=" * 50)
