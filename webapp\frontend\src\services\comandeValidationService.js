/**
 * Servizio per la validazione delle comande e controlli di conflitto
 * Implementa controlli intelligenti per evitare conflitti e garantire integrità workflow
 */

import { CABLE_STATES } from '../utils/stateUtils';

/**
 * Tipi di validazione per comande
 */
export const VALIDATION_TYPES = {
  CABLE_STATE: 'cable_state',
  COMMAND_CONFLICT: 'command_conflict', 
  PREREQUISITE: 'prerequisite',
  RESPONSIBLE_CONFLICT: 'responsible_conflict'
};

/**
 * Livelli di severità per i controlli
 */
export const SEVERITY_LEVELS = {
  ERROR: 'error',      // Blocca l'operazione
  WARNING: 'warning',  // Permette ma avvisa
  INFO: 'info'         // Solo informativo
};

/**
 * Classe principale per la validazione delle comande
 */
class ComandeValidationService {
  
  /**
   * Valida una lista di cavi per un tipo di comanda specifico
   * @param {Array} cavi - Lista dei cavi da validare
   * @param {string} tipoComanda - Tipo di comanda (POSA, COLLEGAMENTO_PARTENZA, etc.)
   * @param {string} responsabile - Responsabile della comanda
   * @returns {Object} Risultato della validazione con errori, warning e info
   */
  validateCaviForComanda(cavi, tipoComanda, responsabile) {
    const result = {
      valid: true,
      errors: [],
      warnings: [],
      info: [],
      caviValidi: [],
      caviProblematici: []
    };

    cavi.forEach(cavo => {
      const cavoValidation = this.validateSingleCavo(cavo, tipoComanda, responsabile);
      
      // Aggrega i risultati
      result.errors.push(...cavoValidation.errors);
      result.warnings.push(...cavoValidation.warnings);
      result.info.push(...cavoValidation.info);
      
      if (cavoValidation.valid) {
        result.caviValidi.push(cavo);
      } else {
        result.caviProblematici.push({
          cavo: cavo,
          issues: cavoValidation.errors
        });
      }
    });

    // La validazione generale è valida se non ci sono errori bloccanti
    result.valid = result.errors.length === 0;
    
    return result;
  }

  /**
   * Valida un singolo cavo per un tipo di comanda
   * @param {Object} cavo - Cavo da validare
   * @param {string} tipoComanda - Tipo di comanda
   * @param {string} responsabile - Responsabile della comanda
   * @returns {Object} Risultato della validazione per il singolo cavo
   */
  validateSingleCavo(cavo, tipoComanda, responsabile) {
    const result = {
      valid: true,
      errors: [],
      warnings: [],
      info: []
    };

    // 1. Controlli di stato del cavo
    const stateChecks = this.checkCableState(cavo, tipoComanda);
    result.errors.push(...stateChecks.errors);
    result.warnings.push(...stateChecks.warnings);
    result.info.push(...stateChecks.info);

    // 2. Controlli di conflitto comande
    const commandChecks = this.checkCommandConflicts(cavo, tipoComanda);
    result.errors.push(...commandChecks.errors);
    result.warnings.push(...commandChecks.warnings);
    result.info.push(...commandChecks.info);

    // 3. Controlli prerequisiti
    const prerequisiteChecks = this.checkPrerequisites(cavo, tipoComanda);
    result.errors.push(...prerequisiteChecks.errors);
    result.warnings.push(...prerequisiteChecks.warnings);
    result.info.push(...prerequisiteChecks.info);

    // 4. Controlli responsabile
    const responsibleChecks = this.checkResponsibleConflicts(cavo, tipoComanda, responsabile);
    result.warnings.push(...responsibleChecks.warnings);
    result.info.push(...responsibleChecks.info);

    result.valid = result.errors.length === 0;
    return result;
  }

  /**
   * Controlla lo stato del cavo per determinare se può essere assegnato alla comanda
   */
  checkCableState(cavo, tipoComanda) {
    const result = { errors: [], warnings: [], info: [] };
    
    const isInstalled = cavo.stato_installazione === CABLE_STATES.INSTALLATO;
    const hasMeters = cavo.metratura_reale && parseFloat(cavo.metratura_reale) > 0;
    const isConnected = cavo.collegamenti && parseInt(cavo.collegamenti) > 0;
    const isCertified = cavo.stato_certificazione === 'CERTIFICATO';

    switch (tipoComanda) {
      case 'POSA':
        // Controllo 1: metri reali devono essere 0
        if (hasMeters) {
          result.errors.push({
            type: VALIDATION_TYPES.CABLE_STATE,
            severity: SEVERITY_LEVELS.ERROR,
            cavoId: cavo.id_cavo,
            message: `Cavo ${cavo.id_cavo} ha già metri reali inseriti (${cavo.metratura_reale}m). Non può essere assegnato a comanda posa.`,
            details: {
              stato: cavo.stato_installazione,
              metri: cavo.metratura_reale
            }
          });
        }

        // Controllo 2: deve avere metri teorici > 0
        const metriTeorici = parseFloat(cavo.metri_teorici) || 0;
        if (metriTeorici <= 0) {
          result.errors.push({
            type: VALIDATION_TYPES.CABLE_STATE,
            severity: SEVERITY_LEVELS.ERROR,
            cavoId: cavo.id_cavo,
            message: `Cavo ${cavo.id_cavo} non ha metri teorici inseriti. Inserire prima i metri teorici per poter creare una comanda posa.`,
            details: {
              metri_teorici: cavo.metri_teorici
            }
          });
        }

        // Controllo 3: stato installazione deve essere "Da installare"
        if (isInstalled) {
          result.errors.push({
            type: VALIDATION_TYPES.CABLE_STATE,
            severity: SEVERITY_LEVELS.ERROR,
            cavoId: cavo.id_cavo,
            message: `Cavo ${cavo.id_cavo} risulta già installato. Non può essere assegnato a comanda posa.`,
            details: {
              stato: cavo.stato_installazione
            }
          });
        }
        break;

      case 'COLLEGAMENTO_PARTENZA':
      case 'COLLEGAMENTO_ARRIVO':
        if (!isInstalled && !hasMeters) {
          result.errors.push({
            type: VALIDATION_TYPES.CABLE_STATE,
            severity: SEVERITY_LEVELS.ERROR,
            cavoId: cavo.id_cavo,
            message: `Cavo ${cavo.id_cavo} non ancora posato. Deve essere installato prima del collegamento.`,
            details: {
              stato: cavo.stato_installazione,
              metri: cavo.metratura_reale
            }
          });
        }
        
        // Controllo specifico per lato già collegato
        const lato = tipoComanda === 'COLLEGAMENTO_PARTENZA' ? 'partenza' : 'arrivo';
        const isAlreadyConnected = this.isLatoConnected(cavo, lato);
        
        if (isAlreadyConnected) {
          result.warnings.push({
            type: VALIDATION_TYPES.CABLE_STATE,
            severity: SEVERITY_LEVELS.WARNING,
            cavoId: cavo.id_cavo,
            message: `Cavo ${cavo.id_cavo} già collegato lato ${lato}. Verificare se necessario ricollegamento.`,
            details: {
              collegamenti: cavo.collegamenti,
              lato: lato
            }
          });
        }
        break;

      case 'CERTIFICAZIONE':
        if (!isInstalled && !hasMeters) {
          result.warnings.push({
            type: VALIDATION_TYPES.CABLE_STATE,
            severity: SEVERITY_LEVELS.WARNING,
            cavoId: cavo.id_cavo,
            message: `Cavo ${cavo.id_cavo} non ancora posato. La certificazione è possibile ma limitata.`,
            details: {
              stato: cavo.stato_installazione
            }
          });
        }

        if (isCertified) {
          result.warnings.push({
            type: VALIDATION_TYPES.CABLE_STATE,
            severity: SEVERITY_LEVELS.WARNING,
            cavoId: cavo.id_cavo,
            message: `Cavo ${cavo.id_cavo} già certificato. Verificare se necessaria ri-certificazione.`,
            details: {
              stato_certificazione: cavo.stato_certificazione,
              data_certificazione: cavo.data_certificazione_cavo
            }
          });
        }
        break;
    }

    return result;
  }

  /**
   * Controlla conflitti con comande esistenti
   */
  checkCommandConflicts(cavo, tipoComanda) {
    const result = { errors: [], warnings: [], info: [] };

    const comandeAssegnate = {
      posa: cavo.comanda_posa,
      partenza: cavo.comanda_partenza,
      arrivo: cavo.comanda_arrivo,
      certificazione: cavo.comanda_certificazione
    };

    switch (tipoComanda) {
      case 'POSA':
        if (comandeAssegnate.posa) {
          result.errors.push({
            type: VALIDATION_TYPES.COMMAND_CONFLICT,
            severity: SEVERITY_LEVELS.ERROR,
            cavoId: cavo.id_cavo,
            message: `Cavo ${cavo.id_cavo} già assegnato alla comanda posa ${comandeAssegnate.posa}`,
            details: {
              comandaEsistente: comandeAssegnate.posa,
              tipoConflitto: 'POSA_DUPLICATA'
            }
          });
        }
        break;

      case 'COLLEGAMENTO_PARTENZA':
        if (comandeAssegnate.partenza) {
          result.errors.push({
            type: VALIDATION_TYPES.COMMAND_CONFLICT,
            severity: SEVERITY_LEVELS.ERROR,
            cavoId: cavo.id_cavo,
            message: `Cavo ${cavo.id_cavo} già assegnato alla comanda collegamento partenza ${comandeAssegnate.partenza}`,
            details: {
              comandaEsistente: comandeAssegnate.partenza,
              tipoConflitto: 'COLLEGAMENTO_PARTENZA_DUPLICATO'
            }
          });
        }
        break;

      case 'COLLEGAMENTO_ARRIVO':
        if (comandeAssegnate.arrivo) {
          result.errors.push({
            type: VALIDATION_TYPES.COMMAND_CONFLICT,
            severity: SEVERITY_LEVELS.ERROR,
            cavoId: cavo.id_cavo,
            message: `Cavo ${cavo.id_cavo} già assegnato alla comanda collegamento arrivo ${comandeAssegnate.arrivo}`,
            details: {
              comandaEsistente: comandeAssegnate.arrivo,
              tipoConflitto: 'COLLEGAMENTO_ARRIVO_DUPLICATO'
            }
          });
        }
        break;

      case 'CERTIFICAZIONE':
        if (comandeAssegnate.certificazione) {
          result.warnings.push({
            type: VALIDATION_TYPES.COMMAND_CONFLICT,
            severity: SEVERITY_LEVELS.WARNING,
            cavoId: cavo.id_cavo,
            message: `Cavo ${cavo.id_cavo} già assegnato alla comanda certificazione ${comandeAssegnate.certificazione}. Possibile ri-certificazione.`,
            details: {
              comandaEsistente: comandeAssegnate.certificazione,
              tipoConflitto: 'CERTIFICAZIONE_DUPLICATA'
            }
          });
        }
        break;
    }

    return result;
  }

  /**
   * Controlla prerequisiti per il tipo di comanda
   */
  checkPrerequisites(cavo, tipoComanda) {
    const result = { errors: [], warnings: [], info: [] };

    switch (tipoComanda) {
      case 'COLLEGAMENTO_PARTENZA':
      case 'COLLEGAMENTO_ARRIVO':
        // Prerequisito: deve esistere comanda posa completata o cavo installato
        if (!cavo.comanda_posa && (!cavo.metratura_reale || parseFloat(cavo.metratura_reale) <= 0)) {
          result.warnings.push({
            type: VALIDATION_TYPES.PREREQUISITE,
            severity: SEVERITY_LEVELS.WARNING,
            cavoId: cavo.id_cavo,
            message: `Cavo ${cavo.id_cavo} non ha comanda posa assegnata e non risulta installato. Verificare prerequisiti.`,
            details: {
              prerequisitoMancante: 'POSA_O_INSTALLAZIONE'
            }
          });
        }
        break;

      case 'CERTIFICAZIONE':
        // Info: la certificazione è più efficace se il cavo è installato e collegato
        const isInstalled = cavo.metratura_reale && parseFloat(cavo.metratura_reale) > 0;
        const isConnected = cavo.collegamenti && parseInt(cavo.collegamenti) > 0;
        
        if (!isInstalled) {
          result.info.push({
            type: VALIDATION_TYPES.PREREQUISITE,
            severity: SEVERITY_LEVELS.INFO,
            cavoId: cavo.id_cavo,
            message: `Cavo ${cavo.id_cavo} non ancora installato. La certificazione sarà limitata ai test di continuità.`,
            details: {
              suggerimento: 'INSTALLAZIONE_CONSIGLIATA'
            }
          });
        }

        if (!isConnected) {
          result.info.push({
            type: VALIDATION_TYPES.PREREQUISITE,
            severity: SEVERITY_LEVELS.INFO,
            cavoId: cavo.id_cavo,
            message: `Cavo ${cavo.id_cavo} non ancora collegato. Test di isolamento limitati.`,
            details: {
              suggerimento: 'COLLEGAMENTO_CONSIGLIATO'
            }
          });
        }
        break;
    }

    return result;
  }

  /**
   * Controlla conflitti con responsabili esistenti
   */
  checkResponsibleConflicts(cavo, tipoComanda, nuovoResponsabile) {
    const result = { errors: [], warnings: [], info: [] };

    const responsabili = {
      posa: cavo.responsabile_posa,
      partenza: cavo.responsabile_partenza,
      arrivo: cavo.responsabile_arrivo
    };

    // Controlla se ci sono responsabili diversi per lo stesso cavo
    const responsabiliAttivi = Object.values(responsabili).filter(r => r && r.trim() !== '');
    const responsabiliUnici = [...new Set(responsabiliAttivi)];

    if (responsabiliUnici.length > 1 && !responsabiliUnici.includes(nuovoResponsabile)) {
      result.warnings.push({
        type: VALIDATION_TYPES.RESPONSIBLE_CONFLICT,
        severity: SEVERITY_LEVELS.WARNING,
        cavoId: cavo.id_cavo,
        message: `Cavo ${cavo.id_cavo} ha già responsabili diversi (${responsabiliUnici.join(', ')}). Nuovo responsabile: ${nuovoResponsabile}`,
        details: {
          responsabiliEsistenti: responsabiliUnici,
          nuovoResponsabile: nuovoResponsabile
        }
      });
    }

    return result;
  }

  /**
   * Verifica se un lato del cavo è già collegato
   */
  isLatoConnected(cavo, lato) {
    const collegamenti = parseInt(cavo.collegamenti) || 0;
    
    if (lato === 'partenza') {
      return (collegamenti & 1) === 1; // Bit 0 per partenza
    } else if (lato === 'arrivo') {
      return (collegamenti & 2) === 2; // Bit 1 per arrivo
    }
    
    return false;
  }

  /**
   * Formatta i risultati della validazione per la UI
   */
  formatValidationResults(validationResult) {
    return {
      canProceed: validationResult.valid,
      totalCavi: validationResult.caviValidi.length + validationResult.caviProblematici.length,
      caviValidi: validationResult.caviValidi.length,
      caviProblematici: validationResult.caviProblematici.length,
      errorsCount: validationResult.errors.length,
      warningsCount: validationResult.warnings.length,
      infoCount: validationResult.info.length,
      summary: this.generateSummary(validationResult)
    };
  }

  /**
   * Genera un riassunto testuale della validazione
   */
  generateSummary(validationResult) {
    const { caviValidi, caviProblematici, errors, warnings } = validationResult;
    
    let summary = `${caviValidi.length} cavi validi`;
    
    if (caviProblematici.length > 0) {
      summary += `, ${caviProblematici.length} con problemi`;
    }
    
    if (errors.length > 0) {
      summary += ` (${errors.length} errori bloccanti)`;
    }
    
    if (warnings.length > 0) {
      summary += ` (${warnings.length} avvisi)`;
    }
    
    return summary;
  }
}

// Esporta un'istanza singleton del servizio
export default new ComandeValidationService();
