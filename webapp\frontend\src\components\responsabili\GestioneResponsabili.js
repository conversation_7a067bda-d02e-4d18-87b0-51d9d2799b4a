import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typo<PERSON>,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  CircularProgress,
  Tooltip,
  Chip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  Divider
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Person as PersonIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  ExpandMore as ExpandMoreIcon,
  Assignment as AssignmentIcon
} from '@mui/icons-material';
import responsabiliService from '../../services/responsabiliService';
import comandeService from '../../services/comandeService';

const GestioneResponsabili = ({ cantiereId, open, onClose }) => {
  const [responsabili, setResponsabili] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogMode, setDialogMode] = useState('create'); // 'create', 'edit'
  const [selectedResponsabile, setSelectedResponsabile] = useState(null);
  const [comandePerResponsabile, setComandePerResponsabile] = useState({});
  const [formData, setFormData] = useState({
    nome_responsabile: '',
    email: '',
    telefono: ''
  });

  useEffect(() => {
    if (open && cantiereId) {
      loadResponsabili();
    }
  }, [open, cantiereId]);

  const loadResponsabili = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await responsabiliService.getResponsabiliCantiere(cantiereId);
      setResponsabili(data || []);
      
      // Carica le comande per ogni responsabile
      await loadComandePerResponsabili(data || []);
    } catch (err) {
      console.error('Errore nel caricamento dei responsabili:', err);
      setError('Errore nel caricamento dei responsabili');
    } finally {
      setLoading(false);
    }
  };

  const loadComandePerResponsabili = async (responsabiliList) => {
    try {
      const comandeMap = {};
      
      for (const responsabile of responsabiliList) {
        try {
          const comande = await comandeService.getComandeByResponsabile(cantiereId, responsabile.nome_responsabile);
          comandeMap[responsabile.id_responsabile] = comande || [];
        } catch (err) {
          console.error(`Errore nel caricamento comande per ${responsabile.nome_responsabile}:`, err);
          comandeMap[responsabile.id_responsabile] = [];
        }
      }
      
      setComandePerResponsabile(comandeMap);
    } catch (err) {
      console.error('Errore nel caricamento delle comande:', err);
    }
  };

  const handleOpenDialog = (mode, responsabile = null) => {
    setDialogMode(mode);
    setSelectedResponsabile(responsabile);
    
    if (mode === 'edit' && responsabile) {
      setFormData({
        nome_responsabile: responsabile.nome_responsabile || '',
        email: responsabile.email || '',
        telefono: responsabile.telefono || ''
      });
    } else {
      setFormData({
        nome_responsabile: '',
        email: '',
        telefono: ''
      });
    }
    
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedResponsabile(null);
    setError(null);
  };

  const handleSubmit = async () => {
    try {
      setError(null);
      
      // Validazione
      if (!formData.nome_responsabile.trim()) {
        setError('Il nome del responsabile è obbligatorio');
        return;
      }
      
      if (!formData.email && !formData.telefono) {
        setError('Almeno uno tra email e telefono deve essere specificato');
        return;
      }

      if (dialogMode === 'create') {
        await responsabiliService.createResponsabile(cantiereId, formData);
      } else if (dialogMode === 'edit') {
        await responsabiliService.updateResponsabile(selectedResponsabile.id_responsabile, formData);
      }

      handleCloseDialog();
      await loadResponsabili();
    } catch (err) {
      console.error('Errore nel salvataggio:', err);
      setError(err.detail || 'Errore nel salvataggio del responsabile');
    }
  };

  const handleDelete = async (idResponsabile) => {
    if (!window.confirm('Sei sicuro di voler eliminare questo responsabile?')) {
      return;
    }

    try {
      await responsabiliService.deleteResponsabile(idResponsabile);
      await loadResponsabili();
    } catch (err) {
      console.error('Errore nell\'eliminazione:', err);
      setError('Errore nell\'eliminazione del responsabile');
    }
  };

  const getTipoComandaLabel = (tipo) => {
    const labels = {
      'POSA': 'Posa',
      'COLLEGAMENTO_PARTENZA': 'Coll. Partenza',
      'COLLEGAMENTO_ARRIVO': 'Coll. Arrivo',
      'CERTIFICAZIONE': 'Certificazione',
      'TESTING': 'Testing'
    };
    return labels[tipo] || tipo;
  };

  const getStatoComandaColor = (stato) => {
    const colors = {
      'CREATA': 'default',
      'IN_CORSO': 'primary',
      'COMPLETATA': 'success',
      'ANNULLATA': 'error'
    };
    return colors[stato] || 'default';
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle>
        <Box display="flex" alignItems="center" gap={1}>
          <PersonIcon />
          Gestione Responsabili
        </Box>
      </DialogTitle>
      
      <DialogContent>
        <Box sx={{ pt: 1 }}>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {/* Toolbar */}
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            <Typography variant="h6">
              Responsabili del Cantiere
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => handleOpenDialog('create')}
            >
              Nuovo Responsabile
            </Button>
          </Box>

          {loading ? (
            <Box display="flex" justifyContent="center" py={4}>
              <CircularProgress />
            </Box>
          ) : (
            <Box>
              {responsabili.length === 0 ? (
                <Box textAlign="center" py={4}>
                  <Typography variant="h6" color="textSecondary">
                    Nessun responsabile trovato
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Clicca su "Nuovo Responsabile" per iniziare
                  </Typography>
                </Box>
              ) : (
                responsabili.map((responsabile) => (
                  <Accordion key={responsabile.id_responsabile} sx={{ mb: 1 }}>
                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                      <Box display="flex" alignItems="center" justifyContent="space-between" width="100%">
                        <Box display="flex" alignItems="center" gap={2}>
                          <PersonIcon color="primary" />
                          <Box>
                            <Typography variant="h6">
                              {responsabile.nome_responsabile}
                            </Typography>
                            <Box display="flex" gap={2} mt={0.5}>
                              {responsabile.email && (
                                <Box display="flex" alignItems="center" gap={0.5}>
                                  <EmailIcon fontSize="small" color="action" />
                                  <Typography variant="body2" color="textSecondary">
                                    {responsabile.email}
                                  </Typography>
                                </Box>
                              )}
                              {responsabile.telefono && (
                                <Box display="flex" alignItems="center" gap={0.5}>
                                  <PhoneIcon fontSize="small" color="action" />
                                  <Typography variant="body2" color="textSecondary">
                                    {responsabile.telefono}
                                  </Typography>
                                </Box>
                              )}
                            </Box>
                          </Box>
                        </Box>
                        
                        <Box display="flex" gap={1} onClick={(e) => e.stopPropagation()}>
                          <Chip
                            icon={<AssignmentIcon />}
                            label={`${(comandePerResponsabile[responsabile.id_responsabile] || []).length} comande`}
                            size="small"
                            color="primary"
                            variant="outlined"
                          />
                          <Tooltip title="Modifica">
                            <IconButton
                              size="small"
                              onClick={() => handleOpenDialog('edit', responsabile)}
                            >
                              <EditIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Elimina">
                            <IconButton
                              size="small"
                              color="error"
                              onClick={() => handleDelete(responsabile.id_responsabile)}
                            >
                              <DeleteIcon />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </Box>
                    </AccordionSummary>
                    
                    <AccordionDetails>
                      <Typography variant="subtitle2" gutterBottom>
                        Comande Assegnate:
                      </Typography>
                      
                      {(comandePerResponsabile[responsabile.id_responsabile] || []).length === 0 ? (
                        <Typography variant="body2" color="textSecondary" style={{ fontStyle: 'italic' }}>
                          Nessuna comanda assegnata
                        </Typography>
                      ) : (
                        <List dense>
                          {(comandePerResponsabile[responsabile.id_responsabile] || []).map((comanda) => (
                            <ListItem key={comanda.codice_comanda} divider>
                              <ListItemText
                                primary={
                                  <Box display="flex" alignItems="center" gap={1}>
                                    <Typography variant="body2" fontWeight="bold">
                                      {comanda.codice_comanda}
                                    </Typography>
                                    <Chip
                                      label={getTipoComandaLabel(comanda.tipo_comanda)}
                                      size="small"
                                      variant="outlined"
                                    />
                                    <Chip
                                      label={comanda.stato || 'CREATA'}
                                      size="small"
                                      color={getStatoComandaColor(comanda.stato)}
                                    />
                                  </Box>
                                }
                                secondary={
                                  <Typography variant="body2" color="textSecondary">
                                    {comanda.descrizione || 'Nessuna descrizione'}
                                    {comanda.data_creazione && ` • Creata: ${new Date(comanda.data_creazione).toLocaleDateString()}`}
                                  </Typography>
                                }
                              />
                            </ListItem>
                          ))}
                        </List>
                      )}
                    </AccordionDetails>
                  </Accordion>
                ))
              )}
            </Box>
          )}
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>
          Chiudi
        </Button>
      </DialogActions>

      {/* Dialog per creazione/modifica responsabile */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {dialogMode === 'create' ? 'Nuovo Responsabile' : 'Modifica Responsabile'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}

            <TextField
              fullWidth
              label="Nome Responsabile"
              value={formData.nome_responsabile}
              onChange={(e) => setFormData({ ...formData, nome_responsabile: e.target.value })}
              margin="normal"
              required
            />

            <TextField
              fullWidth
              label="Email"
              type="email"
              value={formData.email}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              margin="normal"
              helperText="Email per notifiche (opzionale se inserisci telefono)"
            />

            <TextField
              fullWidth
              label="Telefono"
              value={formData.telefono}
              onChange={(e) => setFormData({ ...formData, telefono: e.target.value })}
              margin="normal"
              helperText="Numero per SMS (opzionale se inserisci email)"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>
            Annulla
          </Button>
          <Button onClick={handleSubmit} variant="contained">
            {dialogMode === 'create' ? 'Crea' : 'Salva'}
          </Button>
        </DialogActions>
      </Dialog>
    </Dialog>
  );
};

export default GestioneResponsabili;
