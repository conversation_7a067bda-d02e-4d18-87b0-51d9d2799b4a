#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script per testare l'API dei cavi e verificare il campo certificato
"""

import requests
import json

# Configurazione
BASE_URL = "http://127.0.0.1:8002/api"
CANTIERE_ID = 1

def test_api_cavi():
    """Testa l'API dei cavi per verificare il campo certificato."""
    try:
        print("🔍 Test API cavi per verificare campo certificato...")
        print("=" * 60)
        
        # Test senza autenticazione (per vedere se l'endpoint è raggiungibile)
        url = f"{BASE_URL}/cantieri/{CANTIERE_ID}/cavi"
        print(f"📡 Chiamata GET: {url}")
        
        response = requests.get(url)
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code == 401:
            print("🔐 Richiesta autenticazione - normale per API protette")
            print("ℹ️ L'API richiede autenticazione, ma l'endpoint è raggiungibile")
            return
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Risposta ricevuta: {len(data)} cavi")
            
            # Cerca il cavo C001
            cavo_c001 = None
            for cavo in data:
                if cavo.get('id_cavo') == 'C001':
                    cavo_c001 = cavo
                    break
            
            if cavo_c001:
                print(f"\n🔍 Dettagli cavo C001:")
                print(f"  ID Cavo: {cavo_c001.get('id_cavo')}")
                print(f"  Stato Installazione: {cavo_c001.get('stato_installazione')}")
                print(f"  Certificato: {cavo_c001.get('certificato')}")
                print(f"  Tipo certificato: {type(cavo_c001.get('certificato'))}")
            else:
                print("❌ Cavo C001 non trovato nella risposta")
        else:
            print(f"❌ Errore nella risposta: {response.status_code}")
            print(f"Contenuto: {response.text[:200]}...")
        
    except requests.exceptions.ConnectionError:
        print("❌ Impossibile connettersi al server backend")
        print("ℹ️ Assicurati che il backend sia in esecuzione sulla porta 8002")
    except Exception as e:
        print(f"❌ Errore durante il test: {str(e)}")

if __name__ == "__main__":
    test_api_cavi()
