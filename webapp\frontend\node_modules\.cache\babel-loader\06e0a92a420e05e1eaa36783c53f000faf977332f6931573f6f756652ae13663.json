{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\comande\\\\ComandeList.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Card, CardContent, Typography, Button, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Chip, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, TextField, MenuItem, Alert, CircularProgress, Tooltip, Grid, List, ListItem, ListItemText, Divider } from '@mui/material';\nimport { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, Visibility as ViewIcon, Assignment as AssignIcon, Refresh as RefreshIcon, People as PeopleIcon, Person as PersonIcon, Email as EmailIcon, Phone as PhoneIcon, ExpandMore as ExpandMoreIcon } from '@mui/icons-material';\nimport { Accordion, AccordionSummary, AccordionDetails, Tabs, Tab } from '@mui/material';\nimport comandeService from '../../services/comandeService';\nimport CreaComandaConCavi from './CreaComandaConCavi';\nimport responsabiliService from '../../services/responsabiliService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ComandeList = ({\n  cantiereId,\n  cantiereName\n}) => {\n  _s();\n  const [comande, setComande] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [selectedComanda, setSelectedComanda] = useState(null);\n  const [dialogMode, setDialogMode] = useState('edit'); // 'edit', 'view', 'assign'\n  const [formData, setFormData] = useState({\n    tipo_comanda: 'POSA',\n    descrizione: '',\n    responsabile: '',\n    data_scadenza: '',\n    priorita: 'NORMALE',\n    note_capo_cantiere: ''\n  });\n  const [statistiche, setStatistiche] = useState(null);\n  const [caviAssegnazione, setCaviAssegnazione] = useState('');\n  const [openCreaConCavi, setOpenCreaConCavi] = useState(false);\n\n  // Stati per gestione responsabili integrata\n  const [responsabili, setResponsabili] = useState([]);\n  const [loadingResponsabili, setLoadingResponsabili] = useState(false);\n  const [comandePerResponsabile, setComandePerResponsabile] = useState({});\n  const [openResponsabileDialog, setOpenResponsabileDialog] = useState(false);\n  const [dialogModeResponsabile, setDialogModeResponsabile] = useState('create');\n  const [selectedResponsabile, setSelectedResponsabile] = useState(null);\n  const [formDataResponsabile, setFormDataResponsabile] = useState({\n    nome_responsabile: '',\n    email: '',\n    telefono: ''\n  });\n\n  // Stato per le tabs\n  const [activeTab, setActiveTab] = useState(0);\n\n  // Carica le comande al mount del componente\n  useEffect(() => {\n    if (cantiereId) {\n      loadComande();\n      loadStatistiche();\n      loadResponsabili();\n    }\n  }, [cantiereId]);\n  const loadComande = async () => {\n    try {\n      setLoading(true);\n      const response = await comandeService.getComande(cantiereId);\n      setComande(response.comande || []);\n      setError(null);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n      setError('Errore nel caricamento delle comande');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadStatistiche = async () => {\n    try {\n      const stats = await comandeService.getStatisticheComande(cantiereId);\n      setStatistiche(stats);\n    } catch (err) {\n      console.error('Errore nel caricamento delle statistiche:', err);\n    }\n  };\n  const loadResponsabili = async () => {\n    try {\n      setLoadingResponsabili(true);\n      setError(null);\n      const data = await responsabiliService.getResponsabiliCantiere(cantiereId);\n      setResponsabili(data || []);\n\n      // Carica le comande per ogni responsabile\n      await loadComandePerResponsabili(data || []);\n    } catch (err) {\n      console.error('Errore nel caricamento dei responsabili:', err);\n      setError('Errore nel caricamento dei responsabili');\n    } finally {\n      setLoadingResponsabili(false);\n    }\n  };\n  const loadComandePerResponsabili = async responsabiliList => {\n    try {\n      const comandeMap = {};\n      for (const responsabile of responsabiliList) {\n        try {\n          const comande = await comandeService.getComandeByResponsabile(cantiereId, responsabile.nome_responsabile);\n          comandeMap[responsabile.id_responsabile] = comande || [];\n        } catch (err) {\n          console.error(`Errore nel caricamento comande per ${responsabile.nome_responsabile}:`, err);\n          comandeMap[responsabile.id_responsabile] = [];\n        }\n      }\n      setComandePerResponsabile(comandeMap);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n    }\n  };\n  const handleOpenDialog = (mode, comanda = null) => {\n    setDialogMode(mode);\n    setSelectedComanda(comanda);\n    if (mode === 'edit' && comanda) {\n      setFormData({\n        tipo_comanda: comanda.tipo_comanda,\n        descrizione: comanda.descrizione || '',\n        responsabile: comanda.responsabile || '',\n        data_scadenza: comanda.data_scadenza || '',\n        priorita: comanda.priorita || 'NORMALE',\n        note_capo_cantiere: comanda.note_capo_cantiere || ''\n      });\n    }\n    setOpenDialog(true);\n  };\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setSelectedComanda(null);\n    setCaviAssegnazione('');\n    setFormData({\n      tipo_comanda: 'POSA',\n      descrizione: '',\n      responsabile: '',\n      data_scadenza: '',\n      priorita: 'NORMALE',\n      note_capo_cantiere: ''\n    });\n  };\n  const handleSubmit = async () => {\n    try {\n      if (dialogMode === 'edit') {\n        // Modifica comanda esistente\n        const response = await comandeService.updateComanda(selectedComanda.codice_comanda, formData);\n        console.log('Comanda aggiornata:', response);\n      } else if (dialogMode === 'assign') {\n        // Assegnazione cavi\n        if (!caviAssegnazione.trim()) {\n          setError('Inserisci almeno un ID cavo');\n          return;\n        }\n        const listaIdCavi = caviAssegnazione.split(',').map(id => id.trim()).filter(id => id);\n        await comandeService.assegnaCavi(selectedComanda.codice_comanda, listaIdCavi);\n      }\n      handleCloseDialog();\n      loadComande();\n      loadStatistiche();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError('Errore nel salvataggio della comanda');\n    }\n  };\n  const handleDelete = async codiceComanda => {\n    if (window.confirm('Sei sicuro di voler eliminare questa comanda?')) {\n      try {\n        await comandeService.deleteComanda(codiceComanda);\n        loadComande();\n        loadStatistiche();\n      } catch (err) {\n        console.error('Errore nell\\'eliminazione:', err);\n        setError('Errore nell\\'eliminazione della comanda');\n      }\n    }\n  };\n  const getStatoColor = stato => {\n    switch (stato) {\n      case 'CREATA':\n        return 'default';\n      case 'ASSEGNATA':\n        return 'primary';\n      case 'IN_CORSO':\n        return 'warning';\n      case 'COMPLETATA':\n        return 'success';\n      case 'ANNULLATA':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n  const getTipoComandaLabel = tipo => {\n    switch (tipo) {\n      case 'POSA':\n        return 'Posa';\n      case 'COLLEGAMENTO_PARTENZA':\n        return 'Collegamento Partenza';\n      case 'COLLEGAMENTO_ARRIVO':\n        return 'Collegamento Arrivo';\n      case 'CERTIFICAZIONE':\n        return 'Certificazione';\n      case 'TESTING':\n        return 'Testing/Certificazione';\n      default:\n        return tipo;\n    }\n  };\n  const getPrioritaColor = priorita => {\n    switch (priorita) {\n      case 'BASSA':\n        return 'default';\n      case 'NORMALE':\n        return 'primary';\n      case 'ALTA':\n        return 'warning';\n      case 'URGENTE':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      minHeight: \"400px\",\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 265,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      mb: 3,\n      children: statistiche && /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        mb: 2,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 2,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"textSecondary\",\n                gutterBottom: true,\n                children: \"Totale\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                children: statistiche.totale_comande\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 2,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"textSecondary\",\n                gutterBottom: true,\n                children: \"Create\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                children: statistiche.comande_create\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 2,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"textSecondary\",\n                gutterBottom: true,\n                children: \"Assegnate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                children: statistiche.comande_assegnate\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 2,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"textSecondary\",\n                gutterBottom: true,\n                children: \"In Corso\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                children: statistiche.comande_in_corso\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 2,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"textSecondary\",\n                gutterBottom: true,\n                children: \"Completate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                children: statistiche.comande_completate\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 2,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"textSecondary\",\n                gutterBottom: true,\n                children: \"% Completamento\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                children: [statistiche.percentuale_completamento_medio.toFixed(1), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 274,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"space-between\",\n      alignItems: \"center\",\n      mb: 2,\n      flexWrap: \"wrap\",\n      gap: 1,\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        gap: 1,\n        flexWrap: \"wrap\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 24\n          }, this),\n          onClick: () => setOpenCreaConCavi(true),\n          color: \"primary\",\n          children: \"Nuova Comanda\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(PeopleIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 24\n          }, this),\n          onClick: () => setOpenGestioneResponsabili(true),\n          color: \"secondary\",\n          children: \"Gestisci Responsabili\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(AssignIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 24\n          }, this),\n          onClick: () => {\n            if (comande.length === 0) {\n              setError('Nessuna comanda disponibile per l\\'assegnazione');\n              return;\n            }\n            // Apri dialog per selezionare comanda\n            setError('Seleziona una comanda dalla tabella e clicca sull\\'icona \"Assegna Cavi\"');\n          },\n          disabled: comande.length === 0,\n          children: \"Assegna Cavi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 356,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 22\n        }, this),\n        onClick: () => {\n          loadComande();\n          loadStatistiche();\n        },\n        children: \"Aggiorna\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 392,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 355,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 2\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 406,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Paper,\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Codice\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Tipo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Priorit\\xE0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Responsabile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Data Creazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Stato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Cavi Assegnati\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Completamento\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Azioni\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: comande.map(comanda => /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                fontWeight: \"bold\",\n                children: comanda.codice_comanda\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: getTipoComandaLabel(comanda.tipo_comanda),\n                size: \"small\",\n                variant: \"outlined\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 436,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: comanda.priorita || 'NORMALE',\n                size: \"small\",\n                color: getPrioritaColor(comanda.priorita || 'NORMALE')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 443,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: comanda.responsabile\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 449,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: new Date(comanda.data_creazione).toLocaleDateString('it-IT')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: comanda.stato,\n                color: getStatoColor(comanda.stato),\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 454,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 453,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: comanda.numero_cavi_assegnati || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: comanda.percentuale_completamento ? `${comanda.percentuale_completamento.toFixed(1)}%` : '0%'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"Visualizza\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: () => handleOpenDialog('view', comanda),\n                  children: /*#__PURE__*/_jsxDEV(ViewIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 471,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 467,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 466,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"Modifica\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: () => handleOpenDialog('edit', comanda),\n                  children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 479,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 475,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 474,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"Assegna Cavi\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: () => handleOpenDialog('assign', comanda),\n                  color: \"primary\",\n                  children: /*#__PURE__*/_jsxDEV(AssignIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 488,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 483,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 482,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"Elimina\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: () => handleDelete(comanda.codice_comanda),\n                  color: \"error\",\n                  children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 497,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 492,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 491,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 17\n            }, this)]\n          }, comanda.codice_comanda, true, {\n            fileName: _jsxFileName,\n            lineNumber: 429,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 427,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 413,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 412,\n      columnNumber: 7\n    }, this), comande.length === 0 && !loading && /*#__PURE__*/_jsxDEV(Box, {\n      textAlign: \"center\",\n      py: 4,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"textSecondary\",\n        children: \"Nessuna comanda trovata\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 509,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"textSecondary\",\n        children: \"Clicca su \\\"Nuova Comanda\\\" per iniziare\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 512,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 508,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openDialog,\n      onClose: handleCloseDialog,\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: [dialogMode === 'edit' && 'Modifica Comanda', dialogMode === 'view' && 'Dettagli Comanda', dialogMode === 'assign' && `Assegna Cavi - ${selectedComanda === null || selectedComanda === void 0 ? void 0 : selectedComanda.codice_comanda}`]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 520,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            pt: 1\n          },\n          children: dialogMode === 'assign' ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"info\",\n              sx: {\n                mb: 2\n              },\n              children: \"Inserisci gli ID dei cavi da assegnare alla comanda, separati da virgola.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 529,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"ID Cavi (separati da virgola)\",\n              value: caviAssegnazione,\n              onChange: e => setCaviAssegnazione(e.target.value),\n              margin: \"normal\",\n              placeholder: \"es: CAVO001, CAVO002, CAVO003\",\n              helperText: \"Esempio: CAVO001, CAVO002, CAVO003\",\n              multiline: true,\n              rows: 3\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 532,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) : dialogMode === 'view' && selectedComanda ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: /*#__PURE__*/_jsxDEV(List, {\n              children: [/*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Codice Comanda\",\n                  secondary: selectedComanda.codice_comanda\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 548,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 547,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 553,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Tipo\",\n                  secondary: getTipoComandaLabel(selectedComanda.tipo_comanda)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 555,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 554,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 560,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Stato\",\n                  secondary: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: selectedComanda.stato,\n                    color: getStatoColor(selectedComanda.stato),\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 565,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 562,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 561,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 573,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Descrizione\",\n                  secondary: selectedComanda.descrizione || 'Nessuna descrizione'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 575,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 574,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 580,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Priorit\\xE0\",\n                  secondary: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: selectedComanda.priorita || 'NORMALE',\n                    color: getPrioritaColor(selectedComanda.priorita || 'NORMALE'),\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 585,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 582,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 581,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 593,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Responsabile\",\n                  secondary: selectedComanda.responsabile || 'Non assegnato'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 595,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 594,\n                columnNumber: 19\n              }, this), selectedComanda.note_capo_cantiere && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 602,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                  children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: \"Note Capo Cantiere\",\n                    secondary: selectedComanda.note_capo_cantiere\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 604,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 603,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 611,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Data Creazione\",\n                  secondary: new Date(selectedComanda.data_creazione).toLocaleDateString('it-IT')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 613,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 612,\n                columnNumber: 19\n              }, this), selectedComanda.data_scadenza && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 620,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                  children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: \"Data Scadenza\",\n                    secondary: new Date(selectedComanda.data_scadenza).toLocaleDateString('it-IT')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 622,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 621,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 629,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Cavi Assegnati\",\n                  secondary: selectedComanda.numero_cavi_assegnati || 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 631,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 630,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 636,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Completamento\",\n                  secondary: `${(selectedComanda.percentuale_completamento || 0).toFixed(1)}%`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 638,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 637,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 546,\n              columnNumber: 17\n            }, this)\n          }, void 0, false) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              select: true,\n              label: \"Tipo Comanda\",\n              value: formData.tipo_comanda,\n              onChange: e => setFormData({\n                ...formData,\n                tipo_comanda: e.target.value\n              }),\n              margin: \"normal\",\n              disabled: dialogMode === 'view',\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"POSA\",\n                children: \"Posa\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 656,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"COLLEGAMENTO_PARTENZA\",\n                children: \"Collegamento Partenza\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 657,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"COLLEGAMENTO_ARRIVO\",\n                children: \"Collegamento Arrivo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 658,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"CERTIFICAZIONE\",\n                children: \"Certificazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 659,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"TESTING\",\n                children: \"Testing\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 660,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 647,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              select: true,\n              label: \"Priorit\\xE0\",\n              value: formData.priorita,\n              onChange: e => setFormData({\n                ...formData,\n                priorita: e.target.value\n              }),\n              margin: \"normal\",\n              disabled: dialogMode === 'view',\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"BASSA\",\n                children: \"Bassa\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 672,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"NORMALE\",\n                children: \"Normale\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 673,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"ALTA\",\n                children: \"Alta\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 674,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"URGENTE\",\n                children: \"Urgente\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 675,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 663,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Descrizione\",\n              value: formData.descrizione,\n              onChange: e => setFormData({\n                ...formData,\n                descrizione: e.target.value\n              }),\n              margin: \"normal\",\n              multiline: true,\n              rows: 3,\n              disabled: dialogMode === 'view'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 678,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Responsabile\",\n              value: formData.responsabile,\n              onChange: e => setFormData({\n                ...formData,\n                responsabile: e.target.value\n              }),\n              margin: \"normal\",\n              disabled: dialogMode === 'view',\n              required: true,\n              helperText: \"Chi eseguir\\xE0 il lavoro (obbligatorio)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 689,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Note Capo Cantiere\",\n              value: formData.note_capo_cantiere,\n              onChange: e => setFormData({\n                ...formData,\n                note_capo_cantiere: e.target.value\n              }),\n              margin: \"normal\",\n              multiline: true,\n              rows: 2,\n              disabled: dialogMode === 'view',\n              helperText: \"Istruzioni specifiche per il responsabile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 700,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Data Scadenza\",\n              type: \"date\",\n              value: formData.data_scadenza,\n              onChange: e => setFormData({\n                ...formData,\n                data_scadenza: e.target.value\n              }),\n              margin: \"normal\",\n              InputLabelProps: {\n                shrink: true\n              },\n              disabled: dialogMode === 'view'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 712,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 526,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 525,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseDialog,\n          children: dialogMode === 'view' ? 'Chiudi' : 'Annulla'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 727,\n          columnNumber: 11\n        }, this), dialogMode !== 'view' && /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSubmit,\n          variant: \"contained\",\n          children: dialogMode === 'edit' ? 'Salva' : dialogMode === 'assign' ? 'Assegna Cavi' : 'Salva'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 731,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 726,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 519,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CreaComandaConCavi, {\n      cantiereId: cantiereId,\n      open: openCreaConCavi,\n      onClose: () => setOpenCreaConCavi(false),\n      onSuccess: () => {\n        loadComande();\n        loadStatistiche();\n        setOpenCreaConCavi(false);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 740,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(GestioneResponsabili, {\n      open: openGestioneResponsabili,\n      onClose: () => setOpenGestioneResponsabili(false),\n      cantiereId: cantiereId\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 752,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 272,\n    columnNumber: 5\n  }, this);\n};\n_s(ComandeList, \"4sKIyp801Fmvwb83RoKeDo/plnQ=\");\n_c = ComandeList;\nexport default ComandeList;\nvar _c;\n$RefreshReg$(_c, \"ComandeList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "Chip", "IconButton", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "MenuItem", "<PERSON><PERSON>", "CircularProgress", "<PERSON><PERSON><PERSON>", "Grid", "List", "ListItem", "ListItemText", "Divider", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "Visibility", "ViewIcon", "Assignment", "AssignIcon", "Refresh", "RefreshIcon", "People", "PeopleIcon", "Person", "PersonIcon", "Email", "EmailIcon", "Phone", "PhoneIcon", "ExpandMore", "ExpandMoreIcon", "Accordion", "AccordionSummary", "AccordionDetails", "Tabs", "Tab", "comandeService", "CreaComandaConCavi", "responsabiliService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ComandeList", "cantiereId", "cantiereName", "_s", "comande", "setComande", "loading", "setLoading", "error", "setError", "openDialog", "setOpenDialog", "selectedComanda", "setSelectedComanda", "dialogMode", "setDialogMode", "formData", "setFormData", "tipo_comanda", "descrizione", "responsabile", "data_scadenza", "priorita", "note_capo_cantiere", "statistiche", "setStatistiche", "caviAssegnazione", "setCaviAssegnazione", "openCreaConCavi", "setOpenCreaConCavi", "responsabili", "setResponsabili", "loadingResponsabili", "setLoadingResponsabili", "comandePerResponsabile", "setComandePerResponsabile", "openResponsabileDialog", "setOpenResponsabileDialog", "dialogModeResponsabile", "setDialogModeResponsabile", "selectedResponsabile", "setSelectedResponsabile", "formDataResponsabile", "setFormDataResponsabile", "nome_responsabile", "email", "telefono", "activeTab", "setActiveTab", "loadComande", "loadStatistiche", "loadResponsabili", "response", "getComande", "err", "console", "stats", "getStatisticheComande", "data", "getResponsabiliCantiere", "loadComandePerResponsabili", "responsabiliList", "comandeMap", "getComandeByResponsabile", "id_responsabile", "handleOpenDialog", "mode", "comanda", "handleCloseDialog", "handleSubmit", "updateComanda", "codice_comanda", "log", "trim", "listaIdCavi", "split", "map", "id", "filter", "assegnaCavi", "handleDelete", "codiceComanda", "window", "confirm", "deleteComanda", "getStatoColor", "stato", "getTipoComandaLabel", "tipo", "getPrioritaColor", "display", "justifyContent", "alignItems", "minHeight", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "mb", "container", "spacing", "item", "xs", "sm", "md", "color", "gutterBottom", "variant", "totale_comande", "comande_create", "comande_assegnate", "comande_in_corso", "comande_completate", "percentuale_completamento_medio", "toFixed", "flexWrap", "gap", "startIcon", "onClick", "setOpenGestioneResponsabili", "length", "disabled", "severity", "sx", "component", "fontWeight", "label", "size", "Date", "data_creazione", "toLocaleDateString", "numero_cavi_assegnati", "percentuale_completamento", "title", "textAlign", "py", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "pt", "value", "onChange", "e", "target", "margin", "placeholder", "helperText", "multiline", "rows", "primary", "secondary", "select", "required", "type", "InputLabelProps", "shrink", "onSuccess", "GestioneResponsabili", "openGestioneResponsabili", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/comande/ComandeList.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Card,\n  CardContent,\n  Typo<PERSON>,\n  Button,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  Chip,\n  IconButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  MenuItem,\n  Alert,\n  CircularProgress,\n  Tooltip,\n  Grid,\n  List,\n  ListItem,\n  ListItemText,\n  Divider\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  Visibility as ViewIcon,\n  Assignment as AssignIcon,\n  Refresh as RefreshIcon,\n  People as PeopleIcon,\n  Person as PersonIcon,\n  Email as EmailIcon,\n  Phone as PhoneIcon,\n  ExpandMore as ExpandMoreIcon\n} from '@mui/icons-material';\nimport {\n  Accordion,\n  AccordionSummary,\n  AccordionDetails,\n  Tabs,\n  Tab\n} from '@mui/material';\nimport comandeService from '../../services/comandeService';\nimport CreaComandaConCavi from './CreaComandaConCavi';\nimport responsabiliService from '../../services/responsabiliService';\n\nconst ComandeList = ({ cantiereId, cantiereName }) => {\n  const [comande, setComande] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [selectedComanda, setSelectedComanda] = useState(null);\n  const [dialogMode, setDialogMode] = useState('edit'); // 'edit', 'view', 'assign'\n  const [formData, setFormData] = useState({\n    tipo_comanda: 'POSA',\n    descrizione: '',\n    responsabile: '',\n    data_scadenza: '',\n    priorita: 'NORMALE',\n    note_capo_cantiere: ''\n  });\n  const [statistiche, setStatistiche] = useState(null);\n  const [caviAssegnazione, setCaviAssegnazione] = useState('');\n  const [openCreaConCavi, setOpenCreaConCavi] = useState(false);\n\n  // Stati per gestione responsabili integrata\n  const [responsabili, setResponsabili] = useState([]);\n  const [loadingResponsabili, setLoadingResponsabili] = useState(false);\n  const [comandePerResponsabile, setComandePerResponsabile] = useState({});\n  const [openResponsabileDialog, setOpenResponsabileDialog] = useState(false);\n  const [dialogModeResponsabile, setDialogModeResponsabile] = useState('create');\n  const [selectedResponsabile, setSelectedResponsabile] = useState(null);\n  const [formDataResponsabile, setFormDataResponsabile] = useState({\n    nome_responsabile: '',\n    email: '',\n    telefono: ''\n  });\n\n  // Stato per le tabs\n  const [activeTab, setActiveTab] = useState(0);\n\n  // Carica le comande al mount del componente\n  useEffect(() => {\n    if (cantiereId) {\n      loadComande();\n      loadStatistiche();\n      loadResponsabili();\n    }\n  }, [cantiereId]);\n\n  const loadComande = async () => {\n    try {\n      setLoading(true);\n      const response = await comandeService.getComande(cantiereId);\n      setComande(response.comande || []);\n      setError(null);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n      setError('Errore nel caricamento delle comande');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadStatistiche = async () => {\n    try {\n      const stats = await comandeService.getStatisticheComande(cantiereId);\n      setStatistiche(stats);\n    } catch (err) {\n      console.error('Errore nel caricamento delle statistiche:', err);\n    }\n  };\n\n  const loadResponsabili = async () => {\n    try {\n      setLoadingResponsabili(true);\n      setError(null);\n      const data = await responsabiliService.getResponsabiliCantiere(cantiereId);\n      setResponsabili(data || []);\n\n      // Carica le comande per ogni responsabile\n      await loadComandePerResponsabili(data || []);\n    } catch (err) {\n      console.error('Errore nel caricamento dei responsabili:', err);\n      setError('Errore nel caricamento dei responsabili');\n    } finally {\n      setLoadingResponsabili(false);\n    }\n  };\n\n  const loadComandePerResponsabili = async (responsabiliList) => {\n    try {\n      const comandeMap = {};\n\n      for (const responsabile of responsabiliList) {\n        try {\n          const comande = await comandeService.getComandeByResponsabile(cantiereId, responsabile.nome_responsabile);\n          comandeMap[responsabile.id_responsabile] = comande || [];\n        } catch (err) {\n          console.error(`Errore nel caricamento comande per ${responsabile.nome_responsabile}:`, err);\n          comandeMap[responsabile.id_responsabile] = [];\n        }\n      }\n\n      setComandePerResponsabile(comandeMap);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n    }\n  };\n\n  const handleOpenDialog = (mode, comanda = null) => {\n    setDialogMode(mode);\n    setSelectedComanda(comanda);\n\n    if (mode === 'edit' && comanda) {\n      setFormData({\n        tipo_comanda: comanda.tipo_comanda,\n        descrizione: comanda.descrizione || '',\n        responsabile: comanda.responsabile || '',\n        data_scadenza: comanda.data_scadenza || '',\n        priorita: comanda.priorita || 'NORMALE',\n        note_capo_cantiere: comanda.note_capo_cantiere || ''\n      });\n    }\n\n    setOpenDialog(true);\n  };\n\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setSelectedComanda(null);\n    setCaviAssegnazione('');\n    setFormData({\n      tipo_comanda: 'POSA',\n      descrizione: '',\n      responsabile: '',\n      data_scadenza: '',\n      priorita: 'NORMALE',\n      note_capo_cantiere: ''\n    });\n  };\n\n  const handleSubmit = async () => {\n    try {\n      if (dialogMode === 'edit') {\n        // Modifica comanda esistente\n        const response = await comandeService.updateComanda(selectedComanda.codice_comanda, formData);\n        console.log('Comanda aggiornata:', response);\n      } else if (dialogMode === 'assign') {\n        // Assegnazione cavi\n        if (!caviAssegnazione.trim()) {\n          setError('Inserisci almeno un ID cavo');\n          return;\n        }\n\n        const listaIdCavi = caviAssegnazione.split(',').map(id => id.trim()).filter(id => id);\n        await comandeService.assegnaCavi(selectedComanda.codice_comanda, listaIdCavi);\n      }\n\n      handleCloseDialog();\n      loadComande();\n      loadStatistiche();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError('Errore nel salvataggio della comanda');\n    }\n  };\n\n  const handleDelete = async (codiceComanda) => {\n    if (window.confirm('Sei sicuro di voler eliminare questa comanda?')) {\n      try {\n        await comandeService.deleteComanda(codiceComanda);\n        loadComande();\n        loadStatistiche();\n      } catch (err) {\n        console.error('Errore nell\\'eliminazione:', err);\n        setError('Errore nell\\'eliminazione della comanda');\n      }\n    }\n  };\n\n  const getStatoColor = (stato) => {\n    switch (stato) {\n      case 'CREATA': return 'default';\n      case 'ASSEGNATA': return 'primary';\n      case 'IN_CORSO': return 'warning';\n      case 'COMPLETATA': return 'success';\n      case 'ANNULLATA': return 'error';\n      default: return 'default';\n    }\n  };\n\n  const getTipoComandaLabel = (tipo) => {\n    switch (tipo) {\n      case 'POSA': return 'Posa';\n      case 'COLLEGAMENTO_PARTENZA': return 'Collegamento Partenza';\n      case 'COLLEGAMENTO_ARRIVO': return 'Collegamento Arrivo';\n      case 'CERTIFICAZIONE': return 'Certificazione';\n      case 'TESTING': return 'Testing/Certificazione';\n      default: return tipo;\n    }\n  };\n\n  const getPrioritaColor = (priorita) => {\n    switch (priorita) {\n      case 'BASSA': return 'default';\n      case 'NORMALE': return 'primary';\n      case 'ALTA': return 'warning';\n      case 'URGENTE': return 'error';\n      default: return 'default';\n    }\n  };\n\n  if (loading) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"400px\">\n        <CircularProgress />\n      </Box>\n    );\n  }\n\n  return (\n    <Box>\n      {/* Header con statistiche */}\n      <Box mb={3}>\n        \n        {statistiche && (\n          <Grid container spacing={2} mb={2}>\n            <Grid item xs={12} sm={6} md={2}>\n              <Card>\n                <CardContent>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    Totale\n                  </Typography>\n                  <Typography variant=\"h5\">\n                    {statistiche.totale_comande}\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n            <Grid item xs={12} sm={6} md={2}>\n              <Card>\n                <CardContent>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    Create\n                  </Typography>\n                  <Typography variant=\"h5\">\n                    {statistiche.comande_create}\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n            <Grid item xs={12} sm={6} md={2}>\n              <Card>\n                <CardContent>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    Assegnate\n                  </Typography>\n                  <Typography variant=\"h5\">\n                    {statistiche.comande_assegnate}\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n            <Grid item xs={12} sm={6} md={2}>\n              <Card>\n                <CardContent>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    In Corso\n                  </Typography>\n                  <Typography variant=\"h5\">\n                    {statistiche.comande_in_corso}\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n            <Grid item xs={12} sm={6} md={2}>\n              <Card>\n                <CardContent>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    Completate\n                  </Typography>\n                  <Typography variant=\"h5\">\n                    {statistiche.comande_completate}\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n            <Grid item xs={12} sm={6} md={2}>\n              <Card>\n                <CardContent>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    % Completamento\n                  </Typography>\n                  <Typography variant=\"h5\">\n                    {statistiche.percentuale_completamento_medio.toFixed(1)}%\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n          </Grid>\n        )}\n      </Box>\n\n      {/* Toolbar */}\n      <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={2} flexWrap=\"wrap\" gap={1}>\n        <Box display=\"flex\" gap={1} flexWrap=\"wrap\">\n          <Button\n            variant=\"contained\"\n            startIcon={<AddIcon />}\n            onClick={() => setOpenCreaConCavi(true)}\n            color=\"primary\"\n          >\n            Nuova Comanda\n          </Button>\n\n          <Button\n            variant=\"outlined\"\n            startIcon={<PeopleIcon />}\n            onClick={() => setOpenGestioneResponsabili(true)}\n            color=\"secondary\"\n          >\n            Gestisci Responsabili\n          </Button>\n\n          <Button\n            variant=\"outlined\"\n            startIcon={<AssignIcon />}\n            onClick={() => {\n              if (comande.length === 0) {\n                setError('Nessuna comanda disponibile per l\\'assegnazione');\n                return;\n              }\n              // Apri dialog per selezionare comanda\n              setError('Seleziona una comanda dalla tabella e clicca sull\\'icona \"Assegna Cavi\"');\n            }}\n            disabled={comande.length === 0}\n          >\n            Assegna Cavi\n          </Button>\n        </Box>\n\n        <Button\n          variant=\"outlined\"\n          startIcon={<RefreshIcon />}\n          onClick={() => {\n            loadComande();\n            loadStatistiche();\n          }}\n        >\n          Aggiorna\n        </Button>\n      </Box>\n\n      {/* Messaggio di errore */}\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          {error}\n        </Alert>\n      )}\n\n      {/* Tabella comande */}\n      <TableContainer component={Paper}>\n        <Table>\n          <TableHead>\n            <TableRow>\n              <TableCell>Codice</TableCell>\n              <TableCell>Tipo</TableCell>\n              <TableCell>Priorità</TableCell>\n              <TableCell>Responsabile</TableCell>\n              <TableCell>Data Creazione</TableCell>\n              <TableCell>Stato</TableCell>\n              <TableCell>Cavi Assegnati</TableCell>\n              <TableCell>Completamento</TableCell>\n              <TableCell>Azioni</TableCell>\n            </TableRow>\n          </TableHead>\n          <TableBody>\n            {comande.map((comanda) => (\n              <TableRow key={comanda.codice_comanda}>\n                <TableCell>\n                  <Typography variant=\"body2\" fontWeight=\"bold\">\n                    {comanda.codice_comanda}\n                  </Typography>\n                </TableCell>\n                <TableCell>\n                  <Chip\n                    label={getTipoComandaLabel(comanda.tipo_comanda)}\n                    size=\"small\"\n                    variant=\"outlined\"\n                  />\n                </TableCell>\n                <TableCell>\n                  <Chip\n                    label={comanda.priorita || 'NORMALE'}\n                    size=\"small\"\n                    color={getPrioritaColor(comanda.priorita || 'NORMALE')}\n                  />\n                </TableCell>\n                <TableCell>{comanda.responsabile}</TableCell>\n                <TableCell>\n                  {new Date(comanda.data_creazione).toLocaleDateString('it-IT')}\n                </TableCell>\n                <TableCell>\n                  <Chip \n                    label={comanda.stato}\n                    color={getStatoColor(comanda.stato)}\n                    size=\"small\"\n                  />\n                </TableCell>\n                <TableCell>{comanda.numero_cavi_assegnati || 0}</TableCell>\n                <TableCell>\n                  {comanda.percentuale_completamento ? \n                    `${comanda.percentuale_completamento.toFixed(1)}%` : '0%'}\n                </TableCell>\n                <TableCell>\n                  <Tooltip title=\"Visualizza\">\n                    <IconButton \n                      size=\"small\"\n                      onClick={() => handleOpenDialog('view', comanda)}\n                    >\n                      <ViewIcon />\n                    </IconButton>\n                  </Tooltip>\n                  <Tooltip title=\"Modifica\">\n                    <IconButton \n                      size=\"small\"\n                      onClick={() => handleOpenDialog('edit', comanda)}\n                    >\n                      <EditIcon />\n                    </IconButton>\n                  </Tooltip>\n                  <Tooltip title=\"Assegna Cavi\">\n                    <IconButton\n                      size=\"small\"\n                      onClick={() => handleOpenDialog('assign', comanda)}\n                      color=\"primary\"\n                    >\n                      <AssignIcon />\n                    </IconButton>\n                  </Tooltip>\n                  <Tooltip title=\"Elimina\">\n                    <IconButton\n                      size=\"small\"\n                      onClick={() => handleDelete(comanda.codice_comanda)}\n                      color=\"error\"\n                    >\n                      <DeleteIcon />\n                    </IconButton>\n                  </Tooltip>\n                </TableCell>\n              </TableRow>\n            ))}\n          </TableBody>\n        </Table>\n      </TableContainer>\n\n      {comande.length === 0 && !loading && (\n        <Box textAlign=\"center\" py={4}>\n          <Typography variant=\"h6\" color=\"textSecondary\">\n            Nessuna comanda trovata\n          </Typography>\n          <Typography variant=\"body2\" color=\"textSecondary\">\n            Clicca su \"Nuova Comanda\" per iniziare\n          </Typography>\n        </Box>\n      )}\n\n      {/* Dialog per modifica/assegnazione */}\n      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n        <DialogTitle>\n          {dialogMode === 'edit' && 'Modifica Comanda'}\n          {dialogMode === 'view' && 'Dettagli Comanda'}\n          {dialogMode === 'assign' && `Assegna Cavi - ${selectedComanda?.codice_comanda}`}\n        </DialogTitle>\n        <DialogContent>\n          <Box sx={{ pt: 1 }}>\n            {dialogMode === 'assign' ? (\n              <>\n                <Alert severity=\"info\" sx={{ mb: 2 }}>\n                  Inserisci gli ID dei cavi da assegnare alla comanda, separati da virgola.\n                </Alert>\n                <TextField\n                  fullWidth\n                  label=\"ID Cavi (separati da virgola)\"\n                  value={caviAssegnazione}\n                  onChange={(e) => setCaviAssegnazione(e.target.value)}\n                  margin=\"normal\"\n                  placeholder=\"es: CAVO001, CAVO002, CAVO003\"\n                  helperText=\"Esempio: CAVO001, CAVO002, CAVO003\"\n                  multiline\n                  rows={3}\n                />\n              </>\n            ) : dialogMode === 'view' && selectedComanda ? (\n              <>\n                <List>\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Codice Comanda\"\n                      secondary={selectedComanda.codice_comanda}\n                    />\n                  </ListItem>\n                  <Divider />\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Tipo\"\n                      secondary={getTipoComandaLabel(selectedComanda.tipo_comanda)}\n                    />\n                  </ListItem>\n                  <Divider />\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Stato\"\n                      secondary={\n                        <Chip\n                          label={selectedComanda.stato}\n                          color={getStatoColor(selectedComanda.stato)}\n                          size=\"small\"\n                        />\n                      }\n                    />\n                  </ListItem>\n                  <Divider />\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Descrizione\"\n                      secondary={selectedComanda.descrizione || 'Nessuna descrizione'}\n                    />\n                  </ListItem>\n                  <Divider />\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Priorità\"\n                      secondary={\n                        <Chip\n                          label={selectedComanda.priorita || 'NORMALE'}\n                          color={getPrioritaColor(selectedComanda.priorita || 'NORMALE')}\n                          size=\"small\"\n                        />\n                      }\n                    />\n                  </ListItem>\n                  <Divider />\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Responsabile\"\n                      secondary={selectedComanda.responsabile || 'Non assegnato'}\n                    />\n                  </ListItem>\n                  {selectedComanda.note_capo_cantiere && (\n                    <>\n                      <Divider />\n                      <ListItem>\n                        <ListItemText\n                          primary=\"Note Capo Cantiere\"\n                          secondary={selectedComanda.note_capo_cantiere}\n                        />\n                      </ListItem>\n                    </>\n                  )}\n                  <Divider />\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Data Creazione\"\n                      secondary={new Date(selectedComanda.data_creazione).toLocaleDateString('it-IT')}\n                    />\n                  </ListItem>\n                  {selectedComanda.data_scadenza && (\n                    <>\n                      <Divider />\n                      <ListItem>\n                        <ListItemText\n                          primary=\"Data Scadenza\"\n                          secondary={new Date(selectedComanda.data_scadenza).toLocaleDateString('it-IT')}\n                        />\n                      </ListItem>\n                    </>\n                  )}\n                  <Divider />\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Cavi Assegnati\"\n                      secondary={selectedComanda.numero_cavi_assegnati || 0}\n                    />\n                  </ListItem>\n                  <Divider />\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Completamento\"\n                      secondary={`${(selectedComanda.percentuale_completamento || 0).toFixed(1)}%`}\n                    />\n                  </ListItem>\n                </List>\n              </>\n            ) : (\n              <>\n                <TextField\n                  fullWidth\n                  select\n                  label=\"Tipo Comanda\"\n                  value={formData.tipo_comanda}\n                  onChange={(e) => setFormData({ ...formData, tipo_comanda: e.target.value })}\n                  margin=\"normal\"\n                  disabled={dialogMode === 'view'}\n                >\n                  <MenuItem value=\"POSA\">Posa</MenuItem>\n                  <MenuItem value=\"COLLEGAMENTO_PARTENZA\">Collegamento Partenza</MenuItem>\n                  <MenuItem value=\"COLLEGAMENTO_ARRIVO\">Collegamento Arrivo</MenuItem>\n                  <MenuItem value=\"CERTIFICAZIONE\">Certificazione</MenuItem>\n                  <MenuItem value=\"TESTING\">Testing</MenuItem>\n                </TextField>\n\n                <TextField\n                  fullWidth\n                  select\n                  label=\"Priorità\"\n                  value={formData.priorita}\n                  onChange={(e) => setFormData({ ...formData, priorita: e.target.value })}\n                  margin=\"normal\"\n                  disabled={dialogMode === 'view'}\n                >\n                  <MenuItem value=\"BASSA\">Bassa</MenuItem>\n                  <MenuItem value=\"NORMALE\">Normale</MenuItem>\n                  <MenuItem value=\"ALTA\">Alta</MenuItem>\n                  <MenuItem value=\"URGENTE\">Urgente</MenuItem>\n                </TextField>\n\n                <TextField\n                  fullWidth\n                  label=\"Descrizione\"\n                  value={formData.descrizione}\n                  onChange={(e) => setFormData({ ...formData, descrizione: e.target.value })}\n                  margin=\"normal\"\n                  multiline\n                  rows={3}\n                  disabled={dialogMode === 'view'}\n                />\n\n                <TextField\n                  fullWidth\n                  label=\"Responsabile\"\n                  value={formData.responsabile}\n                  onChange={(e) => setFormData({ ...formData, responsabile: e.target.value })}\n                  margin=\"normal\"\n                  disabled={dialogMode === 'view'}\n                  required\n                  helperText=\"Chi eseguirà il lavoro (obbligatorio)\"\n                />\n\n                <TextField\n                  fullWidth\n                  label=\"Note Capo Cantiere\"\n                  value={formData.note_capo_cantiere}\n                  onChange={(e) => setFormData({ ...formData, note_capo_cantiere: e.target.value })}\n                  margin=\"normal\"\n                  multiline\n                  rows={2}\n                  disabled={dialogMode === 'view'}\n                  helperText=\"Istruzioni specifiche per il responsabile\"\n                />\n\n                <TextField\n                  fullWidth\n                  label=\"Data Scadenza\"\n                  type=\"date\"\n                  value={formData.data_scadenza}\n                  onChange={(e) => setFormData({ ...formData, data_scadenza: e.target.value })}\n                  margin=\"normal\"\n                  InputLabelProps={{ shrink: true }}\n                  disabled={dialogMode === 'view'}\n                />\n              </>\n            )}\n          </Box>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handleCloseDialog}>\n            {dialogMode === 'view' ? 'Chiudi' : 'Annulla'}\n          </Button>\n          {dialogMode !== 'view' && (\n            <Button onClick={handleSubmit} variant=\"contained\">\n              {dialogMode === 'edit' ? 'Salva' :\n               dialogMode === 'assign' ? 'Assegna Cavi' : 'Salva'}\n            </Button>\n          )}\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog per creazione comanda con cavi */}\n      <CreaComandaConCavi\n        cantiereId={cantiereId}\n        open={openCreaConCavi}\n        onClose={() => setOpenCreaConCavi(false)}\n        onSuccess={() => {\n          loadComande();\n          loadStatistiche();\n          setOpenCreaConCavi(false);\n        }}\n      />\n\n      {/* Dialog Gestione Responsabili */}\n      <GestioneResponsabili\n        open={openGestioneResponsabili}\n        onClose={() => setOpenGestioneResponsabili(false)}\n        cantiereId={cantiereId}\n      />\n    </Box>\n  );\n};\n\nexport default ComandeList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,gBAAgB,EAChBC,OAAO,EACPC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,OAAO,QACF,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,UAAU,IAAIC,QAAQ,EACtBC,UAAU,IAAIC,UAAU,EACxBC,OAAO,IAAIC,WAAW,EACtBC,MAAM,IAAIC,UAAU,EACpBC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,KAAK,IAAIC,SAAS,EAClBC,UAAU,IAAIC,cAAc,QACvB,qBAAqB;AAC5B,SACEC,SAAS,EACTC,gBAAgB,EAChBC,gBAAgB,EAChBC,IAAI,EACJC,GAAG,QACE,eAAe;AACtB,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,mBAAmB,MAAM,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErE,MAAMC,WAAW,GAAGA,CAAC;EAAEC,UAAU;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACpD,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGrE,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACsE,OAAO,EAAEC,UAAU,CAAC,GAAGvE,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwE,KAAK,EAAEC,QAAQ,CAAC,GAAGzE,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC0E,UAAU,EAAEC,aAAa,CAAC,GAAG3E,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC4E,eAAe,EAAEC,kBAAkB,CAAC,GAAG7E,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC8E,UAAU,EAAEC,aAAa,CAAC,GAAG/E,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;EACtD,MAAM,CAACgF,QAAQ,EAAEC,WAAW,CAAC,GAAGjF,QAAQ,CAAC;IACvCkF,YAAY,EAAE,MAAM;IACpBC,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE,EAAE;IACjBC,QAAQ,EAAE,SAAS;IACnBC,kBAAkB,EAAE;EACtB,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGzF,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC0F,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3F,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC4F,eAAe,EAAEC,kBAAkB,CAAC,GAAG7F,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAM,CAAC8F,YAAY,EAAEC,eAAe,CAAC,GAAG/F,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACgG,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGjG,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACkG,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGnG,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxE,MAAM,CAACoG,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGrG,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAACsG,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGvG,QAAQ,CAAC,QAAQ,CAAC;EAC9E,MAAM,CAACwG,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGzG,QAAQ,CAAC,IAAI,CAAC;EACtE,MAAM,CAAC0G,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG3G,QAAQ,CAAC;IAC/D4G,iBAAiB,EAAE,EAAE;IACrBC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGhH,QAAQ,CAAC,CAAC,CAAC;;EAE7C;EACAC,SAAS,CAAC,MAAM;IACd,IAAIgE,UAAU,EAAE;MACdgD,WAAW,CAAC,CAAC;MACbC,eAAe,CAAC,CAAC;MACjBC,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAAClD,UAAU,CAAC,CAAC;EAEhB,MAAMgD,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF1C,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM6C,QAAQ,GAAG,MAAM3D,cAAc,CAAC4D,UAAU,CAACpD,UAAU,CAAC;MAC5DI,UAAU,CAAC+C,QAAQ,CAAChD,OAAO,IAAI,EAAE,CAAC;MAClCK,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAO6C,GAAG,EAAE;MACZC,OAAO,CAAC/C,KAAK,CAAC,uCAAuC,EAAE8C,GAAG,CAAC;MAC3D7C,QAAQ,CAAC,sCAAsC,CAAC;IAClD,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM2C,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMM,KAAK,GAAG,MAAM/D,cAAc,CAACgE,qBAAqB,CAACxD,UAAU,CAAC;MACpEwB,cAAc,CAAC+B,KAAK,CAAC;IACvB,CAAC,CAAC,OAAOF,GAAG,EAAE;MACZC,OAAO,CAAC/C,KAAK,CAAC,2CAA2C,EAAE8C,GAAG,CAAC;IACjE;EACF,CAAC;EAED,MAAMH,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFlB,sBAAsB,CAAC,IAAI,CAAC;MAC5BxB,QAAQ,CAAC,IAAI,CAAC;MACd,MAAMiD,IAAI,GAAG,MAAM/D,mBAAmB,CAACgE,uBAAuB,CAAC1D,UAAU,CAAC;MAC1E8B,eAAe,CAAC2B,IAAI,IAAI,EAAE,CAAC;;MAE3B;MACA,MAAME,0BAA0B,CAACF,IAAI,IAAI,EAAE,CAAC;IAC9C,CAAC,CAAC,OAAOJ,GAAG,EAAE;MACZC,OAAO,CAAC/C,KAAK,CAAC,0CAA0C,EAAE8C,GAAG,CAAC;MAC9D7C,QAAQ,CAAC,yCAAyC,CAAC;IACrD,CAAC,SAAS;MACRwB,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;EAED,MAAM2B,0BAA0B,GAAG,MAAOC,gBAAgB,IAAK;IAC7D,IAAI;MACF,MAAMC,UAAU,GAAG,CAAC,CAAC;MAErB,KAAK,MAAM1C,YAAY,IAAIyC,gBAAgB,EAAE;QAC3C,IAAI;UACF,MAAMzD,OAAO,GAAG,MAAMX,cAAc,CAACsE,wBAAwB,CAAC9D,UAAU,EAAEmB,YAAY,CAACwB,iBAAiB,CAAC;UACzGkB,UAAU,CAAC1C,YAAY,CAAC4C,eAAe,CAAC,GAAG5D,OAAO,IAAI,EAAE;QAC1D,CAAC,CAAC,OAAOkD,GAAG,EAAE;UACZC,OAAO,CAAC/C,KAAK,CAAC,sCAAsCY,YAAY,CAACwB,iBAAiB,GAAG,EAAEU,GAAG,CAAC;UAC3FQ,UAAU,CAAC1C,YAAY,CAAC4C,eAAe,CAAC,GAAG,EAAE;QAC/C;MACF;MAEA7B,yBAAyB,CAAC2B,UAAU,CAAC;IACvC,CAAC,CAAC,OAAOR,GAAG,EAAE;MACZC,OAAO,CAAC/C,KAAK,CAAC,uCAAuC,EAAE8C,GAAG,CAAC;IAC7D;EACF,CAAC;EAED,MAAMW,gBAAgB,GAAGA,CAACC,IAAI,EAAEC,OAAO,GAAG,IAAI,KAAK;IACjDpD,aAAa,CAACmD,IAAI,CAAC;IACnBrD,kBAAkB,CAACsD,OAAO,CAAC;IAE3B,IAAID,IAAI,KAAK,MAAM,IAAIC,OAAO,EAAE;MAC9BlD,WAAW,CAAC;QACVC,YAAY,EAAEiD,OAAO,CAACjD,YAAY;QAClCC,WAAW,EAAEgD,OAAO,CAAChD,WAAW,IAAI,EAAE;QACtCC,YAAY,EAAE+C,OAAO,CAAC/C,YAAY,IAAI,EAAE;QACxCC,aAAa,EAAE8C,OAAO,CAAC9C,aAAa,IAAI,EAAE;QAC1CC,QAAQ,EAAE6C,OAAO,CAAC7C,QAAQ,IAAI,SAAS;QACvCC,kBAAkB,EAAE4C,OAAO,CAAC5C,kBAAkB,IAAI;MACpD,CAAC,CAAC;IACJ;IAEAZ,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMyD,iBAAiB,GAAGA,CAAA,KAAM;IAC9BzD,aAAa,CAAC,KAAK,CAAC;IACpBE,kBAAkB,CAAC,IAAI,CAAC;IACxBc,mBAAmB,CAAC,EAAE,CAAC;IACvBV,WAAW,CAAC;MACVC,YAAY,EAAE,MAAM;MACpBC,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,EAAE;MACjBC,QAAQ,EAAE,SAAS;MACnBC,kBAAkB,EAAE;IACtB,CAAC,CAAC;EACJ,CAAC;EAED,MAAM8C,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,IAAIvD,UAAU,KAAK,MAAM,EAAE;QACzB;QACA,MAAMsC,QAAQ,GAAG,MAAM3D,cAAc,CAAC6E,aAAa,CAAC1D,eAAe,CAAC2D,cAAc,EAAEvD,QAAQ,CAAC;QAC7FuC,OAAO,CAACiB,GAAG,CAAC,qBAAqB,EAAEpB,QAAQ,CAAC;MAC9C,CAAC,MAAM,IAAItC,UAAU,KAAK,QAAQ,EAAE;QAClC;QACA,IAAI,CAACY,gBAAgB,CAAC+C,IAAI,CAAC,CAAC,EAAE;UAC5BhE,QAAQ,CAAC,6BAA6B,CAAC;UACvC;QACF;QAEA,MAAMiE,WAAW,GAAGhD,gBAAgB,CAACiD,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,EAAE,IAAIA,EAAE,CAACJ,IAAI,CAAC,CAAC,CAAC,CAACK,MAAM,CAACD,EAAE,IAAIA,EAAE,CAAC;QACrF,MAAMpF,cAAc,CAACsF,WAAW,CAACnE,eAAe,CAAC2D,cAAc,EAAEG,WAAW,CAAC;MAC/E;MAEAN,iBAAiB,CAAC,CAAC;MACnBnB,WAAW,CAAC,CAAC;MACbC,eAAe,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZC,OAAO,CAAC/C,KAAK,CAAC,yBAAyB,EAAE8C,GAAG,CAAC;MAC7C7C,QAAQ,CAAC,sCAAsC,CAAC;IAClD;EACF,CAAC;EAED,MAAMuE,YAAY,GAAG,MAAOC,aAAa,IAAK;IAC5C,IAAIC,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC,EAAE;MACnE,IAAI;QACF,MAAM1F,cAAc,CAAC2F,aAAa,CAACH,aAAa,CAAC;QACjDhC,WAAW,CAAC,CAAC;QACbC,eAAe,CAAC,CAAC;MACnB,CAAC,CAAC,OAAOI,GAAG,EAAE;QACZC,OAAO,CAAC/C,KAAK,CAAC,4BAA4B,EAAE8C,GAAG,CAAC;QAChD7C,QAAQ,CAAC,yCAAyC,CAAC;MACrD;IACF;EACF,CAAC;EAED,MAAM4E,aAAa,GAAIC,KAAK,IAAK;IAC/B,QAAQA,KAAK;MACX,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,WAAW;QAAE,OAAO,SAAS;MAClC,KAAK,UAAU;QAAE,OAAO,SAAS;MACjC,KAAK,YAAY;QAAE,OAAO,SAAS;MACnC,KAAK,WAAW;QAAE,OAAO,OAAO;MAChC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMC,mBAAmB,GAAIC,IAAI,IAAK;IACpC,QAAQA,IAAI;MACV,KAAK,MAAM;QAAE,OAAO,MAAM;MAC1B,KAAK,uBAAuB;QAAE,OAAO,uBAAuB;MAC5D,KAAK,qBAAqB;QAAE,OAAO,qBAAqB;MACxD,KAAK,gBAAgB;QAAE,OAAO,gBAAgB;MAC9C,KAAK,SAAS;QAAE,OAAO,wBAAwB;MAC/C;QAAS,OAAOA,IAAI;IACtB;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAInE,QAAQ,IAAK;IACrC,QAAQA,QAAQ;MACd,KAAK,OAAO;QAAE,OAAO,SAAS;MAC9B,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,MAAM;QAAE,OAAO,SAAS;MAC7B,KAAK,SAAS;QAAE,OAAO,OAAO;MAC9B;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,IAAIhB,OAAO,EAAE;IACX,oBACET,OAAA,CAAC3D,GAAG;MAACwJ,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,QAAQ;MAACC,UAAU,EAAC,QAAQ;MAACC,SAAS,EAAC,OAAO;MAAAC,QAAA,eAC/EjG,OAAA,CAACtC,gBAAgB;QAAAwI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC;EAEV;EAEA,oBACErG,OAAA,CAAC3D,GAAG;IAAA4J,QAAA,gBAEFjG,OAAA,CAAC3D,GAAG;MAACiK,EAAE,EAAE,CAAE;MAAAL,QAAA,EAERtE,WAAW,iBACV3B,OAAA,CAACpC,IAAI;QAAC2I,SAAS;QAACC,OAAO,EAAE,CAAE;QAACF,EAAE,EAAE,CAAE;QAAAL,QAAA,gBAChCjG,OAAA,CAACpC,IAAI;UAAC6I,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAX,QAAA,eAC9BjG,OAAA,CAAC1D,IAAI;YAAA2J,QAAA,eACHjG,OAAA,CAACzD,WAAW;cAAA0J,QAAA,gBACVjG,OAAA,CAACxD,UAAU;gBAACqK,KAAK,EAAC,eAAe;gBAACC,YAAY;gBAAAb,QAAA,EAAC;cAE/C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbrG,OAAA,CAACxD,UAAU;gBAACuK,OAAO,EAAC,IAAI;gBAAAd,QAAA,EACrBtE,WAAW,CAACqF;cAAc;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACPrG,OAAA,CAACpC,IAAI;UAAC6I,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAX,QAAA,eAC9BjG,OAAA,CAAC1D,IAAI;YAAA2J,QAAA,eACHjG,OAAA,CAACzD,WAAW;cAAA0J,QAAA,gBACVjG,OAAA,CAACxD,UAAU;gBAACqK,KAAK,EAAC,eAAe;gBAACC,YAAY;gBAAAb,QAAA,EAAC;cAE/C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbrG,OAAA,CAACxD,UAAU;gBAACuK,OAAO,EAAC,IAAI;gBAAAd,QAAA,EACrBtE,WAAW,CAACsF;cAAc;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACPrG,OAAA,CAACpC,IAAI;UAAC6I,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAX,QAAA,eAC9BjG,OAAA,CAAC1D,IAAI;YAAA2J,QAAA,eACHjG,OAAA,CAACzD,WAAW;cAAA0J,QAAA,gBACVjG,OAAA,CAACxD,UAAU;gBAACqK,KAAK,EAAC,eAAe;gBAACC,YAAY;gBAAAb,QAAA,EAAC;cAE/C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbrG,OAAA,CAACxD,UAAU;gBAACuK,OAAO,EAAC,IAAI;gBAAAd,QAAA,EACrBtE,WAAW,CAACuF;cAAiB;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACPrG,OAAA,CAACpC,IAAI;UAAC6I,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAX,QAAA,eAC9BjG,OAAA,CAAC1D,IAAI;YAAA2J,QAAA,eACHjG,OAAA,CAACzD,WAAW;cAAA0J,QAAA,gBACVjG,OAAA,CAACxD,UAAU;gBAACqK,KAAK,EAAC,eAAe;gBAACC,YAAY;gBAAAb,QAAA,EAAC;cAE/C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbrG,OAAA,CAACxD,UAAU;gBAACuK,OAAO,EAAC,IAAI;gBAAAd,QAAA,EACrBtE,WAAW,CAACwF;cAAgB;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACPrG,OAAA,CAACpC,IAAI;UAAC6I,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAX,QAAA,eAC9BjG,OAAA,CAAC1D,IAAI;YAAA2J,QAAA,eACHjG,OAAA,CAACzD,WAAW;cAAA0J,QAAA,gBACVjG,OAAA,CAACxD,UAAU;gBAACqK,KAAK,EAAC,eAAe;gBAACC,YAAY;gBAAAb,QAAA,EAAC;cAE/C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbrG,OAAA,CAACxD,UAAU;gBAACuK,OAAO,EAAC,IAAI;gBAAAd,QAAA,EACrBtE,WAAW,CAACyF;cAAkB;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACPrG,OAAA,CAACpC,IAAI;UAAC6I,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAX,QAAA,eAC9BjG,OAAA,CAAC1D,IAAI;YAAA2J,QAAA,eACHjG,OAAA,CAACzD,WAAW;cAAA0J,QAAA,gBACVjG,OAAA,CAACxD,UAAU;gBAACqK,KAAK,EAAC,eAAe;gBAACC,YAAY;gBAAAb,QAAA,EAAC;cAE/C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbrG,OAAA,CAACxD,UAAU;gBAACuK,OAAO,EAAC,IAAI;gBAAAd,QAAA,GACrBtE,WAAW,CAAC0F,+BAA+B,CAACC,OAAO,CAAC,CAAC,CAAC,EAAC,GAC1D;cAAA;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACP;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNrG,OAAA,CAAC3D,GAAG;MAACwJ,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,eAAe;MAACC,UAAU,EAAC,QAAQ;MAACO,EAAE,EAAE,CAAE;MAACiB,QAAQ,EAAC,MAAM;MAACC,GAAG,EAAE,CAAE;MAAAvB,QAAA,gBACnGjG,OAAA,CAAC3D,GAAG;QAACwJ,OAAO,EAAC,MAAM;QAAC2B,GAAG,EAAE,CAAE;QAACD,QAAQ,EAAC,MAAM;QAAAtB,QAAA,gBACzCjG,OAAA,CAACvD,MAAM;UACLsK,OAAO,EAAC,WAAW;UACnBU,SAAS,eAAEzH,OAAA,CAAC9B,OAAO;YAAAgI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBqB,OAAO,EAAEA,CAAA,KAAM1F,kBAAkB,CAAC,IAAI,CAAE;UACxC6E,KAAK,EAAC,SAAS;UAAAZ,QAAA,EAChB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETrG,OAAA,CAACvD,MAAM;UACLsK,OAAO,EAAC,UAAU;UAClBU,SAAS,eAAEzH,OAAA,CAAClB,UAAU;YAAAoH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1BqB,OAAO,EAAEA,CAAA,KAAMC,2BAA2B,CAAC,IAAI,CAAE;UACjDd,KAAK,EAAC,WAAW;UAAAZ,QAAA,EAClB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETrG,OAAA,CAACvD,MAAM;UACLsK,OAAO,EAAC,UAAU;UAClBU,SAAS,eAAEzH,OAAA,CAACtB,UAAU;YAAAwH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1BqB,OAAO,EAAEA,CAAA,KAAM;YACb,IAAInH,OAAO,CAACqH,MAAM,KAAK,CAAC,EAAE;cACxBhH,QAAQ,CAAC,iDAAiD,CAAC;cAC3D;YACF;YACA;YACAA,QAAQ,CAAC,yEAAyE,CAAC;UACrF,CAAE;UACFiH,QAAQ,EAAEtH,OAAO,CAACqH,MAAM,KAAK,CAAE;UAAA3B,QAAA,EAChC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENrG,OAAA,CAACvD,MAAM;QACLsK,OAAO,EAAC,UAAU;QAClBU,SAAS,eAAEzH,OAAA,CAACpB,WAAW;UAAAsH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC3BqB,OAAO,EAAEA,CAAA,KAAM;UACbtE,WAAW,CAAC,CAAC;UACbC,eAAe,CAAC,CAAC;QACnB,CAAE;QAAA4C,QAAA,EACH;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAGL1F,KAAK,iBACJX,OAAA,CAACvC,KAAK;MAACqK,QAAQ,EAAC,OAAO;MAACC,EAAE,EAAE;QAAEzB,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,EACnCtF;IAAK;MAAAuF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,eAGDrG,OAAA,CAACnD,cAAc;MAACmL,SAAS,EAAEhL,KAAM;MAAAiJ,QAAA,eAC/BjG,OAAA,CAACtD,KAAK;QAAAuJ,QAAA,gBACJjG,OAAA,CAAClD,SAAS;UAAAmJ,QAAA,eACRjG,OAAA,CAACjD,QAAQ;YAAAkJ,QAAA,gBACPjG,OAAA,CAACpD,SAAS;cAAAqJ,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC7BrG,OAAA,CAACpD,SAAS;cAAAqJ,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC3BrG,OAAA,CAACpD,SAAS;cAAAqJ,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC/BrG,OAAA,CAACpD,SAAS;cAAAqJ,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACnCrG,OAAA,CAACpD,SAAS;cAAAqJ,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACrCrG,OAAA,CAACpD,SAAS;cAAAqJ,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC5BrG,OAAA,CAACpD,SAAS;cAAAqJ,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACrCrG,OAAA,CAACpD,SAAS;cAAAqJ,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACpCrG,OAAA,CAACpD,SAAS;cAAAqJ,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZrG,OAAA,CAACrD,SAAS;UAAAsJ,QAAA,EACP1F,OAAO,CAACwE,GAAG,CAAET,OAAO,iBACnBtE,OAAA,CAACjD,QAAQ;YAAAkJ,QAAA,gBACPjG,OAAA,CAACpD,SAAS;cAAAqJ,QAAA,eACRjG,OAAA,CAACxD,UAAU;gBAACuK,OAAO,EAAC,OAAO;gBAACkB,UAAU,EAAC,MAAM;gBAAAhC,QAAA,EAC1C3B,OAAO,CAACI;cAAc;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACZrG,OAAA,CAACpD,SAAS;cAAAqJ,QAAA,eACRjG,OAAA,CAAC/C,IAAI;gBACHiL,KAAK,EAAExC,mBAAmB,CAACpB,OAAO,CAACjD,YAAY,CAAE;gBACjD8G,IAAI,EAAC,OAAO;gBACZpB,OAAO,EAAC;cAAU;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eACZrG,OAAA,CAACpD,SAAS;cAAAqJ,QAAA,eACRjG,OAAA,CAAC/C,IAAI;gBACHiL,KAAK,EAAE5D,OAAO,CAAC7C,QAAQ,IAAI,SAAU;gBACrC0G,IAAI,EAAC,OAAO;gBACZtB,KAAK,EAAEjB,gBAAgB,CAACtB,OAAO,CAAC7C,QAAQ,IAAI,SAAS;cAAE;gBAAAyE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eACZrG,OAAA,CAACpD,SAAS;cAAAqJ,QAAA,EAAE3B,OAAO,CAAC/C;YAAY;cAAA2E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC7CrG,OAAA,CAACpD,SAAS;cAAAqJ,QAAA,EACP,IAAImC,IAAI,CAAC9D,OAAO,CAAC+D,cAAc,CAAC,CAACC,kBAAkB,CAAC,OAAO;YAAC;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eACZrG,OAAA,CAACpD,SAAS;cAAAqJ,QAAA,eACRjG,OAAA,CAAC/C,IAAI;gBACHiL,KAAK,EAAE5D,OAAO,CAACmB,KAAM;gBACrBoB,KAAK,EAAErB,aAAa,CAAClB,OAAO,CAACmB,KAAK,CAAE;gBACpC0C,IAAI,EAAC;cAAO;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eACZrG,OAAA,CAACpD,SAAS;cAAAqJ,QAAA,EAAE3B,OAAO,CAACiE,qBAAqB,IAAI;YAAC;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC3DrG,OAAA,CAACpD,SAAS;cAAAqJ,QAAA,EACP3B,OAAO,CAACkE,yBAAyB,GAChC,GAAGlE,OAAO,CAACkE,yBAAyB,CAAClB,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;YAAI;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eACZrG,OAAA,CAACpD,SAAS;cAAAqJ,QAAA,gBACRjG,OAAA,CAACrC,OAAO;gBAAC8K,KAAK,EAAC,YAAY;gBAAAxC,QAAA,eACzBjG,OAAA,CAAC9C,UAAU;kBACTiL,IAAI,EAAC,OAAO;kBACZT,OAAO,EAAEA,CAAA,KAAMtD,gBAAgB,CAAC,MAAM,EAAEE,OAAO,CAAE;kBAAA2B,QAAA,eAEjDjG,OAAA,CAACxB,QAAQ;oBAAA0H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACVrG,OAAA,CAACrC,OAAO;gBAAC8K,KAAK,EAAC,UAAU;gBAAAxC,QAAA,eACvBjG,OAAA,CAAC9C,UAAU;kBACTiL,IAAI,EAAC,OAAO;kBACZT,OAAO,EAAEA,CAAA,KAAMtD,gBAAgB,CAAC,MAAM,EAAEE,OAAO,CAAE;kBAAA2B,QAAA,eAEjDjG,OAAA,CAAC5B,QAAQ;oBAAA8H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACVrG,OAAA,CAACrC,OAAO;gBAAC8K,KAAK,EAAC,cAAc;gBAAAxC,QAAA,eAC3BjG,OAAA,CAAC9C,UAAU;kBACTiL,IAAI,EAAC,OAAO;kBACZT,OAAO,EAAEA,CAAA,KAAMtD,gBAAgB,CAAC,QAAQ,EAAEE,OAAO,CAAE;kBACnDuC,KAAK,EAAC,SAAS;kBAAAZ,QAAA,eAEfjG,OAAA,CAACtB,UAAU;oBAAAwH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACVrG,OAAA,CAACrC,OAAO;gBAAC8K,KAAK,EAAC,SAAS;gBAAAxC,QAAA,eACtBjG,OAAA,CAAC9C,UAAU;kBACTiL,IAAI,EAAC,OAAO;kBACZT,OAAO,EAAEA,CAAA,KAAMvC,YAAY,CAACb,OAAO,CAACI,cAAc,CAAE;kBACpDmC,KAAK,EAAC,OAAO;kBAAAZ,QAAA,eAEbjG,OAAA,CAAC1B,UAAU;oBAAA4H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA,GAvEC/B,OAAO,CAACI,cAAc;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwE3B,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,EAEhB9F,OAAO,CAACqH,MAAM,KAAK,CAAC,IAAI,CAACnH,OAAO,iBAC/BT,OAAA,CAAC3D,GAAG;MAACqM,SAAS,EAAC,QAAQ;MAACC,EAAE,EAAE,CAAE;MAAA1C,QAAA,gBAC5BjG,OAAA,CAACxD,UAAU;QAACuK,OAAO,EAAC,IAAI;QAACF,KAAK,EAAC,eAAe;QAAAZ,QAAA,EAAC;MAE/C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbrG,OAAA,CAACxD,UAAU;QAACuK,OAAO,EAAC,OAAO;QAACF,KAAK,EAAC,eAAe;QAAAZ,QAAA,EAAC;MAElD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN,eAGDrG,OAAA,CAAC7C,MAAM;MAACyL,IAAI,EAAE/H,UAAW;MAACgI,OAAO,EAAEtE,iBAAkB;MAACuE,QAAQ,EAAC,IAAI;MAACC,SAAS;MAAA9C,QAAA,gBAC3EjG,OAAA,CAAC5C,WAAW;QAAA6I,QAAA,GACThF,UAAU,KAAK,MAAM,IAAI,kBAAkB,EAC3CA,UAAU,KAAK,MAAM,IAAI,kBAAkB,EAC3CA,UAAU,KAAK,QAAQ,IAAI,kBAAkBF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE2D,cAAc,EAAE;MAAA;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpE,CAAC,eACdrG,OAAA,CAAC3C,aAAa;QAAA4I,QAAA,eACZjG,OAAA,CAAC3D,GAAG;UAAC0L,EAAE,EAAE;YAAEiB,EAAE,EAAE;UAAE,CAAE;UAAA/C,QAAA,EAChBhF,UAAU,KAAK,QAAQ,gBACtBjB,OAAA,CAAAE,SAAA;YAAA+F,QAAA,gBACEjG,OAAA,CAACvC,KAAK;cAACqK,QAAQ,EAAC,MAAM;cAACC,EAAE,EAAE;gBAAEzB,EAAE,EAAE;cAAE,CAAE;cAAAL,QAAA,EAAC;YAEtC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRrG,OAAA,CAACzC,SAAS;cACRwL,SAAS;cACTb,KAAK,EAAC,+BAA+B;cACrCe,KAAK,EAAEpH,gBAAiB;cACxBqH,QAAQ,EAAGC,CAAC,IAAKrH,mBAAmB,CAACqH,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cACrDI,MAAM,EAAC,QAAQ;cACfC,WAAW,EAAC,+BAA+B;cAC3CC,UAAU,EAAC,oCAAoC;cAC/CC,SAAS;cACTC,IAAI,EAAE;YAAE;cAAAvD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA,eACF,CAAC,GACDpF,UAAU,KAAK,MAAM,IAAIF,eAAe,gBAC1Cf,OAAA,CAAAE,SAAA;YAAA+F,QAAA,eACEjG,OAAA,CAACnC,IAAI;cAAAoI,QAAA,gBACHjG,OAAA,CAAClC,QAAQ;gBAAAmI,QAAA,eACPjG,OAAA,CAACjC,YAAY;kBACX2L,OAAO,EAAC,gBAAgB;kBACxBC,SAAS,EAAE5I,eAAe,CAAC2D;gBAAe;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC,eACXrG,OAAA,CAAChC,OAAO;gBAAAkI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACXrG,OAAA,CAAClC,QAAQ;gBAAAmI,QAAA,eACPjG,OAAA,CAACjC,YAAY;kBACX2L,OAAO,EAAC,MAAM;kBACdC,SAAS,EAAEjE,mBAAmB,CAAC3E,eAAe,CAACM,YAAY;gBAAE;kBAAA6E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC,eACXrG,OAAA,CAAChC,OAAO;gBAAAkI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACXrG,OAAA,CAAClC,QAAQ;gBAAAmI,QAAA,eACPjG,OAAA,CAACjC,YAAY;kBACX2L,OAAO,EAAC,OAAO;kBACfC,SAAS,eACP3J,OAAA,CAAC/C,IAAI;oBACHiL,KAAK,EAAEnH,eAAe,CAAC0E,KAAM;oBAC7BoB,KAAK,EAAErB,aAAa,CAACzE,eAAe,CAAC0E,KAAK,CAAE;oBAC5C0C,IAAI,EAAC;kBAAO;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBACF;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC,eACXrG,OAAA,CAAChC,OAAO;gBAAAkI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACXrG,OAAA,CAAClC,QAAQ;gBAAAmI,QAAA,eACPjG,OAAA,CAACjC,YAAY;kBACX2L,OAAO,EAAC,aAAa;kBACrBC,SAAS,EAAE5I,eAAe,CAACO,WAAW,IAAI;gBAAsB;kBAAA4E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC,eACXrG,OAAA,CAAChC,OAAO;gBAAAkI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACXrG,OAAA,CAAClC,QAAQ;gBAAAmI,QAAA,eACPjG,OAAA,CAACjC,YAAY;kBACX2L,OAAO,EAAC,aAAU;kBAClBC,SAAS,eACP3J,OAAA,CAAC/C,IAAI;oBACHiL,KAAK,EAAEnH,eAAe,CAACU,QAAQ,IAAI,SAAU;oBAC7CoF,KAAK,EAAEjB,gBAAgB,CAAC7E,eAAe,CAACU,QAAQ,IAAI,SAAS,CAAE;oBAC/D0G,IAAI,EAAC;kBAAO;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBACF;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC,eACXrG,OAAA,CAAChC,OAAO;gBAAAkI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACXrG,OAAA,CAAClC,QAAQ;gBAAAmI,QAAA,eACPjG,OAAA,CAACjC,YAAY;kBACX2L,OAAO,EAAC,cAAc;kBACtBC,SAAS,EAAE5I,eAAe,CAACQ,YAAY,IAAI;gBAAgB;kBAAA2E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC,EACVtF,eAAe,CAACW,kBAAkB,iBACjC1B,OAAA,CAAAE,SAAA;gBAAA+F,QAAA,gBACEjG,OAAA,CAAChC,OAAO;kBAAAkI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACXrG,OAAA,CAAClC,QAAQ;kBAAAmI,QAAA,eACPjG,OAAA,CAACjC,YAAY;oBACX2L,OAAO,EAAC,oBAAoB;oBAC5BC,SAAS,EAAE5I,eAAe,CAACW;kBAAmB;oBAAAwE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CAAC;cAAA,eACX,CACH,eACDrG,OAAA,CAAChC,OAAO;gBAAAkI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACXrG,OAAA,CAAClC,QAAQ;gBAAAmI,QAAA,eACPjG,OAAA,CAACjC,YAAY;kBACX2L,OAAO,EAAC,gBAAgB;kBACxBC,SAAS,EAAE,IAAIvB,IAAI,CAACrH,eAAe,CAACsH,cAAc,CAAC,CAACC,kBAAkB,CAAC,OAAO;gBAAE;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC,EACVtF,eAAe,CAACS,aAAa,iBAC5BxB,OAAA,CAAAE,SAAA;gBAAA+F,QAAA,gBACEjG,OAAA,CAAChC,OAAO;kBAAAkI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACXrG,OAAA,CAAClC,QAAQ;kBAAAmI,QAAA,eACPjG,OAAA,CAACjC,YAAY;oBACX2L,OAAO,EAAC,eAAe;oBACvBC,SAAS,EAAE,IAAIvB,IAAI,CAACrH,eAAe,CAACS,aAAa,CAAC,CAAC8G,kBAAkB,CAAC,OAAO;kBAAE;oBAAApC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CAAC;cAAA,eACX,CACH,eACDrG,OAAA,CAAChC,OAAO;gBAAAkI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACXrG,OAAA,CAAClC,QAAQ;gBAAAmI,QAAA,eACPjG,OAAA,CAACjC,YAAY;kBACX2L,OAAO,EAAC,gBAAgB;kBACxBC,SAAS,EAAE5I,eAAe,CAACwH,qBAAqB,IAAI;gBAAE;kBAAArC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC,eACXrG,OAAA,CAAChC,OAAO;gBAAAkI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACXrG,OAAA,CAAClC,QAAQ;gBAAAmI,QAAA,eACPjG,OAAA,CAACjC,YAAY;kBACX2L,OAAO,EAAC,eAAe;kBACvBC,SAAS,EAAE,GAAG,CAAC5I,eAAe,CAACyH,yBAAyB,IAAI,CAAC,EAAElB,OAAO,CAAC,CAAC,CAAC;gBAAI;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9E;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP;UAAC,gBACP,CAAC,gBAEHrG,OAAA,CAAAE,SAAA;YAAA+F,QAAA,gBACEjG,OAAA,CAACzC,SAAS;cACRwL,SAAS;cACTa,MAAM;cACN1B,KAAK,EAAC,cAAc;cACpBe,KAAK,EAAE9H,QAAQ,CAACE,YAAa;cAC7B6H,QAAQ,EAAGC,CAAC,IAAK/H,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEE,YAAY,EAAE8H,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAC5EI,MAAM,EAAC,QAAQ;cACfxB,QAAQ,EAAE5G,UAAU,KAAK,MAAO;cAAAgF,QAAA,gBAEhCjG,OAAA,CAACxC,QAAQ;gBAACyL,KAAK,EAAC,MAAM;gBAAAhD,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACtCrG,OAAA,CAACxC,QAAQ;gBAACyL,KAAK,EAAC,uBAAuB;gBAAAhD,QAAA,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACxErG,OAAA,CAACxC,QAAQ;gBAACyL,KAAK,EAAC,qBAAqB;gBAAAhD,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACpErG,OAAA,CAACxC,QAAQ;gBAACyL,KAAK,EAAC,gBAAgB;gBAAAhD,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC1DrG,OAAA,CAACxC,QAAQ;gBAACyL,KAAK,EAAC,SAAS;gBAAAhD,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eAEZrG,OAAA,CAACzC,SAAS;cACRwL,SAAS;cACTa,MAAM;cACN1B,KAAK,EAAC,aAAU;cAChBe,KAAK,EAAE9H,QAAQ,CAACM,QAAS;cACzByH,QAAQ,EAAGC,CAAC,IAAK/H,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEM,QAAQ,EAAE0H,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cACxEI,MAAM,EAAC,QAAQ;cACfxB,QAAQ,EAAE5G,UAAU,KAAK,MAAO;cAAAgF,QAAA,gBAEhCjG,OAAA,CAACxC,QAAQ;gBAACyL,KAAK,EAAC,OAAO;gBAAAhD,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACxCrG,OAAA,CAACxC,QAAQ;gBAACyL,KAAK,EAAC,SAAS;gBAAAhD,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC5CrG,OAAA,CAACxC,QAAQ;gBAACyL,KAAK,EAAC,MAAM;gBAAAhD,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACtCrG,OAAA,CAACxC,QAAQ;gBAACyL,KAAK,EAAC,SAAS;gBAAAhD,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eAEZrG,OAAA,CAACzC,SAAS;cACRwL,SAAS;cACTb,KAAK,EAAC,aAAa;cACnBe,KAAK,EAAE9H,QAAQ,CAACG,WAAY;cAC5B4H,QAAQ,EAAGC,CAAC,IAAK/H,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEG,WAAW,EAAE6H,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAC3EI,MAAM,EAAC,QAAQ;cACfG,SAAS;cACTC,IAAI,EAAE,CAAE;cACR5B,QAAQ,EAAE5G,UAAU,KAAK;YAAO;cAAAiF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,eAEFrG,OAAA,CAACzC,SAAS;cACRwL,SAAS;cACTb,KAAK,EAAC,cAAc;cACpBe,KAAK,EAAE9H,QAAQ,CAACI,YAAa;cAC7B2H,QAAQ,EAAGC,CAAC,IAAK/H,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEI,YAAY,EAAE4H,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAC5EI,MAAM,EAAC,QAAQ;cACfxB,QAAQ,EAAE5G,UAAU,KAAK,MAAO;cAChC4I,QAAQ;cACRN,UAAU,EAAC;YAAuC;cAAArD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,eAEFrG,OAAA,CAACzC,SAAS;cACRwL,SAAS;cACTb,KAAK,EAAC,oBAAoB;cAC1Be,KAAK,EAAE9H,QAAQ,CAACO,kBAAmB;cACnCwH,QAAQ,EAAGC,CAAC,IAAK/H,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEO,kBAAkB,EAAEyH,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAClFI,MAAM,EAAC,QAAQ;cACfG,SAAS;cACTC,IAAI,EAAE,CAAE;cACR5B,QAAQ,EAAE5G,UAAU,KAAK,MAAO;cAChCsI,UAAU,EAAC;YAA2C;cAAArD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC,eAEFrG,OAAA,CAACzC,SAAS;cACRwL,SAAS;cACTb,KAAK,EAAC,eAAe;cACrB4B,IAAI,EAAC,MAAM;cACXb,KAAK,EAAE9H,QAAQ,CAACK,aAAc;cAC9B0H,QAAQ,EAAGC,CAAC,IAAK/H,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEK,aAAa,EAAE2H,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAC7EI,MAAM,EAAC,QAAQ;cACfU,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK,CAAE;cAClCnC,QAAQ,EAAE5G,UAAU,KAAK;YAAO;cAAAiF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC;UAAA,eACF;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChBrG,OAAA,CAAC1C,aAAa;QAAA2I,QAAA,gBACZjG,OAAA,CAACvD,MAAM;UAACiL,OAAO,EAAEnD,iBAAkB;UAAA0B,QAAA,EAChChF,UAAU,KAAK,MAAM,GAAG,QAAQ,GAAG;QAAS;UAAAiF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,EACRpF,UAAU,KAAK,MAAM,iBACpBjB,OAAA,CAACvD,MAAM;UAACiL,OAAO,EAAElD,YAAa;UAACuC,OAAO,EAAC,WAAW;UAAAd,QAAA,EAC/ChF,UAAU,KAAK,MAAM,GAAG,OAAO,GAC/BA,UAAU,KAAK,QAAQ,GAAG,cAAc,GAAG;QAAO;UAAAiF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTrG,OAAA,CAACH,kBAAkB;MACjBO,UAAU,EAAEA,UAAW;MACvBwI,IAAI,EAAE7G,eAAgB;MACtB8G,OAAO,EAAEA,CAAA,KAAM7G,kBAAkB,CAAC,KAAK,CAAE;MACzCiI,SAAS,EAAEA,CAAA,KAAM;QACf7G,WAAW,CAAC,CAAC;QACbC,eAAe,CAAC,CAAC;QACjBrB,kBAAkB,CAAC,KAAK,CAAC;MAC3B;IAAE;MAAAkE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGFrG,OAAA,CAACkK,oBAAoB;MACnBtB,IAAI,EAAEuB,wBAAyB;MAC/BtB,OAAO,EAAEA,CAAA,KAAMlB,2BAA2B,CAAC,KAAK,CAAE;MAClDvH,UAAU,EAAEA;IAAW;MAAA8F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC/F,EAAA,CA/rBIH,WAAW;AAAAiK,EAAA,GAAXjK,WAAW;AAisBjB,eAAeA,WAAW;AAAC,IAAAiK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}