import React, { useState, useEffect } from 'react';
import {
  Box,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Typography,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  DialogContentText
} from '@mui/material';
import { Cancel as CancelIcon } from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import caviService from '../../services/caviService';
import { redirectToVisualizzaCavi } from '../../utils/navigationUtils';
import CavoForm from './CavoForm';

/**
 * Componente per la selezione e modifica di un cavo
 *
 * @param {Object} props - Proprietà del componente
 * @param {string} props.cantiereId - ID del cantiere
 * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione
 * @param {Function} props.onError - Funzione chiamata in caso di errore
 * @param {boolean} props.isDialog - Indica se il componente è in un dialog
 * @param {Function} props.onCancel - Funzione chiamata all'annullamento dell'operazione
 */
const SelezionaCavoForm = ({ cantiereId, onSuccess, onError, isDialog = false, onCancel }) => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [caviLoading, setCaviLoading] = useState(true);
  const [cavi, setCavi] = useState([]);
  const [selectedCavoId, setSelectedCavoId] = useState('');
  const [cavoSelectionStep, setCavoSelectionStep] = useState(true);
  const [redirectDialog, setRedirectDialog] = useState(false);
  const [selectedCavoData, setSelectedCavoData] = useState(null);

  // Carica la lista dei cavi all'avvio
  useEffect(() => {
    const loadCavi = async () => {
      try {
        setCaviLoading(true);
        const caviData = await caviService.getCavi(cantiereId);
        // Filtra solo i cavi idonei per POSA:
        // 1. metratura_reale = 0 (non ancora posati)
        // 2. stato != Installato
        // 3. metri_teorici > 0 (devono avere metri inseriti)
        const caviNonPosati = caviData.filter(cavo => {
          const metriReali = parseFloat(cavo.metratura_reale) || 0;
          const metriTeorici = parseFloat(cavo.metri_teorici) || 0;

          return metriReali === 0 &&
                 cavo.stato_installazione !== 'Installato' &&
                 metriTeorici > 0;
        });
        setCavi(caviNonPosati);
      } catch (error) {
        console.error('Errore nel caricamento dei cavi:', error);
        onError('Errore nel caricamento dei cavi');
      } finally {
        setCaviLoading(false);
      }
    };

    loadCavi();
  }, [cantiereId, onError]);

  // Gestisce la selezione di un cavo
  const handleCavoSelection = async () => {
    if (!selectedCavoId) {
      onError('Seleziona un cavo da modificare');
      return;
    }

    try {
      setLoading(true);
      const cavoData = await caviService.getCavoById(cantiereId, selectedCavoId);

      // Verifica se il cavo è già posato
      if (parseFloat(cavoData.metratura_reale) > 0 || cavoData.stato_installazione === 'Installato') {
        // Mostra dialog per reindirizzare a modifica_bobina_cavo_posato
        setRedirectDialog(true);
        return;
      }

      // Imposta i dati del cavo nel form
      setSelectedCavoData(cavoData);
      setCavoSelectionStep(false);
    } catch (error) {
      console.error('Errore nel caricamento dei dettagli del cavo:', error);
      onError('Errore nel caricamento dei dettagli del cavo');
    } finally {
      setLoading(false);
    }
  };

  // Gestisce l'annullamento dell'operazione
  const handleCancel = () => {
    if (isDialog) {
      // Se è in un dialog, chiama la funzione onCancel passata come prop
      if (onCancel) {
        onCancel();
      }
      return;
    }
    // Reindirizza alla visualizzazione dei cavi
    redirectToVisualizzaCavi(navigate);
  };

  // Gestisce il reindirizzamento a modifica_bobina_cavo_posato
  const handleRedirect = () => {
    setRedirectDialog(false);
    navigate(`/dashboard/cantieri/${cantiereId}/cavi/posa/modifica-bobina`);
  };

  // Gestisce il ritorno alla selezione del cavo
  const handleBackToSelection = () => {
    setCavoSelectionStep(true);
    setSelectedCavoData(null);
  };

  // Renderizza il form di selezione del cavo
  if (cavoSelectionStep) {
    return (
      <Box>
        <Paper sx={{ p: isDialog ? 1.5 : 3, mb: isDialog ? 0.75 : 2, boxShadow: isDialog ? 0 : 1 }}>
          <Typography variant="h6" sx={{ fontSize: isDialog ? '0.9rem' : '1.25rem', mb: isDialog ? 1 : 1.5, mt: isDialog ? 0.5 : 1, fontWeight: 'bold' }}>
            Seleziona un cavo da modificare
          </Typography>
          <Typography variant="body2" color="textSecondary" paragraph>
            Puoi modificare solo i cavi non ancora posati. Se il cavo è già posato, verrai reindirizzato alla funzione di modifica bobina.
          </Typography>

          {caviLoading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            <>
              {cavi.length === 0 ? (
                <Alert severity="info" sx={{ mt: 2 }}>
                  Non ci sono cavi non posati disponibili per la modifica.
                </Alert>
              ) : (
                <Grid container spacing={isDialog ? 1.5 : 3} sx={{ mt: 1 }}>
                  <Grid item xs={12}>
                    <FormControl fullWidth>
                      <InputLabel id="cavo-select-label" style={{ marginBottom: '8px' }}>Seleziona Cavo</InputLabel>
                      <Select
                        labelId="cavo-select-label"
                        id="cavo-select"
                        value={selectedCavoId}
                        label="Seleziona Cavo"
                        onChange={(e) => setSelectedCavoId(e.target.value)}
                        sx={{ mb: 1 }}
                      >
                        {cavi.map((cavo) => (
                          <MenuItem key={cavo.id_cavo} value={cavo.id_cavo}>
                            {cavo.id_cavo} - {cavo.sistema || 'N/A'} - {cavo.utility || 'N/A'}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                    <Button
                      variant="contained"
                      color="primary"
                      onClick={handleCavoSelection}
                      disabled={!selectedCavoId || loading}
                    >
                      {loading ? <CircularProgress size={24} /> : 'Seleziona'}
                    </Button>
                  </Grid>
                </Grid>
              )}
            </>
          )}
        </Paper>

        {/* Dialog per reindirizzamento */}
        <Dialog
          open={redirectDialog}
          onClose={() => setRedirectDialog(false)}
        >
          <DialogTitle>Cavo già posato</DialogTitle>
          <DialogContent>
            <DialogContentText>
              Il cavo selezionato è già stato posato. Verrai reindirizzato alla funzione di modifica bobina per cavi posati.
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setRedirectDialog(false)} color="secondary">
              Annulla
            </Button>
            <Button onClick={handleRedirect} color="primary" autoFocus>
              Vai a Modifica Bobina
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    );
  }

  // Renderizza il form di modifica del cavo utilizzando CavoForm
  return (
    <Box>
      {selectedCavoData && (
        <CavoForm
          mode="edit"
          initialData={selectedCavoData}
          cantiereId={cantiereId}
          onSubmit={async (validatedData) => {
            try {
              // Rimuovi i campi di sistema che non devono essere modificati
              const dataToSend = { ...validatedData };
              delete dataToSend.id_bobina; // Rimuovi id_bobina perché è un campo di sistema
              delete dataToSend.metratura_reale; // Rimuovi metratura_reale perché è un campo di sistema
              delete dataToSend.modificato_manualmente; // Rimuovi modificato_manualmente perché è un campo di sistema
              delete dataToSend.timestamp; // Rimuovi timestamp perché è un campo di sistema
              delete dataToSend.stato_installazione; // Rimuovi stato_installazione perché è un campo di sistema per cavi non posati

              // Imposta modificato_manualmente a 1 per indicare che il cavo è stato modificato manualmente
              dataToSend.modificato_manualmente = 1;

              await caviService.updateCavo(cantiereId, dataToSend.id_cavo, dataToSend);
              return true;
            } catch (error) {
              throw error;
            }
          }}
          onSuccess={(message) => {
            onSuccess(message);
            if (isDialog) {
              if (onCancel) {
                onCancel();
              }
            } else {
              redirectToVisualizzaCavi(navigate);
            }
          }}
          onError={onError}
          isDialog={isDialog}
        />
      )}
    </Box>
  );
};

export default SelezionaCavoForm;
