#!/usr/bin/env python3
"""
Test di sicurezza per verificare che la normalizzazione NON alteri i dati critici.
Questo test verifica che la normalizzazione sia conservativa e non introduca allucinazioni.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'webapp'))

from webapp.backend.utils.cable_normalizer import CableNormalizer

def test_section_preservation():
    """Test che verifica la preservazione delle sezioni complesse"""
    
    normalizer = CableNormalizer()
    
    # Sezioni complesse che NON devono essere alterate sostanzialmente
    complex_sections = [
        "3X2.5MM2+2X1.5YG",  # Sezione con conduttore di terra
        "4X1.5+SH",          # Sezione con schermo
        "1X240AL",           # Sezione in alluminio
        "FG16OR16",          # Fibra ottica
        "2X0.75+DRAIN",      # Con drain
        "3X1.5+PE",          # Con protezione
        "4X2.5+2X1.5",       # Sezione mista
        "1X50+16CU",         # Sezione con materiale
        "CUSTOM_SECTION_123", # Sezione personalizzata
        "3x2,5mm2+sh",       # Lowercase con virgola
    ]
    
    print("=== TEST PRESERVAZIONE SEZIONI COMPLESSE ===")
    
    for section in complex_sections:
        normalized = normalizer.normalize_section(section)
        
        # Verifica che la normalizzazione sia conservativa
        original_numbers = extract_numbers(section)
        normalized_numbers = extract_numbers(normalized)
        
        print(f"Originale: '{section}' -> Normalizzato: '{normalized}'")
        
        # Verifica che i numeri non siano cambiati
        if original_numbers != normalized_numbers:
            print(f"  ❌ ERRORE: Numeri alterati! {original_numbers} -> {normalized_numbers}")
        else:
            print(f"  ✅ Numeri preservati: {original_numbers}")
        
        # Verifica che la lunghezza non sia drasticamente cambiata
        if len(normalized) > len(section) * 1.5:
            print(f"  ⚠️  ATTENZIONE: Lunghezza aumentata significativamente")
        
        print()

def extract_numbers(text):
    """Estrae tutti i numeri da una stringa per verificare che non siano alterati"""
    import re
    numbers = re.findall(r'\d+(?:[.,]\d+)?', text)
    return [n.replace(',', '.') for n in numbers]

def test_tipologia_preservation():
    """Test che verifica la preservazione delle tipologie"""
    
    normalizer = CableNormalizer()
    
    # Tipologie che NON devono essere alterate sostanzialmente
    tipologie = [
        "FG16OR16",
        "H07RNF",
        "LIYCY",
        "CUSTOM_CABLE_TYPE",
        "N07G9K",
        "UTP_CAT6",
        "SPECIAL_FIBER_123"
    ]
    
    print("=== TEST PRESERVAZIONE TIPOLOGIE ===")
    
    for tipologia in tipologie:
        normalized = normalizer.normalize_tipologia(tipologia)
        
        print(f"Originale: '{tipologia}' -> Normalizzato: '{normalized}'")
        
        # Verifica che non sia stata completamente cambiata
        if normalized.upper() != tipologia.upper().strip():
            print(f"  ⚠️  ATTENZIONE: Tipologia modificata oltre la normalizzazione case/spazi")
        else:
            print(f"  ✅ Tipologia preservata (solo case/spazi)")
        
        print()

def test_complete_cable_data():
    """Test completo su dati di cavo reali"""
    
    normalizer = CableNormalizer()
    
    # Dati di cavo complessi che devono essere preservati
    cable_data = {
        'id_cavo': 'CAVO_SPECIALE_001',
        'tipologia': 'FG16OR16_CUSTOM',
        'sezione': '3X2.5MM2+2X1.5YG+DRAIN',
        'utility': 'CUSTOM_UTILITY',
        'colore_cavo': 'NERO_SPECIALE',
        'sistema': 'SISTEMA_PARTICOLARE',
        'ubicazione_partenza': 'QUADRO_PRINCIPALE_A1',
        'ubicazione_arrivo': 'SOTTOQUADRO_B2_PIANO_3',
        'metri_teorici': 150.5,
        'n_conduttori': 5,
        'descrizione_utenza_partenza': 'Descrizione molto dettagliata con caratteri speciali: àèìòù & simboli',
    }
    
    print("=== TEST DATI CAVO COMPLETI ===")
    print(f"Dati originali:")
    for key, value in cable_data.items():
        print(f"  {key}: '{value}'")
    
    print("\nNormalizzazione...")
    normalized_data = normalizer.normalize_all_cable_fields(cable_data)
    
    print(f"\nDati normalizzati:")
    for key, value in normalized_data.items():
        print(f"  {key}: '{value}'")
    
    print("\n=== ANALISI MODIFICHE ===")
    for key in cable_data.keys():
        original = cable_data[key]
        normalized = normalized_data[key]
        
        if str(original) != str(normalized):
            print(f"  {key}: '{original}' -> '{normalized}'")
            
            # Verifica se la modifica è solo case/spazi (accettabile)
            if isinstance(original, str) and isinstance(normalized, str):
                if original.upper().replace(' ', '') == normalized.upper().replace(' ', ''):
                    print(f"    ✅ Solo normalizzazione case/spazi")
                else:
                    print(f"    ❌ MODIFICA SOSTANZIALE!")
        else:
            print(f"  {key}: ✅ Non modificato")

def main():
    """Esegue tutti i test di sicurezza"""
    print("🔍 TEST DI SICUREZZA NORMALIZZAZIONE CAVI")
    print("=" * 60)
    print("Verifica che la normalizzazione NON alteri i dati sostanzialmente")
    print()
    
    test_section_preservation()
    test_tipologia_preservation()
    test_complete_cable_data()
    
    print("\n" + "=" * 60)
    print("✅ Test completati. Verifica i risultati sopra per identificare eventuali problemi.")

if __name__ == "__main__":
    main()
